# Pull Request

## Summary

<!-- Describe the change you're submitting and its purpose. Be concise but clear. -->

## Related Issue

<!-- If applicable, reference the issue number with `#123` format. -->
<!-- Do not use closing keywords when the referenced issue is not fully resolved by this PR. -->

Resolves #

## Checklist

Please ensure the following before submitting your PR:

- [ ] The PR branch name starts with the related issue ID (e.g., `fix/123-*`).
- [ ] My commit history follows [semantic commit conventions](https://www.conventionalcommits.org/).
- [ ] Code has been formatted using `.editorconfig` and `dotnet format`.
- [ ] I have added or updated tests where applicable.
- [ ] All tests pass locally (`dotnet test`).
- [ ] The PR targets the `main` branch.
- [ ] I have reviewed the project's [CONTRIBUTING.md](../../CONTRIBUTING.md).

<!-- You can remove this section once you have completed the checklist. -->

## Additional Notes

<!-- Include any other context, dependencies, screenshots, or notes relevant to the PR. -->
