name: Self-hosted Deploy

permissions:
  packages: write # for publishing packages in repository registry

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      image_ref:
        description: "Container image reference (tag) that will be deployed"
        required: true
        type: string
      environment_name:
        description: "ASPNETCORE_ENVIRONMENT"
        required: true
        type: string
      container_label:
        description: "Container label to identify existing containers"
        required: true
        type: string
      container_port:
        description: "Container port"
        required: false
        type: number
        default: 12345
      container_startup_timeout:
        description: "Container startup timeout"
        required: false
        type: number
        default: 60
      debug:
        required: false
        type: boolean
        default: false

jobs:
  deploy:
    name: Deploy ${{ inputs.image_ref }} to ${{ inputs.environment_name }}
    runs-on: [ self-hosted, daedalus ]
    env:
      IS_DEVELOPMENT: ${{ inputs.environment_name != 'Production' && 'true' || 'false' }}
      REGISTRY_NAME: ghcr.io
      REGISTRY_USER: ${{ github.actor }}
      REGISTRY_PASSWORD: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Deploy the Docker image
        run: |
          TIMEOUT=${{ inputs.container_startup_timeout }}
          PORT=${{ inputs.container_port }}
          IMAGE_NAME=$(echo "${REGISTRY_NAME}/${{ github.repository }}" | tr '[:upper:]' '[:lower:]')

          echo "Setting up Docker login to ${REGISTRY_NAME} using ${REGISTRY_USER}"
          docker login -p ${REGISTRY_PASSWORD} -u ${REGISTRY_USER} ${REGISTRY_NAME}

          echo "Looking for any old containers..."
          CONTAINER_ID=$(docker ps -aqf "label=${{ inputs.container_label }}")
          if [ -n "${CONTAINER_ID}" ]; then
            echo "Stopping old containers: ${CONTAINER_ID}"
            docker stop ${CONTAINER_ID} || true
            docker rm ${CONTAINER_ID} || true
          fi

          echo "Starting container..."
          CONTAINER_ID=$(
            docker run \
            --detach \
            --label ${{ inputs.container_label }} \
            --publish ${PORT}:8080 \
            --env ASPNETCORE_URLS=http://0.0.0.0:8080 \
            --env ASPNETCORE_ENVIRONMENT=${{ inputs.environment_name }} \
            --env DetailedErrors=${IS_DEVELOPMENT} \
            --volume ${{ inputs.container_label }}-data:/app/ArkanisOverlay \
            --volume /etc/timezone:/etc/timezone:ro \
            --volume /etc/localtime:/etc/localtime:ro \
            "${IMAGE_NAME}:${{ inputs.image_ref }}"
          )

          # truncate ID for later filtering, as docker uses 12 character IDs
          CONTAINER_ID=$(head -c 12 <<<${CONTAINER_ID})
          echo "New container: ${CONTAINER_ID}"

          SECONDS=0 # this is a bash magic variable that keeps counting up automatically
          until [ "$(docker inspect -f '{{.State.Health.Status}}' "${CONTAINER_ID}")" = "healthy" ]; do
            echo "Current container state: $(docker inspect -f '{{json .State}}' "${CONTAINER_ID}")"

            if [ $SECONDS -gt $TIMEOUT ]; then
              echo "Container failed to become healthy!"
              exit 1
            fi

            echo "Waiting for container to become healthy... (${SECONDS}s)"
            sleep 5
          done

          # only let container restart automatically after it has proven once
          # that it is a good boy and will behave
          echo "Container started successfully!"
          docker update --restart unless-stopped "${CONTAINER_ID}"

          echo "Removing dangling images..."
          docker image prune -f -a

          echo "Logging out of Docker registry ${REGISTRY_NAME}"
          docker logout ${REGISTRY_NAME}

          echo ""
          echo "Done!"
          echo ""
