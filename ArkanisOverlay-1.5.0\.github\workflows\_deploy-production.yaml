name: Deploy (Production) pipeline

concurrency:
  group: deploy-production
  cancel-in-progress: true

permissions:
  contents: read
  packages: write

on:
  workflow_dispatch:
    inputs:
      image_ref:
        description: "Container image reference (tag) that will be deployed"
        type: string
        required: true
  workflow_call:
    inputs:
      image_ref:
        type: string
        required: true

jobs:
  deploy-production:
    name: Production
    uses: ./.github/workflows/_deploy-docker-self-hosted.yaml
    with:
      image_ref: ${{ inputs.image_ref }}
      container_label: arkanis-overlay
      container_port: 12345
      environment_name: Production
