# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: .NET Test

on:
  workflow_call:

env:
  # https://github.com/actions/setup-dotnet?tab=readme-ov-file#reduce-caching-size
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  test:
    runs-on: windows-latest
    steps:
      -   name: Git checkout
          uses: actions/checkout@v4

      -   name: Setup .NET
          id: setup-dotnet
          uses: actions/setup-dotnet@v4
          with:
            dotnet-version: 8.0.x
            cache: true
            cache-dependency-path: "**/packages.lock.json"

      -   run: echo "Using .NET ${{ steps.setup-dotnet.outputs.dotnet-version }}"

      -   run: echo "Cache hit - ${{ steps.setup-dotnet.outputs.cache-hit }}"

      -   name: Restore dependencies
          run: dotnet restore --locked-mode

      -   name: Build solution
          run: dotnet build --no-restore

      -   name: Test solution
          run: dotnet test --no-build --verbosity normal
