name: .NET build

on:
  # You can use the GitHub API to trigger a webhook event called repository_dispatch
  # when you want to trigger a workflow for activity that happens outside of GitHub.
  repository_dispatch:
    branches: [ main, release/*, ci ]
  push:
    branches: [ main, release/*, ci ]
  pull_request:
    branches: [ main, release/* ]

permissions:
  contents: write       # for publishing a GitHub release and creating release backpropagation PRs
  issues: write         # for commenting on released issues
  pull-requests: write  # for commenting on released pull requests and creating release backpropagation PRs
  packages: write       # for publishing packages in repository registry

env:
  # https://github.com/actions/setup-dotnet?tab=readme-ov-file#reduce-caching-size
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  test:
    name: Tests
    uses: ./.github/workflows/_test.yaml

  release:
    name: Release
    needs: test
    uses: ./.github/workflows/_release.yaml
    secrets: inherit
    with:
      dry_run: ${{ github.event_name == 'pull_request' || github.ref == 'refs/heads/ci' }}

  deploy-production:
    name: Deployment
    needs: release
    if: ${{ github.ref == 'refs/heads/release/stable' && needs.release.outputs.new_tag != '' }}
    uses: ./.github/workflows/_deploy-production.yaml
    with:
      image_ref: ${{ needs.release.outputs.new_tag }}
