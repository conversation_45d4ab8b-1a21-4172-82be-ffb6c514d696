{"branches": [{"name": "release/stable", "channel": "stable"}, {"name": "main", "channel": "nightly", "prerelease": "nightly"}, {"name": "release/rc", "channel": "rc", "prerelease": "rc"}, {"name": "release/beta", "channel": "beta", "prerelease": "beta"}, {"name": "release/alpha", "channel": "alpha", "prerelease": "alpha"}, {"name": "ci", "channel": "ci", "prerelease": "ci-do-not-use"}], "repositoryUrl": "https://github.com/ArkanisCorporation/ArkanisOverlay", "tagFormat": "v${version}", "debug": false, "plugins": ["@semantic-release/commit-analyzer", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], "@semantic-release/release-notes-generator", "@semantic-release/github", ["@semantic-release/exec", {"verifyReleaseCmd": "VERSION=${nextRelease.version} VERSION_TAG=${nextRelease.gitTag} VERSION_CHANNEL=${nextRelease.channel} ./scripts/release-10-verify.sh", "prepareCmd": "VERSION=${nextRelease.version} VERSION_TAG=${nextRelease.gitTag} VERSION_CHANNEL=${nextRelease.channel} ./scripts/release-20-prepare.sh", "publishCmd": "VERSION=${nextRelease.version} VERSION_TAG=${nextRelease.gitTag} VERSION_CHANNEL=${nextRelease.channel} ./scripts/release-30-publish.sh"}]]}