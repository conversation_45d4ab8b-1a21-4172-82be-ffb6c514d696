<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Arkanis.Overlay.Host.Desktop" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/artifacts/bin/Arkanis.Overlay.Host.Desktop/debug/ArkanisOverlay.exe" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src/Arkanis.Overlay.Host.Desktop" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="DOTNET_ENVIRONMENT" value="Development" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="1" />
    <option name="ENV_FILE_PATHS" value="" />
    <option name="REDIRECT_INPUT_PATH" value="" />
    <option name="PTY_MODE" value="Auto" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="AUTO_ATTACH_CHILDREN" value="0" />
    <option name="MIXED_MODE_DEBUG" value="0" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/src/Arkanis.Overlay.Host.Desktop/Arkanis.Overlay.Host.Desktop.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="0" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0-windows10.0.17763.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>