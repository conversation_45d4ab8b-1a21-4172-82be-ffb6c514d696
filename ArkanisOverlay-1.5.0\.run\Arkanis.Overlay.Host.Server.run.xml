<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Arkanis.Overlay.Host.Server" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/src/Arkanis.Overlay.Host.Server/Arkanis.Overlay.Host.Server.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
    <option name="LAUNCH_PROFILE_NAME" value="http" />
    <option name="USE_EXTERNAL_CONSOLE" value="1" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
  <configuration default="false" name="Arkanis.Overlay.Host.Server" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/artifacts/bin/Arkanis.Overlay.Host.Server/debug/Arkanis.Overlay.Host.Server.exe" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src/Arkanis.Overlay.Host.Server" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="DOTNET_ENVIRONMENT" value="Development" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="1" />
    <option name="ENV_FILE_PATHS" value="" />
    <option name="REDIRECT_INPUT_PATH" value="" />
    <option name="PTY_MODE" value="Auto" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="AUTO_ATTACH_CHILDREN" value="0" />
    <option name="MIXED_MODE_DEBUG" value="0" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/src/Arkanis.Overlay.Host.Server/Arkanis.Overlay.Host.Server.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>