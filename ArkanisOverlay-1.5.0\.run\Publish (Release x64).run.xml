<component name="ProjectRunConfigurationManager">
    <configuration default="false" name="Publish (Release x64)" type="DotNetFolderPublish"
                   factoryName="Publish to folder" focusToolWindowBeforeRun="true">
        <riderPublish configuration="Release" delete_existing_files="true" include_all_content_for_self_extract="true"
                      include_native_libs_for_self_extract="true" platform="Any CPU" produce_single_file="true"
                      ready_to_run="true" runtime="win-x64" target_folder="$PROJECT_DIR$/publish"
                      target_framework="net8.0-windows" uuid_high="-1807100433525749134"
                      uuid_low="-5969118356554441947"/>
        <method v="2"/>
    </configuration>
    <configuration default="false" name="Publish (Release x64)" type="DotNetFolderPublish"
                   factoryName="Publish to folder" focusToolWindowBeforeRun="true">
        <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" ready_to_run="true"
                      runtime="win-x64" target_folder="$PROJECT_DIR$/publish"
                      target_framework="net8.0-windows10.0.17763.0" uuid_high="-1807100433525749134"
                      uuid_low="-5969118356554441947"/>
        <method v="2"/>
    </configuration>
</component>
