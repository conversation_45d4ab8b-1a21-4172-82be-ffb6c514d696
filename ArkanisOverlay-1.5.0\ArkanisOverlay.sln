
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "root", "root", "{BAB8C1A2-5814-45A1-BC19-8C3661D7E566}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		CODE_OF_CONDUCT.md = CODE_OF_CONDUCT.md
		CONTRIBUTING.md = CONTRIBUTING.md
		DEVELOPMENT.md = DEVELOPMENT.md
		LICENSE.md = LICENSE.md
		README.md = README.md
		SUPPORT.md = SUPPORT.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{616E9EA0-1A7F-4608-BEEA-9C3F26FAF516}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Host.Desktop", "src\Arkanis.Overlay.Host.Desktop\Arkanis.Overlay.Host.Desktop.csproj", "{E6EBE3BA-EA3E-4A72-AD29-6E7AFF4F1B25}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.External.UEX", "src\Arkanis.Overlay.External.UEX\Arkanis.Overlay.External.UEX.csproj", "{16AB4036-03A1-466D-83F0-56108C3E744F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Common", "src\Arkanis.Overlay.Common\Arkanis.Overlay.Common.csproj", "{CED78CB3-F5FA-49D6-ACBC-565E986BC665}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Domain", "src\Arkanis.Overlay.Domain\Arkanis.Overlay.Domain.csproj", "{6BD8A5F9-6D9E-4C13-86A2-1C9C7A170D04}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Components", "src\Arkanis.Overlay.Components\Arkanis.Overlay.Components.csproj", "{B5EC96A2-1FBF-440B-A0E0-7E33925015F8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Host.Server", "src\Arkanis.Overlay.Host.Server\Arkanis.Overlay.Host.Server.csproj", "{AB77121D-856C-4A53-B964-3BEE7BC1D8FC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Infrastructure", "src\Arkanis.Overlay.Infrastructure\Arkanis.Overlay.Infrastructure.csproj", "{6E475819-2732-4C87-9F55-CA8777332642}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Apps", "Apps", "{9AC34A40-36D7-4D0C-A667-508A386B76D5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{8BB043D2-BDE7-461C-8D9C-5184A0A0C185}"
	ProjectSection(SolutionItems) = preProject
		tests\README.md = tests\README.md
		tests\Directory.Build.props = tests\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Infrastructure.UnitTests", "tests\Arkanis.Overlay.Infrastructure.UnitTests\Arkanis.Overlay.Infrastructure.UnitTests.csproj", "{06F00A11-3606-43F2-B6F5-907586C606B8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "configs", "configs", "{811538FC-7A46-4712-915E-3BDA29E04C67}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.gitattributes = .gitattributes
		.gitignore = .gitignore
		.releaserc = .releaserc
		Directory.Build.props = Directory.Build.props
		global.json = global.json
		package.json = package.json
		Directory.Build.targets = Directory.Build.targets
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Host.Server.UnitTests", "tests\Arkanis.Overlay.Host.Server.UnitTests\Arkanis.Overlay.Host.Server.UnitTests.csproj", "{6CC132FD-8633-46B0-B321-61067B544A75}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Common.UnitTests", "tests\Arkanis.Overlay.Common.UnitTests\Arkanis.Overlay.Common.UnitTests.csproj", "{E044AC17-E772-4DA6-A65D-ED5FADECF4FD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Arkanis.Overlay.Host.Desktop.UnitTests", "tests\Arkanis.Overlay.Host.Desktop.UnitTests\Arkanis.Overlay.Host.Desktop.UnitTests.csproj", "{81215169-EF34-4378-9DB6-85D62D481DEF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E6EBE3BA-EA3E-4A72-AD29-6E7AFF4F1B25}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E6EBE3BA-EA3E-4A72-AD29-6E7AFF4F1B25}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E6EBE3BA-EA3E-4A72-AD29-6E7AFF4F1B25}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E6EBE3BA-EA3E-4A72-AD29-6E7AFF4F1B25}.Release|Any CPU.Build.0 = Release|Any CPU
		{16AB4036-03A1-466D-83F0-56108C3E744F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16AB4036-03A1-466D-83F0-56108C3E744F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16AB4036-03A1-466D-83F0-56108C3E744F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16AB4036-03A1-466D-83F0-56108C3E744F}.Release|Any CPU.Build.0 = Release|Any CPU
		{CED78CB3-F5FA-49D6-ACBC-565E986BC665}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CED78CB3-F5FA-49D6-ACBC-565E986BC665}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CED78CB3-F5FA-49D6-ACBC-565E986BC665}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CED78CB3-F5FA-49D6-ACBC-565E986BC665}.Release|Any CPU.Build.0 = Release|Any CPU
		{6BD8A5F9-6D9E-4C13-86A2-1C9C7A170D04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6BD8A5F9-6D9E-4C13-86A2-1C9C7A170D04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6BD8A5F9-6D9E-4C13-86A2-1C9C7A170D04}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6BD8A5F9-6D9E-4C13-86A2-1C9C7A170D04}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5EC96A2-1FBF-440B-A0E0-7E33925015F8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5EC96A2-1FBF-440B-A0E0-7E33925015F8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5EC96A2-1FBF-440B-A0E0-7E33925015F8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5EC96A2-1FBF-440B-A0E0-7E33925015F8}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB77121D-856C-4A53-B964-3BEE7BC1D8FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB77121D-856C-4A53-B964-3BEE7BC1D8FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB77121D-856C-4A53-B964-3BEE7BC1D8FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB77121D-856C-4A53-B964-3BEE7BC1D8FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E475819-2732-4C87-9F55-CA8777332642}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E475819-2732-4C87-9F55-CA8777332642}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E475819-2732-4C87-9F55-CA8777332642}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E475819-2732-4C87-9F55-CA8777332642}.Release|Any CPU.Build.0 = Release|Any CPU
		{06F00A11-3606-43F2-B6F5-907586C606B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06F00A11-3606-43F2-B6F5-907586C606B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06F00A11-3606-43F2-B6F5-907586C606B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06F00A11-3606-43F2-B6F5-907586C606B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CC132FD-8633-46B0-B321-61067B544A75}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CC132FD-8633-46B0-B321-61067B544A75}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CC132FD-8633-46B0-B321-61067B544A75}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CC132FD-8633-46B0-B321-61067B544A75}.Release|Any CPU.Build.0 = Release|Any CPU
		{E044AC17-E772-4DA6-A65D-ED5FADECF4FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E044AC17-E772-4DA6-A65D-ED5FADECF4FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E044AC17-E772-4DA6-A65D-ED5FADECF4FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E044AC17-E772-4DA6-A65D-ED5FADECF4FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{81215169-EF34-4378-9DB6-85D62D481DEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81215169-EF34-4378-9DB6-85D62D481DEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81215169-EF34-4378-9DB6-85D62D481DEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81215169-EF34-4378-9DB6-85D62D481DEF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{16AB4036-03A1-466D-83F0-56108C3E744F} = {616E9EA0-1A7F-4608-BEEA-9C3F26FAF516}
		{CED78CB3-F5FA-49D6-ACBC-565E986BC665} = {616E9EA0-1A7F-4608-BEEA-9C3F26FAF516}
		{6BD8A5F9-6D9E-4C13-86A2-1C9C7A170D04} = {616E9EA0-1A7F-4608-BEEA-9C3F26FAF516}
		{B5EC96A2-1FBF-440B-A0E0-7E33925015F8} = {616E9EA0-1A7F-4608-BEEA-9C3F26FAF516}
		{6E475819-2732-4C87-9F55-CA8777332642} = {616E9EA0-1A7F-4608-BEEA-9C3F26FAF516}
		{9AC34A40-36D7-4D0C-A667-508A386B76D5} = {616E9EA0-1A7F-4608-BEEA-9C3F26FAF516}
		{E6EBE3BA-EA3E-4A72-AD29-6E7AFF4F1B25} = {9AC34A40-36D7-4D0C-A667-508A386B76D5}
		{AB77121D-856C-4A53-B964-3BEE7BC1D8FC} = {9AC34A40-36D7-4D0C-A667-508A386B76D5}
		{06F00A11-3606-43F2-B6F5-907586C606B8} = {8BB043D2-BDE7-461C-8D9C-5184A0A0C185}
		{6CC132FD-8633-46B0-B321-61067B544A75} = {8BB043D2-BDE7-461C-8D9C-5184A0A0C185}
		{E044AC17-E772-4DA6-A65D-ED5FADECF4FD} = {8BB043D2-BDE7-461C-8D9C-5184A0A0C185}
		{81215169-EF34-4378-9DB6-85D62D481DEF} = {8BB043D2-BDE7-461C-8D9C-5184A0A0C185}
	EndGlobalSection
EndGlobal
