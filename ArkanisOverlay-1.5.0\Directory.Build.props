<Project>
    <PropertyGroup>
        <LangVersion>preview</LangVersion>
        <AnalysisLevel>preview</AnalysisLevel>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
        <NoWarn>
            $(NoWarn);
            CA1848; <!-- For improved performance, use the LoggerMessage delegates instead of calling 'LoggerExtensions.LogError(ILogger, string?, params object?[])' -->
            CA1826; <!-- Do not use Enumerable methods on indexable collections. Instead use the collection directly. -->
            CA1822; <!-- Member 'PrepareRequest' does not access instance data and can be marked as static. -->
        </NoWarn>
        <WarningsNotAsErrors>
            $(WarningsNotAsErrors);
            NU1900; <!-- Error occurred while getting package vulnerability data: Unable to load the service index for source... -->
        </WarningsNotAsErrors>
    </PropertyGroup>

    <PropertyGroup>
        <PublisherName>Arkanis Corporation</PublisherName>
        <Company>Arkanis Corporation</Company>
        <Authors><PERSON><PERSON><PERSON><PERSON><PERSON>, TheKronnY and contributors</Authors>
        <Copyright>Copyright © $([System.DateTime]::Now.Year) <PERSON><PERSON><PERSON><PERSON><PERSON>, TheKronnY and contributors</Copyright>
    </PropertyGroup>

    <PropertyGroup>
        <!-- https://github.com/actions/setup-dotnet?tab=readme-ov-file#caching-nuget-packages -->
        <DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
        <!-- Use shared artifacts path -->
        <ArtifactsPath>$(MSBuildThisFileDirectory)artifacts</ArtifactsPath>
        <!-- Use package version lock file to ensure consistent package versions across builds -->
        <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
    </PropertyGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="$(PackageId).UnitTests"/>
        <InternalsVisibleTo Include="$(PackageId).IntegrationTests"/>
    </ItemGroup>
</Project>
