﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>true</IsPackable>
        <PackageId>Arkanis.Overlay.Common</PackageId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation"/>
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions"/>
        <PackageReference Include="FuzzySharp"/>
        <PackageReference Include="Humanizer"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Binder"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions"/>
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions"/>
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions"/>
        <PackageReference Include="MoreAsyncLINQ"/>
        <PackageReference Include="morelinq"/>
        <PackageReference Include="NuGet.Versioning"/>
        <PackageReference Include="Riok.Mapperly"/>
    </ItemGroup>

</Project>
