<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <SupportedPlatform Include="browser"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Blazor-Analytics"/>
        <PackageReference Include="MathEvaluator"/>
        <PackageReference Include="Microsoft.AspNetCore.Components.Web"/>
        <PackageReference Include="MudBlazor"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Arkanis.Overlay.Domain\Arkanis.Overlay.Domain.csproj"/>
        <ProjectReference Include="..\Arkanis.Overlay.Infrastructure\Arkanis.Overlay.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Resource Include="wwwroot\assets\img\uex-api-badge-powered.png"/>
    </ItemGroup>

</Project>
