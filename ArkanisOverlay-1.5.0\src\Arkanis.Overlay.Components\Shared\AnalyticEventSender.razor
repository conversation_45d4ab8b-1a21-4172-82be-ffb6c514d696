@inject IJSRuntime JsRuntime
@inject IAnalytics Analytics
@inject IDialogService DialogService
@inject IAnalyticsEventReporter AnalyticsEventReporter
@inject IGlobalAnalyticsReporter GlobalAnalyticsReporter
@inject IUserPreferencesProvider UserRePreferencesProvider
@inject SharedAnalyticsPropertyProvider AnalyticsPropertyProvider
@inject ILogger<AnalyticEventSender> Logger
@using Microsoft.EntityFrameworkCore.Infrastructure
@using Microsoft.JSInterop
@implements IDisposable

<NavigationTracker/>

@code
{
    private EventInterop? _eventInterop;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            await ConfigureAnalyticsAsync(UserRePreferencesProvider.CurrentPreferences);
            await LinkAnalyticsReporterAsync();

            _eventInterop = new EventInterop(JsRuntime);
            var linkAnalyticsHandler = EventInterop.CreateHandler(LinkAnalyticsReporterAsync);
            await _eventInterop.RegisterWindowEventHandlerAsync("focus", linkAnalyticsHandler);
        }
    }

    private async Task LinkAnalyticsReporterAsync()
        => await GlobalAnalyticsReporter.LinkReporterAsync(AnalyticsEventReporter);

    public void Dispose()
    {
        UserRePreferencesProvider.ApplyPreferences -= OnPreferencesChanged;
        DialogService.DialogInstanceAddedAsync -= OnDialogInstanceAddedAsync;
        _eventInterop?.Dispose();
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();
        UserRePreferencesProvider.ApplyPreferences += OnPreferencesChanged;
        DialogService.DialogInstanceAddedAsync += OnDialogInstanceAddedAsync;
    }

    private Task OnDialogInstanceAddedAsync(IDialogReference arg)
    {
        _ = arg.Result.ContinueWith(async _ =>
            {
                if (arg.Dialog?.GetType().ShortDisplayName() is { } dialogName)
                {
                    await GlobalAnalyticsReporter.TrackEventAsync(new DialogOpenedEvent(dialogName));
                }
            }
        );
        return Task.CompletedTask;
    }

    private async void OnPreferencesChanged(object? _, UserPreferences preferences)
    {
        try
        {
            await ConfigureAnalyticsAsync(preferences);
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception e)
        {
            Logger.LogError(e, "Failed to properly update analytics preferences");
        }
    }

    private async Task ConfigureAnalyticsAsync(UserPreferences preferences)
    {
        Logger.LogDebug("Configuring analytics tracking, disable? {DisableAnalytics}", preferences.DisableAnalytics);

        if (preferences.DisableAnalytics)
        {
            Analytics.Disable();
        }
        else
        {
            Analytics.Enable();

            var globalConfigData = AnalyticsPropertyProvider.PropertyItems.ToDictionary();
            await Analytics.ConfigureGlobalConfigData(globalConfigData);
            Analytics.ConfigureGlobalEventData(globalConfigData);
        }
    }

}
