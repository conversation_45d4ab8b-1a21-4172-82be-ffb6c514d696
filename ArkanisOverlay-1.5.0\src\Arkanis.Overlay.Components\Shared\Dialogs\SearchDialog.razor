@using Arkanis.Overlay.Components.Views
<MudDialog Gutters="false"
           ContentClass="ma-0"
           ContentStyle="background: var(--mud-palette-background);">
    <TitleContent>
        Search
    </TitleContent>
    <DialogContent>
        <SearchView/>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@Cancel">Close</MudButton>
    </DialogActions>
</MudDialog>

@code
{

    [CascadingParameter]
    public required IMudDialogInstance MudDialog { get; set; }

    private void Cancel()
        => MudDialog.Cancel();

    public static async Task<DialogResult> ShowAsync(IDialogService dialogService)
    {
        var dialogOptions = new DialogOptions
        {
            FullWidth = true,
            FullScreen = true,
            CloseOnEscapeKey = true,
            CloseButton = true,
        };
        var dialogRef = await dialogService.ShowAsync<SearchDialog>(null, dialogOptions);
        return await dialogRef.Result ?? DialogResult.Cancel();
    }

}
