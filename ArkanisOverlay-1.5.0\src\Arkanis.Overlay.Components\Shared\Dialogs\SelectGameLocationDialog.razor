@using Arkanis.Overlay.Domain.Abstractions.Game
<MudDialog DefaultFocus="@DefaultFocus.FirstChild" ContentClass="py-2">
    <TitleContent>
        Select Location
    </TitleContent>
    <DialogContent>
        <GameEntitySelectBox
            Placeholder="Search for a game location"
            EntityCategory="@GameEntityCategory.Location"
            Accept="@AcceptEntity"
            @bind-Value="Location"/>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@Cancel">Cancel</MudButton>
        <MudButton Color="@Color.Success"
                   OnClick="@Submit">
            @SubmitLabel
        </MudButton>
    </DialogActions>
</MudDialog>

@code
{

    [CascadingParameter]
    public required IMudDialogInstance MudDialog { get; set; }

    [Parameter]
    public IGameEntity? Location { get; set; }

    [Parameter]
    public required Func<IGameLocation, bool> AcceptLocation { get; set; }

    [Parameter]
    public string SubmitLabel { get; set; } = "Continue";

    private bool AcceptEntity(IGameEntity entity)
        => entity is IGameLocation location && AcceptLocation(location);

    private void Submit()
        => MudDialog.Close(DialogResult.Ok(Location));

    private void Cancel()
        => MudDialog.Cancel();

    public static async Task<IGameLocation?> ShowAsync(IDialogService dialogService, Func<IGameLocation, bool>? accept = null)
    {
        accept ??= _ => true;
        var dialogParameters = new DialogParameters<SelectGameLocationDialog>
        {
            [nameof(AcceptLocation)] = accept,
        };
        var dialogOptions = new DialogOptions
        {
            FullWidth = true,
            MaxWidth = MaxWidth.Medium,
            CloseOnEscapeKey = true,
            CloseButton = true,
        };
        var dialogRef = await dialogService.ShowAsync<SelectGameLocationDialog>(null, dialogParameters, dialogOptions);
        return await dialogRef.GetReturnValueAsync<IGameLocation>();
    }

}
