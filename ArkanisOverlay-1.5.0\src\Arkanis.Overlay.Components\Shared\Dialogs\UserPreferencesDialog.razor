@using System.Globalization
@using Arkanis.Overlay.Common.Abstractions
@using Arkanis.Overlay.Common.Extensions
@using Arkanis.Overlay.Common.Models
@inject IUserPreferencesManager UserPreferencesManager
@inject IAppVersionProvider VersionProvider

<MudDialog ContentStyle="padding: 0">
    <TitleContent>
        Preferences
    </TitleContent>
    <DialogContent>
        <MudDivider/>
        <MudExpansionPanels Outlined Square Dense>
            <MudExpansionPanel Expanded Dense>
                <TitleContent>
                    General
                </TitleContent>
                <ChildContent>
                    <MudForm @ref="_form">
                        <MudGrid>
                            <MudItem xs="12">
                                <MudText Typo="@Typo.caption">Application</MudText>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudSwitch
                                    @bind-Value="@Preferences.TerminateOnGameExit"
                                    @bind-Value:after="@ValidateAsync"
                                    Color="@Color.Success"
                                    Label="Terminate on game exit"/>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudSwitch
                                    @bind-Value="@Preferences.AutoStartWithBoot"
                                    @bind-Value:after="@ValidateAsync"
                                    Color="@Color.Success"
                                    Label="Auto-start on system boot"/>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudSelect T="@UpdateChannel"
                                           @bind-Value="@Preferences.UpdateChannel"
                                           @bind-Value:after="@ValidateAsync"
                                           Label="Update channel"
                                           Adornment="@Adornment.Start"
                                           AdornmentIcon="@MaterialSymbols.Outlined.SystemUpdateAlt"
                                           Text="@(Preferences.UpdateChannel.Name)"
                                           Required
                                           Dense>
                                    @foreach (var channel in UpdateChannel.All)
                                    {
                                        <MudSelectItem Value="@channel"
                                                       Disabled="@(!UpdateChannel.Available.Contains(channel))">
                                            <span>@channel.Name</span>
                                        </MudSelectItem>
                                    }
                                </MudSelect>
                                <UpdateChannelDetails Model="@Preferences.UpdateChannel"/>
                                @if (UserPreferencesManager.CurrentPreferences.UpdateChannel != Preferences.UpdateChannel
                                     && (UserPreferencesManager.CurrentPreferences.UpdateChannel.IsUnstable
                                         || Preferences.UpdateChannel.IsUnstable))
                                {
                                    <MudText Typo="@Typo.caption" Color="@Color.Error">
                                        Potential version downgrade will lead to <b>full wipe</b> of persistent local
                                        storage.
                                    </MudText>
                                }
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudStack Spacing="0">
                                    <MudSwitch
                                        @bind-Value="@Preferences.DisableAnalytics"
                                        @bind-Value:after="@ValidateAsync"
                                        Color="@Color.Error"
                                        Label="Disable usage analytics"/>
                                    @if (UserPreferencesManager.CurrentPreferences.DisableAnalytics != Preferences.DisableAnalytics)
                                    {
                                        <MudText Typo="@Typo.caption" Color="@Color.Warning">
                                            App restart may be necessary to fully apply
                                        </MudText>
                                    }
                                </MudStack>
                            </MudItem>
                            <MudItem xs="12">
                                <MudText Typo="@Typo.caption">Overlay</MudText>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudSwitch
                                    @bind-Value="@Preferences.BlurBackground"
                                    @bind-Value:after="@ValidateAsync"
                                    Color="@Color.Success"
                                    Label="Blur overlay background"/>
                            </MudItem>
                            <MudItem xs="12">
                                <MudText Typo="@Typo.caption">Locale</MudText>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudAutocomplete T="CultureInfo"
                                                 Label="Overlay language"
                                                 Adornment="@Adornment.Start"
                                                 AdornmentIcon="@MaterialSymbols.Outlined.Language"
                                                 @bind-Value="@Preferences.CustomCultureInfo"
                                                 @bind-Value:after="@ValidateAsync"
                                                 Text="@Preferences.CustomCultureInfo?.EnglishName"
                                                 HelperText="@CultureSelectHelpText"
                                                 SearchFunc="SearchCulture"
                                                 ToStringFunc="@(culture => culture?.EnglishName)"
                                                 Disabled
                                                 Clearable
                                                 Dense>
                                    <ItemTemplate Context="culture">
                                        @culture.EnglishName <code>(@culture.ThreeLetterISOLanguageName)</code>
                                    </ItemTemplate>
                                </MudAutocomplete>
                            </MudItem>
                            <MudItem xs="12" sm="6">
                                <MudAutocomplete T="RegionInfo"
                                                 Label="Display format region"
                                                 Adornment="@Adornment.Start"
                                                 AdornmentIcon="@MaterialSymbols.Outlined.Globe"
                                                 @bind-Value="@Preferences.CustomRegionInfo"
                                                 @bind-Value:after="@ValidateAsync"
                                                 SearchFunc="SearchRegion"
                                                 ToStringFunc="@(region => region?.EnglishName)"
                                                 Clearable
                                                 Dense>
                                    <ItemTemplate Context="region">
                                        @region.EnglishName <code>(@region.ThreeLetterISORegionName)</code>
                                    </ItemTemplate>
                                </MudAutocomplete>
                            </MudItem>
                            <MudItem xs="12">
                                <KeyboardShortcutInput
                                    Label="Overlay toggle shortcut"
                                    @bind-Value="@Preferences.LaunchShortcut"
                                    @bind-Value:after="@ValidateAsync"
                                    Required/>
                            </MudItem>
                        </MudGrid>
                    </MudForm>
                </ChildContent>
            </MudExpansionPanel>
            <MudExpansionPanel Expanded Dense>
                <TitleContent>
                    Cache management
                </TitleContent>
                <ChildContent>
                    <CacheManagement/>
                </ChildContent>
            </MudExpansionPanel>
        </MudExpansionPanels>
        <MudDivider/>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@Cancel">Cancel</MudButton>
        <MudButton Color="@Color.Success" OnClick="@SubmitAsync" Disabled="@(!IsValid)">Save</MudButton>
    </DialogActions>
</MudDialog>

@code
{

    private MudForm? _form;

    private static CultureInfo[] _cultures = CultureInfo.GetCultures(CultureTypes.NeutralCultures)
        .Where(culture => !string.IsNullOrWhiteSpace(culture.ThreeLetterISOLanguageName))
        .OrderBy(culture => culture.EnglishName)
        .Distinct()
        .ToArray();

    private static RegionInfo[] _regions = CultureInfo.GetCultures(CultureTypes.SpecificCultures)
        .Select(culture => new RegionInfo(culture.Name))
        .Where(region => !string.IsNullOrWhiteSpace(region.ThreeLetterISORegionName))
        .OrderBy(region => region.EnglishName)
        .Distinct()
        .ToArray();

    private UserPreferences Preferences { get; set; } = new();

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        Preferences = UserPreferencesManager.CurrentPreferences with { };
    }

    [CascadingParameter]
    public required IMudDialogInstance MudDialog { get; set; }

    public string CultureSelectHelpText
        => Preferences.CustomCultureInfo is null
            ? $"Using system culture {Preferences.ActiveCultureInfo.EnglishName}"
            : $"Using {Preferences.ActiveCultureInfo.EnglishName}";

    public bool IsValid
        => _form?.IsValid ?? false;

    private Task<IEnumerable<CultureInfo>> SearchCulture(string? searchTerm, CancellationToken ct)
        => Task.FromResult(_cultures.FuzzySearch(culture => culture.EnglishName, searchTerm ?? string.Empty));

    private Task<IEnumerable<RegionInfo>> SearchRegion(string? searchTerm, CancellationToken ct)
        => Task.FromResult(_regions.FuzzySearch(culture => culture.EnglishName, searchTerm ?? string.Empty));

    private async Task SubmitAsync()
    {
        await ValidateAsync();
        if (!IsValid)
        {
            return;
        }

        await UserPreferencesManager.SaveAndApplyUserPreferencesAsync(Preferences);
        MudDialog.Close(DialogResult.Ok(true));
    }

    private async Task ValidateAsync()
    {
        if (_form is not null)
        {
            await _form.Validate();
        }
    }

    private void Cancel()
        => MudDialog.Cancel();

    public static Task<DialogResult> ShowAsync(IDialogService dialogService)
    {
        var dialogOptions = new DialogOptions
        {
            FullWidth = true,
            MaxWidth = MaxWidth.Small,
            CloseOnEscapeKey = true,
        };

        return ShowAsync(dialogService, dialogOptions);
    }

    public static Task<DialogResult> ShowFullscreenAsync(IDialogService dialogService)
    {
        var dialogOptions = new DialogOptions
        {
            FullScreen = true,
            CloseOnEscapeKey = true,
            NoHeader = true,
        };

        return ShowAsync(dialogService, dialogOptions);
    }

    private static async Task<DialogResult> ShowAsync(IDialogService dialogService, DialogOptions dialogOptions)
    {
        var dialogRef = await dialogService.ShowAsync<UserPreferencesDialog>(null, dialogOptions);
        return await dialogRef.Result ?? DialogResult.Cancel();
    }

}
