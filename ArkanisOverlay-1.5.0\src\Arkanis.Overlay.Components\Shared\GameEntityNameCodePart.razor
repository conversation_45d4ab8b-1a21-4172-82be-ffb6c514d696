<MudText HtmlTag="code"
         Typo="@Typo"
         Color="@Color.Secondary"
         Style="@CodeStyle">
    @if (PrefixContent is not null)
    {
        <span>@PrefixContent</span>
    }
    <MudTooltip Text="@TooltipContent" Placement="@Placement.Top">
        <span>@Model.Code</span>
    </MudTooltip>
</MudText>

@code
{

    private string CodeStyle
        => $"font-family: monospace; {NameStyle}; {Style}";

    private string NameStyle
        => !Embedded
            ? "font-weight: bold"
            : "font-weight: lighter";

    [Parameter]
    [EditorRequired]
    public required GameEntityName.IHasCode Model { get; set; }

    [Parameter]
    public bool Embedded { get; set; }

    [Parameter]
    public Typo Typo { get; set; } = Typo.body1;

    [Parameter]
    public string? Style { get; set; }

    [Parameter]
    public RenderFragment? PrefixContent { get; set; }

    public string TooltipContent
        => Model is GameEntityName.Name name
            ? name.FullName
            : Model.Code;

}
