<MudStack Spacing="0"
          Class="game-entity-name">
    @if (Model.Location is { } locationReference && !Embedded)
    {
        <div class="header location">
            <GameLocationNamePart
                Model="@locationReference"
                Typo="@Typo"/>
        </div>
    }
    <MudStack Class="primary"
              Spacing="1"
              Row>
        @if (Model.Company is { } companyReference)
        {
            <div class="d-flex text-secondary flex-truncate">
                <GameCompanyNamePart
                    Model="@companyReference"
                    Typo="@Typo"
                    PreferShort/>
            </div>
        }
        <GameEntityNamePart
            Class="no-wrap"
            Typo="@Typo"
            Model="@Model.MainContent"
            PreferCode="@PreferCode"
            Embedded="@Embedded"
            PreferShort="@(PreferShort || Model.Company is not null)"/>
    </MudStack>
    @if (Model.Properties is { Count: > 0 } properties && !Embedded)
    {
        <MudStack Class="footer properties mt-n2 no-wrap"
                  Row>
            @foreach (var property in properties)
            {
                <MudStack Spacing="2" Row>
                    <span style="font-weight: lighter">@property.Key</span>
                    <b>@property.Value</b>
                </MudStack>
            }
        </MudStack>
    }
</MudStack>

@code
{

    [Parameter]
    public required GameEntityName Model { get; set; }

    [Parameter]
    public Typo? Typo { get; set; }

    [Parameter]
    public string? Style { get; set; }

    [Parameter]
    public bool Embedded { get; set; }

    [Parameter]
    public bool PreferCode { get; set; }

    [Parameter]
    public bool PreferShort { get; set; }

}
