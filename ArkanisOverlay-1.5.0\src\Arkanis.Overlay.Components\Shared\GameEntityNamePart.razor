<MudStack Class="@Class"
          Style="@Style"
          AlignItems="@AlignItems.Center"
          Row>
    @if (PreferCode && CodeModel is not null)
    {
    }
    else if ((Embedded || PreferShort) && Model is GameEntityName.IHasShortName shortVariant)
    {
        <MudTooltip Placement="@Placement.Top"
                    Text="@Model.FullName">
            <MudText Typo="@NameTypo" Style="@NameCss">
                @shortVariant.ShortName
            </MudText>
        </MudTooltip>
    }
    else
    {
        <MudText Typo="@NameTypo" Style="@NameCss">
            @Model.FullName
        </MudText>
    }
    @if ((!Embedded || PreferCode) && CodeModel is not null)
    {
        <GameEntityNameCodePart
            Model="@CodeModel"
            Typo="@CodeTypo"
            Embedded="@Embedded"/>
    }
</MudStack>

@code
{

    private string NameCss
        => Embedded
            ? "font-weight: lighter"
            : "font-weight: bold";

    private Typo NameTypo
        => Typo
           ?? (Embedded
               ? MudBlazor.Typo.overline
               : MudBlazor.Typo.h5);

    private Typo CodeTypo
        => Typo ?? MudBlazor.Typo.body1;

    private GameEntityName.IHasCode? CodeModel
        => Model as GameEntityName.IHasCode;

    [Parameter]
    public required GameEntityName.Name Model { get; set; }

    [Parameter]
    public bool Embedded { get; set; }

    [Parameter]
    public bool PreferCode { get; set; }

    [Parameter]
    public bool PreferShort { get; set; }

    [Parameter]
    public Typo? Typo { get; set; }

    [Parameter]
    public string? Class { get; set; }

    [Parameter]
    public string? Style { get; set; }

}
