@using Arkanis.Overlay.Domain.Abstractions
@using Arkanis.Overlay.Domain.Abstractions.Game
@inject ISearchService SearchService

<MudAutocomplete Label="@Label"
                 Placeholder="@Placeholder"
                 Value="@Value"
                 ValueChanged="@ValueChanged"
                 Text="@GetText(Value)"
                 HelperText="@HelperText"
                 Disabled="@Disabled"
                 Clearable="@(!Required)"
                 Required="@Required"
                 CoerceText="false"
                 PopoverClass="width-min-content"
                 SearchFunc="@FindLocationsAsync">
    <ItemTemplate>
        @if (context is not null)
        {
            <div class="my-n2">
                <GameEntityNameLabel Model="@context.Name"/>
            </div>
        }
        else
        {
            <MudText Typo="@Typo.inherit" Class="text-secondary">
                Unknown
            </MudText>
        }
    </ItemTemplate>
</MudAutocomplete>

@code
{

    private HashSet<IGameEntity> _except = [];
    private HashSet<IGameEntity>? _only;

    [Parameter]
    public string? Label { get; set; } = "Game entity";

    [Parameter]
    public string? Placeholder { get; set; } = "Search for game entity";

    [Parameter]
    public IGameEntity? Value { get; set; }

    [Parameter]
    public EventCallback<IGameEntity?> ValueChanged { get; set; }

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool Required { get; set; }

    [Parameter]
    public GameEntityCategory EntityCategory { get; set; }

    [Parameter]
    public GameEntityCategory[] EntityCategories { get; set; } = [];

    [Parameter]
    public Func<IGameEntity, bool> Accept { get; set; } = _ => true;

    [Parameter]
    public IEnumerable<IGameEntity> Except { get; set; } = [];

    [Parameter]
    public IEnumerable<IGameEntity>? Only { get; set; }

    [Parameter]
    public string? HelperText { get; set; }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        _except = Except.ToHashSet(IIdentifiable.EqualityComparer.For<IGameLocation>());
        _only = Only?.ToHashSet(IIdentifiable.EqualityComparer.For<IGameLocation>());
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            // force render to update initially coerced display text for default values
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task<IEnumerable<IGameEntity?>> FindLocationsAsync(string? searchText, CancellationToken ct)
    {
        IEnumerable<SearchQuery> queries = [];
        if (EntityCategory is not GameEntityCategory.Undefined)
        {
            queries = queries.Append(new EntityCategorySearch(EntityCategory));
        }

        foreach (var category in EntityCategories)
        {
            queries = queries.Append(new EntityCategorySearch(category, false));
        }

        if (searchText is not null)
        {
            queries = queries.Append(new FuzzyTextSearch(searchText));
        }

        var results = await SearchService.SearchAsync(queries, ct);
        return results.GameEntities
            .Select(x => x.Subject)
            .Where(x => _except.Contains(x) == false)
            .Where(x => _only is null || _only.Contains(x))
            .Where(Accept);
    }

    private string? GetText(IGameEntity? value)
        => value?.Name.MainContent.FullName;

}
