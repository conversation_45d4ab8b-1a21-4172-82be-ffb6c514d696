@using Arkanis.Overlay.Domain.Enums
@if (Model.Traits.FirstOrDefault(x => x.TraitType == GameItemTraitType.Grade) is { } itemGrade)
{
    <GameItemTraitDetail
        Model="@itemGrade"
        Value="@itemGrade.Content"/>
}

@if (Model.Traits.FirstOrDefault(x => x.TraitType == GameItemTraitType.Class) is { } itemClass)
{
    <GameItemTraitDetail
        Model="@itemClass"
        Value="@itemClass.Content"/>
}

@code
{

    [Parameter]
    public required GameItem Model { get; set; }

}
