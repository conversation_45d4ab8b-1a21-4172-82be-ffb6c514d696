<MudStack Spacing="2"
          AlignItems="@AlignItems.Baseline"
          Class="no-wrap"
          Reverse="Reverse"
          Row>
    @PrefixContent
    @foreach (var (index, subLocation) in Model.Location.CreatePathToRoot().Index())
    {
        if (index > 0)
        {
            @Separator
        }

        <GameEntityNamePart
            Model="@subLocation.Name.MainContent"
            Typo="@Typo"
            Embedded/>
    }
    @SuffixContent
</MudStack>

@code
{

    public static RenderFragment Separator
        => @<span class="text-secondary">/</span>;

    [Parameter]
    public required GameEntityName.LocationReference Model { get; set; }

    [Parameter]
    public Typo? Typo { get; set; }

    [Parameter]
    public RenderFragment? PrefixContent { get; set; }

    [Parameter]
    public RenderFragment? SuffixContent { get; set; }

    [Parameter]
    public bool Reverse { get; set; }

}
