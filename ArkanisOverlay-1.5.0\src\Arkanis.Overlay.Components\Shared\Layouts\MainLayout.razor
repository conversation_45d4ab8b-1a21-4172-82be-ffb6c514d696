@using Arkanis.Overlay.Common.Abstractions
@using Microsoft.AspNetCore.Http.Extensions
@using Microsoft.JSInterop
@inherits LayoutComponentBase
@inject IAppVersionProvider VersionProvider
@inject IJSRuntime JsRuntime

<MudThemeProvider Theme="_currentTheme" IsDarkMode="true"/>
<MudPopoverProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>
<AnalyticEventSender/>
<WebOverlayControlsInterop/>

<ErrorBoundary @ref="_errorBoundary">
    <ChildContent>
        <GlobalKeyboardEventProxyProvider>
            @Body
        </GlobalKeyboardEventProxyProvider>
    </ChildContent>
    <ErrorContent>
        <MudStack Class="h-100 w-100 pa-6" AlignItems="@AlignItems.Center" Justify="@Justify.Center">
            <div style="min-width: 20vw">
                <div class="mb-2">
                    <h1>Sorry, an unrecoverable error has occured</h1>
                    <pre style="white-space: pre-line;">@context.Message</pre>
                </div>
                <MudExpansionPanel Text="Error Details">
                    <div style="overflow-y: scroll">
                        <pre>@context.ToString()</pre>
                    </div>
                </MudExpansionPanel>
                <MudStack Class="mt-4" Row>
                    <MudButton Color="@Color.Success"
                               Size="@Size.Large"
                               Variant="@Variant.Outlined"
                               StartIcon="@MaterialSymbols.Outlined.FrameReload"
                               OnClick="@TryRecover">
                        Try To Reload
                    </MudButton>
                    <MudButton Color="@Color.Success"
                               Size="@Size.Large"
                               Variant="@Variant.Outlined"
                               StartIcon="@MaterialSymbols.Outlined.ChevronLeft"
                               OnClick="@NavigateToPrevious">
                        Go To Previous Page
                    </MudButton>
                    <MudButton Color="@Color.Warning"
                               Size="@Size.Large"
                               Variant="@Variant.Outlined"
                               StartIcon="@MaterialSymbols.Outlined.OpenInBrowser"
                               Href="@CreateNewIssueUrl(context)"
                               Target="_blank">
                        Report this issue (please check if exists first)
                    </MudButton>
                </MudStack>
            </div>
        </MudStack>
    </ErrorContent>
</ErrorBoundary>

@code
{

    private readonly MudTheme _currentTheme = new()
    {
        PaletteDark = new PaletteDark
        {
            Surface = "#0e0f0f",
        },
    };

    private ErrorBoundary? _errorBoundary;

    private void TryRecover()
    {
        _errorBoundary?.Recover();
    }

    private async Task NavigateToPrevious()
    {
        await JsRuntime.InvokeVoidAsync("history.back");
        await Task.Delay(20);
        TryRecover();
    }

    private string CreateNewIssueUrl(Exception context)
    {
        var queryBuilder = new QueryBuilder
        {
            { "template", "bug_report.yml" },
            { "version", VersionProvider.CurrentVersion.ToFullString() },
            { "logs", context.ToString() },
        };

        return $"{ApplicationConstants.GitHubRepositoryUrl}/issues/new{queryBuilder.ToQueryString()}";
    }
}
