@inherits LayoutComponentBase
@implements IDisposable
@layout MainLayout

@inject IOverlayEventProvider OverlayEventProvider
@inject IAnalyticsEventReporter AnalyticsEventReporter
@inject IUserPreferencesProvider UserPreferencesProvider

<!--suppress CssUnusedSymbol, CssUnresolvedCustomProperty -->
<style>
    body {
        background-color: transparent;
    }
</style>

<MudAppBar Style="background: var(--mud-palette-surface);"
           Fixed="@false"
           Dense>

    <MudStack Class="w-100"
              Justify="@Justify.SpaceBetween"
              AlignItems="@AlignItems.Center"
              Row>
        <MudStack Style="width: 20vw" Row>
            <SectionOutlet SectionId="@RenderSections.AppBar.AppMenu"/>
        </MudStack>

        <OverlayModuleNavigation
            KeybindsOrigin="@Origin.BottomCenter"
            UpdatesOrigin="@Origin.BottomRight"
            Horizontal/>

        <MudStack Style="width: 20vw"
                  Justify="@Justify.FlexEnd"
                  AlignItems="@AlignItems.Center"
                  Row>
            <SectionOutlet SectionId="@RenderSections.AppBar.UserMenu"/>
            <MudIconButton
                Icon="@MaterialSymbols.Outlined.Notifications"
                Disabled/>
            <MudAvatar
                Size="@Size.Small"/>
        </MudStack>
    </MudStack>
</MudAppBar>

<div style="@ContainerBlurCss">
    <div style="height: calc(100vh - 48px); overflow-y: scroll">
        <OverlayControls/>
        @Body
    </div>
</div>

@code
{

    private string ContainerBlurCss
        => UserPreferencesProvider.CurrentPreferences.BlurBackground
            // calc(100% - 48px) offsets the app bar height
            ? "min-height: calc(100% - 48px); backdrop-filter: blur(12px);"
            : "";

    protected override void OnInitialized()
    {
        base.OnInitialized();

        OverlayEventProvider.OverlayShown += OnOverlayShown;
        OverlayEventProvider.OverlayHidden += OnOverlayHidden;
        UserPreferencesProvider.ApplyPreferences += OnPreferencesChanged;
    }

    private void OnPreferencesChanged(object? _, UserPreferences e)
        => InvokeAsync(StateHasChanged);

    private void OnOverlayShown(object? _, EventArgs e)
        => AnalyticsEventReporter.TrackEventAsync(new OverlayShownEvent());

    private void OnOverlayHidden(object? _, EventArgs e)
        => AnalyticsEventReporter.TrackEventAsync(new OverlayHiddenEvent());

    public void Dispose()
    {
        OverlayEventProvider.OverlayShown -= OnOverlayShown;
        OverlayEventProvider.OverlayHidden -= OnOverlayHidden;
        UserPreferencesProvider.ApplyPreferences -= OnPreferencesChanged;
    }

}
