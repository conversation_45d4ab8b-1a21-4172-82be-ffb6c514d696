@using System.Collections.Concurrent
@using Microsoft.AspNetCore.Components.Routing
@implements IDisposable
@inject IServiceProvider ServiceProvider
@inject NavigationManager NavigationManager
@inject OverlayModules OverlayModules


<!--suppress CssUnusedSymbol, CssUnresolvedCustomProperty -->
<style>
    .navigation-tabs {
        .mud-tab.mud-tab-panel {
            min-width: initial !important;
            padding-left: 16px;
            padding-right: 16px;
        }

        .mud-chip {
            margin: 0;
            height: 20px;
        }
    }
</style>

<MudTabs @ref="@_tabs"
         Class="with-badges navigation-tabs"
         ActivePanelIndex="@ActiveModuleIndex">
    @foreach (var (moduleIndex, module) in OverlayModules.GetAll().Index())
    {
        var moduleShortcut = GetKeyFor(moduleIndex, module);
        var isModuleActive = IsActive(module);
        var isShortcutAvailable = !isModuleActive && !module.Disabled;
        var updateCount = GetUpdateCount(module);

        <MudTabPanel @key="@module.Url"
                     Icon="@module.Icon"
                     OnClick="@GetActivationCallbackFor(module)"
                     Disabled="@module.Disabled">
            <TabWrapperContent>
                <KeyboardShortcutBadge @key="@module.Name"
                                       Key="@moduleShortcut"
                                       IsActive="@isShortcutAvailable"
                                       OnKeyPress="@GetActivationCallbackFor(module)"
                                       Origin="@KeybindsOrigin"
                                       Elevation="4"
                                       DoNotOverlap="@DoNotOverlapKeybinds">
                    @context
                </KeyboardShortcutBadge>
            </TabWrapperContent>
            <TabContent>
                <MudStack AlignItems="@AlignItems.Center"
                          Spacing="3"
                          Row>
                    <MudIcon
                        Icon="@module.Icon"/>
                    @module.Name
                    @if (updateCount > 0)
                    {
                        <MudChip
                            Size="@Size.Small"
                            Value="@updateCount"
                            Color="@Color.Warning"/>
                    }
                </MudStack>
            </TabContent>
        </MudTabPanel>
    }
</MudTabs>

@code
{

    private MudTabs? _tabs;
    private readonly ConcurrentDictionary<int, int> _moduleUpdates = [];
    private readonly ConcurrentDictionary<int, IDisposable> _moduleChangeRegistrations = [];
    private string? _currentUri;

    private string CurrentUri
    {
        get => _currentUri ?? NavigationManager.Uri;
        set => _currentUri = value;
    }

    private int ActiveModuleIndex
        => OverlayModules.GetAll()
            .Index()
            .Where(x => IsActive(x.Value))
            .Select(x => x.Key)
            .First();

    [Parameter]
    public string Class { get; set; } = string.Empty;

    [Parameter]
    public string Style { get; set; } = string.Empty;

    [Parameter]
    public bool Horizontal { get; set; }

    [Parameter]
    public Origin KeybindsOrigin { get; set; } = Origin.TopRight;

    [Parameter]
    public bool DoNotOverlapKeybinds { get; set; }

    [Parameter]
    public Origin UpdatesOrigin { get; set; } = Origin.TopCenter;

    [Parameter]
    public bool DoNotOverlapUpdates { get; set; }

    private bool IsActive(OverlayModules.Entry module)
        => CurrentUri.StartsWith(module.GetAbsoluteUri(NavigationManager));

    protected override void OnInitialized()
    {
        base.OnInitialized();
        NavigationManager.LocationChanged += OnLocationChangedAsync;
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        OverlayModules.GetAll().ForEach(RegisterForChanges);
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await RefreshDataAsync();
    }

    private void RegisterForChanges(OverlayModules.Entry entry)
    {
        var registration = new ChangeTokenRegistration(() => entry.GetChangeToken(ServiceProvider));
        registration.OnChange += () => InvokeAsync(RefreshDataAsync);
        _moduleChangeRegistrations[entry.GetHashCode()] = registration;
    }

    private async Task RefreshDataAsync()
    {
        async Task<bool> LoadAsync(OverlayModules.Entry entry)
        {
            var previousValue = GetUpdateCount(entry);
            var currentValue = await entry.GetUpdateCountAsync(ServiceProvider);
            _moduleUpdates[entry.GetHashCode()] = currentValue;
            return previousValue != currentValue;
        }

        var updateTasks = OverlayModules.GetAll().Select(LoadAsync);
        var changes = await Task.WhenAll(updateTasks);
        if (changes.Any())
        {
            _tabs?.ActivatePanel(ActiveModuleIndex);
            await InvokeAsync(StateHasChanged);
        }
    }

    private void OnLocationChangedAsync(object? _, LocationChangedEventArgs e)
    {
        CurrentUri = e.Location;
        InvokeAsync(StateHasChanged);
    }

    private KeyboardKey GetKeyFor(int index, OverlayModules.Entry module)
        => module.ShortcutOverride
           ?? (index < 12
               ? KeyboardKey.F1 + index
               : KeyboardKey.Unknown);

    private EventCallback GetActivationCallbackFor(OverlayModules.Entry module)
        => EventCallback.Factory.Create(this, () => Activate(module));

    private void Activate(OverlayModules.Entry module)
    {
        CurrentUri = module.GetAbsoluteUri(NavigationManager);
        NavigationManager.NavigateTo(module.Url);
    }

    private int GetUpdateCount(OverlayModules.Entry module)
        => _moduleUpdates.GetValueOrDefault(module.GetHashCode());

    public void Dispose()
    {
        _moduleChangeRegistrations.Values.ForEach(x => x.Dispose());
        NavigationManager.LocationChanged -= OnLocationChangedAsync;
    }

}
