@using Humanizer
<MudGrid>
    <MudItem xs="12" sm="7">
        <MudTextField
            Label="Amount"
            @bind-Value="@Value.Amount"
            @bind-Value:after="@OnValueHasChanged"
            InputType="@InputType.Number"
            HelperText="@AmountHelperText"
            Disabled="@(Disabled)"
            Required
            min="0"
            max="@Max"/>
    </MudItem>
    <MudItem xs="12" sm="5">
        <MudSelect Label="Unit"
                   @bind-Value="@Value.Unit"
                   @bind-Value:after="@OnValueHasChanged"
                   Disabled="@(Disabled || DisabledUnitChange)">
            @foreach (var unit in Enum.GetValues<Quantity.UnitType>().Except([Quantity.UnitType.Undefined]))
            {
                <MudSelectItem Value="@unit">
                    @unit.Humanize()
                </MudSelectItem>
            }
        </MudSelect>
    </MudItem>
</MudGrid>

@code
{

    [Parameter]
    public Quantity Value { get; set; } = Quantity.Default;

    [Parameter]
    public EventCallback<Quantity> ValueChanged { get; set; }

    [Parameter]
    public string? AmountHelperText { get; set; }

    [Parameter]
    public int Max { get; set; } = int.MaxValue;

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool DisabledUnitChange { get; set; }

    private async Task OnValueHasChanged()
        => await InvokeAsync(() => ValueChanged.InvokeAsync(Value));
}
