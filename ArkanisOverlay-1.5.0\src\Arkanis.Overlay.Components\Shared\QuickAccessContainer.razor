@using Microsoft.JSInterop
@implements IAsyncDisposable
@inject IJSRuntime JsRuntime
@inject ILogger<QuickAccessContainer> Logger

<FocusRegion @ref="@_regionRef">
    <CascadingValue Value="@(this)" IsFixed>
        @ChildContent(this)
    </CascadingValue>
</FocusRegion>

@code
{

    private DebounceHelper? _debounceHelper;
    private FocusRegion? _regionRef;
    private JsComponentInterop<QuickAccessContainer>? _interop;

    [Parameter]
    [EditorRequired]
    public required RenderFragment<QuickAccessContainer> ChildContent { get; set; }

    [Parameter]
    public string QuerySelector { get; set; } = "*";

    [Parameter]
    public bool ContentVirtualized { get; set; }

    private readonly HashSet<IEntry> _entries = [];

    public EntryRegistration Register(IEntry entry)
    {
        lock (_entries)
        {
            _entries.Add(entry);
        }

        RequestDebouncedUpdate();
        return new EntryRegistration(this)
        {
            Entry = entry,
        };
    }

    public void Unregister(IEntry item)
    {
        lock (_entries)
        {
            _entries.Remove(item);
        }

        RequestDebouncedUpdate();
    }

    private void RequestDebouncedUpdate()
        => _debounceHelper?.RequestDebounced();

    private async Task InvokeJsUpdateAsync()
    {
        if (_interop?.Controls is not null)
        {
            await _interop.Controls.InvokeVoidAsync("updateDebounced", 100);
        }
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();
        _debounceHelper = new DebounceHelper(InvokeJsUpdateAsync, Logger);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _interop = JsRuntime.CreateLifetimeInterop(this);
            IEnumerable<string> features = ContentVirtualized
                ? []
                : ["dom"];
            await _interop.InitializeAsync(_regionRef!.ContainerElement, QuerySelector, features);
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ContentVirtualized && _debounceHelper is not null)
        {
            Logger.LogDebug("Requesting JavaScript logic to update");
            _debounceHelper.RequestDebounced();
        }

        await base.OnParametersSetAsync();
    }

    [JSInvokable]
    public async Task OnJsUpdateAsync()
    {
        IEntry[] entries;
        lock (_entries)
        {
            //? sort is essential due to the visibility call optimizations below
            //! sort must be performed here as the item index changes without notice
            entries = _entries.Order(IEntry.Comparer.Default).ToArray();
        }

        Logger.LogDebug("Updating {ItemCount} items within this quick access container", entries.Length);
        var firstVisibleItemIndex = await FindFirstVisibleItemIndex(entries);
        var pastVisibleItems = false;
        foreach (var entry in entries)
        {
            var visibilityInfo = entry.ItemIndex < firstVisibleItemIndex || pastVisibleItems
                ? EntryInfo.Empty
                : await _interop!.Controls!.InvokeAsync<EntryInfo>("getVisibilityInfo", entry.PageElement);

            if (entry.ItemIndex > firstVisibleItemIndex && !visibilityInfo.IsVisible)
            {
                pastVisibleItems = true;
            }

            await entry.OnQuickAccessInfoChanged(visibilityInfo);
        }
    }

    private async Task<int?> FindFirstVisibleItemIndex(IEntry[] items)
    {
        var itemIndexOffset = 1;
        var alreadyFoundVisible = false;

        for (var itemIndex = 0; itemIndex < items.Length; itemIndex += itemIndexOffset)
        {
            var item = items[itemIndex];
            var visibilityInfo = await _interop!.Controls!.InvokeAsync<EntryInfo>("getVisibilityInfo", item.PageElement);
            if (visibilityInfo.IsVisible)
            {
                if (itemIndex == 0)
                {
                    // this item is visible and is the first item, so we can skip the rest
                    return item.ItemIndex;
                }

                // this item is visible, so we can change the direction and find the first visible item
                alreadyFoundVisible = true;
                itemIndexOffset = -1;
            }
            else if (alreadyFoundVisible)
            {
                // this item is not visible, but we have already found a visible item before,
                //  so the previous item is the first visible item
                return items[itemIndex + 1].ItemIndex;
            }
            else if (itemIndexOffset < 4)
            {
                // we haven't found a visible item yet, increase the offset to skip more items
                itemIndexOffset *= 2;
            }
        }

        return null;
    }

    public async ValueTask DisposeAsync()
    {
        if (_interop is not null)
        {
            await _interop.DisposeAsync();
        }
    }

    public interface IEntry
    {
        int ItemIndex { get; }
        ElementReference PageElement { get; }

        Task OnQuickAccessInfoChanged(EntryInfo info);

        public class Comparer : IComparer<IEntry>
        {
            public static readonly Comparer Default = new();

            public int Compare(IEntry? x, IEntry? y)
            {
                if (x is null)
                {
                    return -1;
                }

                if (y is null)
                {
                    return 1;
                }

                return x.ItemIndex.CompareTo(y.ItemIndex);
            }
        }
    }

    public class EntryRegistration(QuickAccessContainer container) : IDisposable
    {
        public required IEntry Entry { get; init; }

        public void Dispose()
            => container.Unregister(Entry);
    }

    public record EntryInfo(int Index, bool IsVisible)
    {
        public static readonly EntryInfo Empty = new(-1, false);
    }
}
