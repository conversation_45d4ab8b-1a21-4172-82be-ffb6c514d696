@typeparam T where T : notnull

<span @onclick="ToggleDirection"
      @onclick:preventDefault="true"
      @onclick:stopPropagation="true"
      @oncontextmenu="ToggleDirection"
      @oncontextmenu:preventDefault="true"
      @oncontextmenu:stopPropagation="true">
    <MudButton StartIcon="@Icon"
               Color="@Color"
               ClickPropagation="true">
        @ChildContent
    </MudButton>
</span>

@code
{

    private Color Color
        => Direction == SortDirection.None || !IsActive
            ? Color.Inherit
            : Color.Success;

    private string Icon
        => Direction switch
        {
            SortDirection.Ascending when IsActive => Icons.Material.Filled.KeyboardArrowUp,
            SortDirection.Descending when IsActive => Icons.Material.Filled.KeyboardArrowDown,
            _ => Icons.Material.Filled.Sort,
        };

    private bool IsActive
        => TargetField.Equals(ActiveField);

    [Parameter]
    [EditorRequired]
    public required RenderFragment ChildContent { get; set; }

    [Parameter]
    [EditorRequired]
    public required T TargetField { get; set; }

    [Parameter]
    public T? ActiveField { get; set; }

    [Parameter]
    public EventCallback<T?> ActiveFieldChanged { get; set; }

    [Parameter]
    public SortDirection Direction { get; set; }

    [Parameter]
    public EventCallback<SortDirection> DirectionChanged { get; set; }

    [Parameter]
    public EventCallback OnChange { get; set; }

    private async Task ToggleDirection(MouseEventArgs args)
    {
        var newDirection = Direction switch
        {
            _ when args.Button == 2 => SortDirection.None,
            SortDirection.Ascending when IsActive => SortDirection.None,
            SortDirection.Descending when IsActive => SortDirection.Ascending,
            _ => SortDirection.Descending,
        };

        if (newDirection == SortDirection.None)
        {
            if (!IsActive)
            {
                return;
            }
        }

        Direction = newDirection;
        await DirectionChanged.InvokeAsync(Direction);

        if (Direction == SortDirection.None)
        {
            // the current field was active, so remove it
            await ActiveFieldChanged.InvokeAsync(default);
        }
        else
        {
            await ActiveFieldChanged.InvokeAsync(TargetField);
        }

        await OnChange.InvokeAsync();
    }

    public enum SortDirection
    {
        None,
        Ascending,
        Descending,
    }

}
