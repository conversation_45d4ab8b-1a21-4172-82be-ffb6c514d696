@using Arkanis.Overlay.Components.Views.Components
@using Arkanis.Overlay.Components.Views.Trade
<MudPaper Class="px-4 focus"
          tabindex="0">
    <div style="min-height: 64px; align-content: center;">
        <div style="display: grid; grid-template-columns: 5fr 1fr 1fr 1fr 5fr min-content;"
             class="align-center gap-3">
            @* <div class="my-n3 mx-n1 h-100" style="grid-column: auto; min-width: 20px"> *@
            @*     <MudStack Spacing="0" *@
            @*               Class="h-100" *@
            @*               AlignItems="@AlignItems.Center" *@
            @*               Justify="@Justify.Center"> *@
            @*         @if (DateTimeOffset.Now - route.CreatedAt > TimeSpan.FromDays(31)) *@
            @*         { *@
            @*             <MudIcon *@
            @*                 Icon="@Icons.Material.Filled.AccessTime" *@
            @*                 Color="@Color.Warning" *@
            @*                 Size="@Size.Small"/> *@
            @*         } *@
            @*     </MudStack> *@
            @* </div> *@
            <div style="grid-column: auto">
                <TradeRouteParty
                    Model="@Model.Origin"
                    Route="@Model"
                    Side="@TradeRouteParty.PartySide.Origin"
                    Spacing="6"/>
            </div>
            <div style="grid-column: auto" class="pr-1">
                <MudStack Justify="@Justify.Center"
                          AlignItems="@AlignItems.End"
                          Class="text-right h-100"
                          Spacing="0">
                    <MudText Typo="Typo.inherit" Color="@Color.Error">
                        <GameCurrencyLabel
                            Model="@PriceOriginTotal"
                            Decimals="1"/>
                    </MudText>
                </MudStack>
            </div>
            <div style="grid-column: auto">
                <MudStack AlignItems="@AlignItems.Center"
                          Justify="@Justify.Center"
                          Class="h-100 mx-3"
                          Spacing="0">
                    <MudText Typo="@Typo.body2" Class="my-n1 no-wrap text-secondary">
                        <GameEntityNamePart
                            Typo="@Typo.inherit"
                            Model="@Model.Commodity.Name.MainContent"
                            Style="height: initial; position: relative; z-index: 2"
                            PreferCode/>
                    </MudText>
                    <MudIcon
                        Icon="@Icons.Material.Filled.ArrowRightAlt"
                        Size="@Size.Large"
                        Class="my-n1"/>
                    @if (Model.Distance is { } distance)
                    {
                        <MudText Typo="@Typo.body2"
                                 Class="my-n1 no-wrap">
                            @distance Gm
                        </MudText>
                    }
                </MudStack>
            </div>
            <div style="grid-column: auto" class="d-flex justify-end pr-4">
                <MudStack Spacing="0"
                          Class="text-right">
                    <MudText Typo="Typo.inherit">
                        <GameCurrencyLabel
                            Model="@PriceDestinationTotal"
                            Decimals="1"/>
                    </MudText>
                    <MudText Typo="Typo.inherit"
                             Color="@Color.Success"
                             Class="no-wrap">
                        <GameCurrencyLabel
                            Model="@(PriceDestinationTotal - PriceOriginTotal)"
                            Prefix="+"
                            Decimals="1"/>
                    </MudText>
                </MudStack>
            </div>
            <div style="grid-column: auto">
                <TradeRouteParty
                    Model="@Model.Destination"
                    Route="@Model"
                    Side="@TradeRouteParty.PartySide.Destination"
                    Class="flex-grow-1"
                    Spacing="6"
                    CompareToOrigin
                    AlignRight/>
            </div>
            <div style="grid-column: auto">
                <MudStack Spacing="2"
                          AlignItems="@AlignItems.Center"
                          Justify="@Justify.FlexEnd"
                          Class="h-100 mr-n2"
                          Row>
                    @if (ControlsContent is not null)
                    {
                        <MudDivider
                            FlexItem
                            Vertical/>

                        @ControlsContent(Model)
                    }
                </MudStack>
            </div>
        </div>
    </div>
</MudPaper>

@code
{

    private GameCurrency PriceOrigin
        => Model.Origin.Price;

    private GameCurrency PriceDestination
        => Model.Destination.Price;

    private int ScuReachable
        => Context?.ScuReachable(Model.Origin.CargoUnitsAvailable) ?? 0;

    private GameCurrency PriceOriginTotal
        => PriceOrigin * ScuReachable;

    private GameCurrency PriceDestinationTotal
        => PriceDestination * ScuReachable;

    [CascadingParameter]
    public TradeRouteSearchView.SearchContext? Context { get; set; }

    [Parameter]
    [EditorRequired]
    public required GameTradeRoute Model { get; set; }

    [Parameter]
    public RenderFragment<GameTradeRoute>? ControlsContent { get; set; }

}
