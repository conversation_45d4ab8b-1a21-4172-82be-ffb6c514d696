@using Arkanis.Overlay.Infrastructure.Helpers
<!--suppress CssUnusedSymbol, CssUnresolvedCustomProperty -->
<style>
    @@property --angle {
        syntax: @AngleProperty;
        initial-value: 0deg;
        inherits: false;
    }

    .badge-container {
        width: fit-content;
        height: 2rem;
        padding: 0;
        transition: height 0.35s ease-in-out;

        a {
        }

        &:hover {
            height: 4rem;

            & .badge {
                &::after, &::before {
                    padding: 2px;
                    height: 100%;
                    width: 100%;
                }

                &::before {
                    opacity: 0.5;
                }
            }
        }
    }

    /* Animation based on: https://www.coding2go.com/border-animation/ */
    .badge {
        position: relative;
        height: 100%;

        img {
            height: 100%;
            border-radius: 10px;
        }

        &::after, &::before {
            content: '';
            position: absolute;
            height: 95%;
            width: 95%;
            background-image: conic-gradient(from var(--angle), #ff4545, #49002a, #ff4545, #49002a, #ff4545);
            top: 50%;
            left: 50%;
            translate: -50% -50%;
            z-index: -1;
            padding: 0;
            border-radius: 10px;
            animation: 5s spin linear infinite;
            transition: padding 0.3s ease-in-out;
        }

        &::before {
            filter: blur(1.5rem);
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
    }

    @@keyframes spin {
        from {
            --angle: 0deg;
        }
        to {
            --angle: 360deg;
        }
    }

</style>

<div class="badge-container">
    <a href="@ExternalLinkHelper.GetUexLink("powered_by_badge")" target="_blank" rel="noopener noreferrer">
        <div class="badge">
            <img src="@LinkHelper.GetPathToAsset("/img/uex-api-badge-powered.png")"
                 alt="Powered by UEX API"/>
        </div>
    </a>
</div>

@code {

    private const string AngleProperty = "\"<angle>\"";

}
