@implements QuickAccessContainer.IEntry
@using Arkanis.Overlay.Domain.Abstractions.Game
@implements IAsyncDisposable
@inject IPriceProvider PriceProvider

<FocusRegion @ref="_focusRegion" Class="search-result-container">
    <MudPaper Class="w-100 search-result px-4 focus" tabindex="0">
        <KeyboardShortcutBadge Keys="@( [KeyboardKey.AltLeft, KeyboardShortcut])"
                               Origin="@Origin.TopLeft"
                               Class="w-100"
                               IsActive="@(KeyboardShortcutActive)"
                               OnKeyPress="@(() => context.SourceRegion.FocusAsync())">
            <MudStack Class="w-100 py-2"
                      Justify="@Justify.FlexStart"
                      AlignItems="@AlignItems.Center"
                      Row>

                <MudIcon Icon="@IconPicker.PickIconFor(Model.EntityCategory)"></MudIcon>
                <MudDivider Vertical="true" FlexItem="true"></MudDivider>

                <div class="my-n2">
                    <GameEntityNameLabel Model="@Model.Name"/>
                </div>

                <GameEntitySearchDetails Model="@Model"/>
                <GameEntitySearchControls
                    Model="@Model"
                    IsFocused="@context.ContainsFocus"/>
            </MudStack>
        </KeyboardShortcutBadge>
        @if (RenderContext)
        {
            <div class="focus-animate-height"
                 style="height: calc(3 * 62px + 3 * 12px);"> @* 3 * <result item height> + 3 * <item spacing> *@
                <MudDivider Class="mb-2"/>
                <MudGrid Class="my-n2"
                         Spacing="2"
                         Style="height: 100%; scrollbar-width: thin;">
                    @if (Model is IGamePurchasable)
                    {
                        <MudItem xs="6" Class="pt-0 h-100">
                            <PriceTagTable Models="@PurchasePriceTags">
                                <CaptionContent>
                                    All available <b>purchase</b> prices
                                </CaptionContent>
                            </PriceTagTable>
                        </MudItem>
                    }
                    @if (Model is IGameSellable)
                    {
                        <MudItem xs="6" Class="pt-0 h-100">
                            <PriceTagTable Models="@SalePriceTags">
                                <CaptionContent>
                                    All available <b>sale</b> prices
                                </CaptionContent>
                            </PriceTagTable>
                        </MudItem>
                    }
                </MudGrid>
            </div>
        }
    </MudPaper>
</FocusRegion>

@code
{

    private bool RenderContext
        => Model is IGamePurchasable or IGameSellable;

    private KeyboardKey KeyboardShortcut
        => _itemInfo?.IsVisible switch
        {
            true => _itemInfo switch
            {
                { Index: < 9 } => KeyboardKey.Digit1 + _itemInfo!.Index,
                { Index: 9 } => KeyboardKey.Digit0,
                _ => KeyboardKey.Unknown,
            },
            _ => KeyboardKey.Unknown,
        };

    private bool KeyboardShortcutActive
        => KeyboardShortcut is not KeyboardKey.Unknown && _focusRegion?.HasFocus != true;

    private FocusRegion? _focusRegion;
    private QuickAccessContainer.EntryInfo? _itemInfo;
    private QuickAccessContainer.EntryRegistration? _registration;

    private ICollection<PriceTag> PurchasePriceTags { get; set; } = [];
    private ICollection<PriceTag> SalePriceTags { get; set; } = [];

    public ElementReference PageElement
        => _focusRegion?.ContainerElement ?? default;

    [CascadingParameter]
    public QuickAccessContainer? Container { get; set; }

    [Parameter]
    [EditorRequired]
    public required IGameEntity Model { get; set; }

    [Parameter]
    [EditorRequired]
    public required int ItemIndex { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (Model is IGamePurchasable purchasable)
        {
            PurchasePriceTags = await PriceProvider.GetPriceTagsWithinAsync(purchasable, null);
        }

        if (Model is IGameSellable sellable)
        {
            SalePriceTags = await PriceProvider.GetPriceTagsWithinAsync(sellable, null);
            SalePriceTags = SalePriceTags.Reverse().ToArray();
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            RegisterToQuickAccessContainer();
        }
    }

    private void RegisterToQuickAccessContainer()
    {
        if (Container is not null && _focusRegion is not null)
        {
            _registration = Container.Register(this);
        }
    }

    public async Task OnQuickAccessInfoChanged(QuickAccessContainer.EntryInfo info)
    {
        if (_itemInfo == info)
        {
            // prevent unnecessary re-rendering when the state is equivalent
            return;
        }

        _itemInfo = info;
        await InvokeAsync(StateHasChanged);
    }

    public ValueTask DisposeAsync()
    {
        if (_registration != null)
        {
            _registration.Dispose();
            _registration = null;
        }

        return ValueTask.CompletedTask;
    }
}
