@inherits TradeRunStageComponent<TradeRun.TerminalSaleStage>

<MudStep Title="Sell cargo"
         Completed="@IsCompleted"
         Disabled="@IsDisabled">
    <MudStack AlignItems="@AlignItems.Center"
              Justify="@Justify.Center"
              Spacing="6">
        <MudStack AlignItems="AlignItems.Center"
                  Spacing="0">
            <MudText Typo="@Typo.h5">
                Sale
            </MudText>
            <MudText Typo="@Typo.body2">
                Sell the transported commodity
            </MudText>
        </MudStack>
        <MudStack AlignItems="@AlignItems.Center"
                  Spacing="6"
                  Row>
            <GameEntityNameLabel
                Model="@(Stage.Quantity.Reference.Entity.Name)"/>
            <MudText>
                at
            </MudText>
            <GameEntityNameLabel
                Model="@(Stage.Terminal.Name)"/>
        </MudStack>
        <MudGrid Spacing="2" Style="min-width: 400px; max-width: 75%;">
            <MudItem xs="12" sm="6" md="4">
                <MudNumericField
                    Label="Quantity sold"
                    HelperText="in SCUs"
                    Format="N0"
                    @bind-Value="@Stage.Quantity.Amount"
                    @bind-Value:after="@UpdateStageAsync"
                    Min="0"
                    Max="@UnsoldAmount.Amount"
                    Required/>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudNumericField
                    Label="Sale price (per SCU)"
                    Format="N0"
                    @bind-Value="@Stage.PricePerUnit.Amount"
                    @bind-Value:after="@UpdateStageAsync"
                    Min="1"
                    Required/>
            </MudItem>
            <MudItem xs="12" md="4">
                <MudStack AlignItems="@AlignItems.End"
                          Justify="@Justify.Center"
                          Class="h-100">
                    <MudText Typo="@Typo.h6"
                             Color="@Color.Success">
                        =
                        <GameCurrencyLabel
                            Model="@Stage.PriceTotal"/>
                    </MudText>
                </MudStack>
            </MudItem>
            <MudItem xs="12" sm="6" md="5">
                <MudSelect T="TerminalInventoryStatus"
                           Label="New stock status"
                           @bind-Value="@Stage.UserSourcedData.StockStatus"
                           HelperText="Stock status reported AFTER the sale">
                    @foreach (var status in Enum.GetValues<TerminalInventoryStatus>().Where(x => x is not TerminalInventoryStatus.Unknown))
                    {
                        <MudSelectItem Value="@status">
                            @status.Humanize()
                        </MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudNumericField
                    Label="New stock (SCU)"
                    Format="N0"
                    @bind-Value="@Stage.UserSourcedData.Stock.Amount"
                    HelperText="Stock reported AFTER the sale"
                    Min="0"/>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect T="GameContainerSize"
                           Label="Max. acceptable container"
                           @bind-Value="@Stage.UserSourcedData.MaxContainerSize">
                    @foreach (var status in Enum.GetValues<GameContainerSize>().Where(x => x is not GameContainerSize.Unknown))
                    {
                        <MudSelectItem Value="@status">
                            @status.Humanize()
                        </MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
        </MudGrid>
        <MudCheckBox @bind-Value="@Stage.UserSourcedData.UserConfirmed">
            <MudStack Spacing="0">
                <MudText>
                    I have verified the provided values
                </MudText>
                <MudText Typo="Typo.caption" Class="text-secondary">
                    By providing these values you are contributing to the accuracy of the trade routes for all players
                </MudText>
            </MudStack>
        </MudCheckBox>
        @if (Stage.Quantity.Amount == 0)
        {
            <MudButton
                StartIcon="@MaterialIcons.Filled.Error"
                OnClick="@OnSoldClick"
                Color="@Color.Error"
                Size="@Size.Large"
                Disabled="@IsDisabled">
                I have not made the sale
            </MudButton>
        }
        else if (Stage.Quantity < UnsoldAmount)
        {
            <MudButton
                StartIcon="@MaterialIcons.Filled.ShoppingCartCheckout"
                OnClick="@OnSoldClick"
                Color="@Color.Warning"
                Size="@Size.Large"
                Disabled="@IsDisabled">
                I have made partial sale
            </MudButton>
        }
        else
        {
            <MudButton
                StartIcon="@MaterialIcons.Filled.ShoppingCartCheckout"
                OnClick="@OnSoldClick"
                Color="@Color.Success"
                Size="@Size.Large"
                Disabled="@IsDisabled">
                I have made the sale
            </MudButton>
        }
    </MudStack>
</MudStep>

@code
{

    private bool IsCompleted
        => Stage.SoldAt is not null;

    private bool IsDisabled
        => IsCompleted || Stage.IsFinalized;

    private Quantity UnsoldAmount { get; set; } = Quantity.Zero;

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        UnsoldAmount = TradeRun.UnsoldQuantities
            .Where(x => x.Reference.EntityId == Stage.Quantity.Reference.EntityId)
            .SingleOrDefault(Quantity.Zero);
    }

    private async Task OnSoldClick()
    {
        Stage.SoldAt ??= DateTimeOffset.Now;
        await NextStepAsync();
    }
}
