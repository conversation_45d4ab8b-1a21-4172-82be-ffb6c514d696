@inherits TradeRunComponent

<TradeRunSummaryTemplate Events="@TradeRun.CreateEvents()">
    <MudStack Class="h-100 w-100 pa-6">
        <MudStack AlignItems="@AlignItems.Center"
                  Justify="@Justify.SpaceBetween"
                  Class="w-100"
                  Row>
            <MudText Typo="@Typo.h6">
                Trade run length
            </MudText>
            <MudText Typo="@Typo.h6" Color="@Color.Success">
                @(TradeRun.Length.Humanize())
            </MudText>
        </MudStack>
        <MudStack AlignItems="@AlignItems.Center"
                  Justify="@Justify.SpaceBetween"
                  Class="w-100"
                  Row>
            <MudText Typo="@Typo.h6">
                Investment
            </MudText>
            <GameCurrencyLabel
                Model="@TradeRun.CurrentInvestment"
                Typo="@Typo.h6"/>
        </MudStack>
        <MudStack AlignItems="@AlignItems.Center"
                  Justify="@Justify.SpaceBetween"
                  Class="w-100"
                  Row>
            <MudText Typo="@Typo.h6">
                Revenue
            </MudText>
            <GameCurrencyLabel
                Model="@TradeRun.CurrentRevenue"
                Typo="@Typo.h6"/>
        </MudStack>
        <MudStack AlignItems="@AlignItems.Center"
                  Justify="@Justify.SpaceBetween"
                  Class="w-100"
                  Row>
            <MudText Typo="@Typo.h6">
                Fees paid
            </MudText>
            <GameCurrencyLabel
                Model="@TradeRun.Fees"
                Typo="@Typo.h6"/>
        </MudStack>
        <MudDivider Class="flex-grow-0"/>

        <MudStack AlignItems="@AlignItems.Center"
                  Justify="@Justify.SpaceBetween"
                  Class="w-100"
                  Row>
            <MudText Typo="@Typo.h6">
                Trade run profit
            </MudText>
            <GameCurrencyLabel
                Model="@TradeRun.CurrentProfit"
                Typo="@Typo.h6"
                UseColour/>
        </MudStack>
        <MudStack AlignItems="@AlignItems.Center"
                  Justify="@Justify.SpaceBetween"
                  Class="w-100"
                  Row>
            <MudText Typo="@Typo.h6">
                Cargo sold
            </MudText>
            @if (TradeRun.AcquiredQuantities.ToList() is { Count: > 0 } acquiredQuantities)
            {
                <MudStack AlignItems="@AlignItems.Center"
                          Row>
                    @if (TradeRun.SoldQuantities.ToList() is { Count: > 0 } soldQuantities)
                    {
                        <MudText Typo="@Typo.h6" Color="@(TradeRun.HasUnsoldCargo ? Color.Warning : Color.Success)">
                            <QuantityAggregateLabel
                                Models="@soldQuantities"/>
                        </MudText>
                    }
                    else
                    {
                        <MudText Typo="@Typo.h6" Class="text-secondary">
                            0
                        </MudText>
                    }
                    <MudText Typo="@Typo.h6" Class="text-secondary">
                        /
                    </MudText>
                    <MudText Typo="@Typo.h6" Color="@Color.Success">
                        <QuantityAggregateLabel
                            Models="@acquiredQuantities"/>
                    </MudText>
                </MudStack>
            }
            else
            {
                <MudText Typo="@Typo.h6" Class="text-secondary">
                    N/A
                </MudText>
            }
        </MudStack>
        <MudSpacer/>
        @ControlsContent
    </MudStack>
</TradeRunSummaryTemplate>

@code
{

    [Parameter]
    public RenderFragment? ControlsContent { get; set; }

}
