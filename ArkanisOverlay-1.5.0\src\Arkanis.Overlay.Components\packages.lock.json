{"version": 2, "dependencies": {"net8.0": {"Blazor-Analytics": {"type": "Direct", "requested": "[3.12.0, )", "resolved": "3.12.0", "contentHash": "xtOzWLZvM6kMFqk9+LbQyq7jgV5QIQ1lc75xzXWuhhwkqQF0uadzeXgZaek4HHGsvDjheAaa/Y5FTe5oJK/CRQ==", "dependencies": {"Microsoft.AspNetCore.Components": "3.1.8", "Microsoft.AspNetCore.Components.Web": "3.1.8"}}, "MathEvaluator": {"type": "Direct", "requested": "[2.3.1, )", "resolved": "2.3.1", "contentHash": "ES/gH3gncB3IKWhWk1DT9yh/YWlFnNPEjnW4U1g82xmax6AExKfepSI1IX8AbDhuMdyo+RonrRGJF98wZMsN/Q==", "dependencies": {"MathTrigonometric": "1.2.0"}}, "Microsoft.AspNetCore.Components.Web": {"type": "Direct", "requested": "[8.0.16, )", "resolved": "8.0.16", "contentHash": "78mEw/L80FnfSVrUYsV6/+6luSBi8JI/dUNyu2xxSlkGiZ6A3JImcTGgIPhGRlTIKwmrn8yzsOE9S16cwoAJqA==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.16", "Microsoft.AspNetCore.Components.Forms": "8.0.16", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.16", "System.IO.Pipelines": "8.0.0"}}, "MudBlazor": {"type": "Direct", "requested": "[8.6.0, )", "resolved": "8.6.0", "contentHash": "vJROmWdTjiBmyiZBSbD8RcYVFfLbNMEQpKoCOMfY2628KbOLGVcnH1RBziHGyJH9pqc99UoCQS81XLm8X+49ug==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.12", "Microsoft.AspNetCore.Components.Web": "8.0.12", "Microsoft.Extensions.Localization": "8.0.12"}}, "Humanizer.Core": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw=="}, "Humanizer.Core.af": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "BoQHyu5le+xxKOw+/AUM7CLXneM/Bh3++0qh1u0+D95n6f9eGt9kNc8LcAHLIOwId7Sd5hiAaaav0Nimj3peNw==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.ar": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "3d1V10LDtmqg5bZjWkA/EkmGFeSfNBcyCH+TiHcHP+HGQQmRq3eBaLcLnOJbVQVn3Z6Ak8GOte4RX4kVCxQlFA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.az": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "8Z/tp9PdHr/K2Stve2Qs/7uqWPWLUK9D8sOZDNzyv42e20bSoJkHFn7SFoxhmaoVLJwku2jp6P7HuwrfkrP18Q==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.bg": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "S+hIEHicrOcbV2TBtyoPp1AVIGsBzlarOGThhQYCnP6QzEYo/5imtok6LMmhZeTnBFoKhM8yJqRfvJ5yqVQKSQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.bn-BD": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "U3bfj90tnUDRKlL1ZFlzhCHoVgpTcqUlTQxjvGCaFKb+734TTu3nkHUWVZltA1E/swTvimo/aXLtkxnLFrc0EQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.cs": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "jWrQkiCTy3L2u1T86cFkgijX6k7hoB0pdcFMWYaSZnm6rvG/XJE40tfhYyKhYYgIc1x9P2GO5AC7xXvFnFdqMQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.da": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "5o0rJyE/2wWUUphC79rgYDnif/21MKTTx9LIzRVz9cjCIVFrJ2bDyR2gapvI9D6fjoyvD1NAfkN18SHBsO8S9g==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.de": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "9JD/p+rqjb8f5RdZ3aEJqbjMYkbk4VFii2QDnnOdNo6ywEfg/A5YeOQ55CaBJmy7KvV4tOK4+qHJnX/tg3Z54A==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.el": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "Xmv6sTL5mqjOWGGpqY7bvbfK5RngaUHSa8fYDGSLyxY9mGdNbDcasnRnMOvi0SxJS9gAqBCn21Xi90n2SHZbFA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.es": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "e//OIAeMB7pjBV1HqqI4pM2Bcw3Jwgpyz9G5Fi4c+RJvhqFwztoWxW57PzTnNJE2lbhGGLQZihFZjsbTUsbczA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.fa": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "nzDOj1x0NgjXMjsQxrET21t1FbdoRYujzbmZoR8u8ou5CBWY1UNca0j6n/PEJR/iUbt4IxstpszRy41wL/BrpA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.fi-FI": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "Vnxxx4LUhp3AzowYi6lZLAA9Lh8UqkdwRh4IE2qDXiVpbo08rSbokATaEzFS+o+/jCNZBmoyyyph3vgmcSzhhQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.fr": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "2p4g0BYNzFS3u9SOIDByp2VClYKO0K1ecDV4BkB9EYdEPWfFODYnF+8CH8LpUrpxL2TuWo2fiFx/4Jcmrnkbpg==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.fr-BE": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "o6R3SerxCRn5Ij8nCihDNMGXlaJ/1AqefteAssgmU2qXYlSAGdhxmnrQAXZUDlE4YWt/XQ6VkNLtH7oMqsSPFQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.he": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "FPsAhy7Iw6hb+ZitLgYC26xNcgGAHXb0V823yFAzcyoL5ozM+DCJtYfDPYiOpsJhEZmKFTM9No0jUn1M89WGvg==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.hr": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "chnaD89yOlST142AMkAKLuzRcV5df3yyhDyRU5rypDiqrq2HN8y1UR3h1IicEAEtXLoOEQyjSAkAQ6QuXkn7aw==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.hu": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "hAfnaoF9LTGU/CmFdbnvugN4tIs8ppevVMe3e5bD24+tuKsggMc5hYta9aiydI8JH9JnuVmxvNI4DJee1tK05A==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.hy": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "sVIKxOiSBUb4gStRHo9XwwAg9w7TNvAXbjy176gyTtaTiZkcjr9aCPziUlYAF07oNz6SdwdC2mwJBGgvZ0Sl2g==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.id": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "4Zl3GTvk3a49Ia/WDNQ97eCupjjQRs2iCIZEQdmkiqyaLWttfb+cYXDMGthP42nufUL0SRsvBctN67oSpnXtsg==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.is": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "R67A9j/nNgcWzU7gZy1AJ07ABSLvogRbqOWvfRDn4q6hNdbg/mjGjZBp4qCTPnB2mHQQTCKo3oeCUayBCNIBCw==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.it": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "jYxGeN4XIKHVND02FZ+Woir3CUTyBhLsqxu9iqR/9BISArkMf1Px6i5pRZnvq4fc5Zn1qw71GKKoCaHDJBsLFw==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.ja": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "TM3ablFNoYx4cYJybmRgpDioHpiKSD7q0QtMrmpsqwtiiEsdW5zz/q4PolwAczFnvrKpN6nBXdjnPPKVet93ng==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.ko-KR": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "CtvwvK941k/U0r8PGdEuBEMdW6jv/rBiA9tUhakC7Zd2rA/HCnDcbr1DiNZ+/tRshnhzxy/qwmpY8h4qcAYCtQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.ku": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "vHmzXcVMe+LNrF9txpdHzpG7XJX65SiN9GQd/Zkt6gsGIIEeECHrkwCN5Jnlkddw2M/b0HS4SNxdR1GrSn7uCA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.lv": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "E1/KUVnYBS1bdOTMNDD7LV/jdoZv/fbWTLPtvwdMtSdqLyRTllv6PGM9xVQoFDYlpvVGtEl/09glCojPHw8ffA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.ms-MY": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "vX8oq9HnYmAF7bek4aGgGFJficHDRTLgp/EOiPv9mBZq0i4SA96qVMYSjJ2YTaxs7Eljqit7pfpE2nmBhY5Fnw==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.mt": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "pEgTBzUI9hzemF7xrIZigl44LidTUhNu4x/P6M9sAwZjkUF0mMkbpxKkaasOql7lLafKrnszs0xFfaxQyzeuZQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.nb": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "mbs3m6JJq53ssLqVPxNfqSdTxAcZN3njlG8yhJVx83XVedpTe1ECK9aCa8FKVOXv93Gl+yRHF82Hw9T9LWv2hw==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.nb-NO": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "AsJxrrVYmIMbKDGe8W6Z6//wKv9dhWH7RsTcEHSr4tQt/80pcNvLi0hgD3fqfTtg0tWKtgch2cLf4prorEV+5A==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.nl": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "24b0OUdzJxfoqiHPCtYnR5Y4l/s4Oh7KW7uDp+qX25NMAHLCGog2eRfA7p2kRJp8LvnynwwQxm2p534V9m55wQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.pl": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "17mJNYaBssENVZyQHduiq+bvdXS0nhZJGEXtPKoMhKv3GD//WO0mEfd9wjEBsWCSmWI7bjRqhCidxzN+YtJmsg==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.pt": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "8HB8qavcVp2la1GJX6t+G9nDYtylPKzyhxr9LAooIei9MnQvNsjEiIE4QvHoeDZ4weuQ9CsPg1c211XUMVEZ4A==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.ro": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "psXNOcA6R8fSHoQYhpBTtTTYiOk8OBoN3PKCEDgsJKIyeY5xuK81IBdGi77qGZMu/OwBRQjQCBMtPJb0f4O1+A==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.ru": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "zm245xUWrajSN2t9H7BTf84/2APbUkKlUJpcdgsvTdAysr1ag9fi1APu6JEok39RRBXDfNRVZHawQ/U8X0pSvQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.sk": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "Ncw24Vf3ioRnbU4MsMFHafkyYi8JOnTqvK741GftlQvAbULBoTz2+e7JByOaasqeSi0KfTXeegJO+5Wk1c0Mbw==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.sl": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "l8sUy4ciAIbVThWNL0atzTS2HWtv8qJrsGWNlqrEKmPwA4SdKolSqnTes9V89fyZTc2Q43jK8fgzVE2C7t009A==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.sr": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "rnNvhpkOrWEymy7R/MiFv7uef8YO5HuXDyvojZ7JpijHWA5dXuVXooCOiA/3E93fYa3pxDuG2OQe4M/olXbQ7w==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.sr-Latn": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "nuy/ykpk974F8ItoQMS00kJPr2dFNjOSjgzCwfysbu7+gjqHmbLcYs7G4kshLwdA4AsVncxp99LYeJgoh1JF5g==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.sv": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "E53+tpAG0RCp+cSSI7TfBPC+NnsEqUuoSV0sU+rWRXWr9MbRWx1+Zj02XMojqjGzHjjOrBFBBio6m74seFl0AA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.th-TH": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "eSevlJtvs1r4vQarNPfZ2kKDp/xMhuD00tVVzRXkSh1IAZbBJI/x2ydxUOwfK9bEwEp+YjvL1Djx2+kw7ziu7g==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.tr": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "rQ8N+o7yFcFqdbtu1mmbrXFi8TQ+uy+fVH9OPI0CI3Cu1om5hUU/GOMC3hXsTCI6d79y4XX+0HbnD7FT5khegA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.uk": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "2uEfujwXKNm6bdpukaLtEJD+04uUtQD65nSGCetA1fYNizItEaIBUboNfr3GzJxSMQotNwGVM3+nSn8jTd0VSg==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.uz-Cyrl-UZ": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "TD3ME2sprAvFqk9tkWrvSKx5XxEMlAn1sjk+cYClSWZlIMhQQ2Bp/w0VjX1Kc5oeKjxRAnR7vFcLUFLiZIDk9Q==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.uz-Latn-UZ": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "/kHAoF4g0GahnugZiEMpaHlxb+W6jCEbWIdsq9/I1k48ULOsl/J0pxZj93lXC3omGzVF1BTVIeAtv5fW06Phsg==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.vi": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "rsQNh9rmHMBtnsUUlJbShMsIMGflZtPmrMM6JNDw20nhsvqfrdcoDD8cMnLAbuSovtc3dP+swRmLQzKmXDTVPA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.zh-CN": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "uH2dWhrgugkCjDmduLdAFO9w1Mo0q07EuvM0QiIZCVm6FMCu/lGv2fpMu4GX+4HLZ6h5T2Pg9FIdDLCPN2a67w==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.zh-Hans": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "WH6IhJ8V1UBG7rZXQk3dZUoP2gsi8a0WkL8xL0sN6WGiv695s8nVcmab9tWz20ySQbuzp0UkSxUQFi5jJHIpOQ==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "Humanizer.Core.zh-Hant": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "VIXB7HCUC34OoaGnO3HJVtSv2/wljPhjV7eKH4+TFPgQdJj2lvHNKY41Dtg0Bphu7X5UaXFR4zrYYyo+GNOjbA==", "dependencies": {"Humanizer.Core": "[2.14.1]"}}, "MathTrigonometric": {"type": "Transitive", "resolved": "1.2.0", "contentHash": "5Mr4SCGevBHpYI2MBkqVtbwtiW/Nr/gi9P323NhgAToJP06DgcHDlKqZ5qfrV0VoQSNHuSaAz6uGCcJqdwsQdA=="}, "Microsoft.AspNetCore.Authorization": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "4msfwr7vdFQgQdmN54kqeY01GRG99sw2gTC8eWQ1eRxq6oOGNmGY0wIl9vk3xZN6CdIdXq2hwFSYGw2frfHLOw==", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.16", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.AspNetCore.Components": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "53foNz+R1KAe404RgmLxjpcqORZM23NLsS5Qe8zIIt02ThMPCa9IWC9WULwTr60g6kcTt1HUnExhrnvsVCl0SQ==", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.16", "Microsoft.AspNetCore.Components.Analyzers": "8.0.16"}}, "Microsoft.AspNetCore.Components.Analyzers": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "now9Taa3SMu7nWKe1sLExn86AVgo1NZej5u759UMvR74/OWzvDy2aN+uQE7twYW1mHTtR5ILjE7cX4L5WSm2Pg=="}, "Microsoft.AspNetCore.Components.Forms": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "xZaevE5BMss3yu5qDPoS5OzHWaWF9rIIGC+YsE4kj6R8ovUmVykHRmR34LWJEzhvs1WJxx3wvUC3itL6FFwwrA==", "dependencies": {"Microsoft.AspNetCore.Components": "8.0.16"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Transitive", "resolved": "2.3.0", "contentHash": "39r9PPrjA6s0blyFv5qarckjNkaHRA5B+3b53ybuGGNTXEj1/DStQJ4NWjFL6QTRQpL9zt7nDyKxZdJOlcnq+Q==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "2.3.0", "contentHash": "f10WUgcsKqrkmnz6gt8HeZ7kyKjYN30PO7cSic1lPtH7paPtnQqXPOveul/SIPI43PhRD4trttg4ywnrEmmJpA==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.AspNetCore.Metadata": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "sgavJHc/BLiGxFiw2frEoFZld+FXDQLyvS+3bzofSD1l2RXe6xkFpcWn1BFxJVqGq5w30hzdkwCbKYYTXax7OA=="}, "Microsoft.Data.Sqlite.Core": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "kkXtEh85rTxzBtNluE6jhaj47Fj7xR9gxo1TRp2xTmbqKSFKKHs6IVxuDSKmKtLuJ6sLJAyRQmt0h/+QvUJT2Q==", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}}, "Microsoft.EntityFrameworkCore": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "of6QttndQXVC6VOoWMDGGCkoo3I904R05g1GX55Qnw6EFXo6WuWe77/mNGbG1rlBbm7K0k68ila0AaUHHaVkiQ==", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.16", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.16", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}}, "Microsoft.EntityFrameworkCore.Abstractions": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "pjkQ8mhtmzekC56TlWvMbHAme5viF81COeVlGMfpCe5KpC5sRCa1s6qcITwEpcKKVW9JT4EqKH3xHWpoHJUn2A=="}, "Microsoft.EntityFrameworkCore.Analyzers": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "2MaPx6EBsmE8AswQv2KKQ8m2NeP3JnqAfLQi3UPrIVsxlkSkJWg5xU6ie4rctIOl872f2QzGog57NrucfKAFbw=="}, "Microsoft.EntityFrameworkCore.Relational": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "mZxHVSzGYmQWhvk98JEREmYke+NkBVsCL0q+yN23V3939qxXguSXBvnWc+zO9OfWGpYqzS80ES4I/H+xY33hoA==", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.16", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "0dqnweFn4wXm6MdCeE7KZq//V7GDIAp/YHoHNtAhuGS0GHx41t77eXId6Q3tXI0BPeZThoYRpz5aHcVsCPecoQ==", "dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.16", "Microsoft.EntityFrameworkCore.Relational": "8.0.16", "Microsoft.Extensions.DependencyModel": "8.0.2"}}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw=="}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "doVPCUUCY7c6LhBsEfiy3W1bvS7Mi6LkfQMS8nlC22jZWNxBv8VO8bdfeyvpYFst6Kxqk7HBC6lytmEoBssvSQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Localization": {"type": "Transitive", "resolved": "8.0.12", "contentHash": "Ef7P8kpJzX/khXB8oYxt3vFHXw48uWY+NirCrh8u8gokCHxr/Noc3OxME+Ji1ugqoMvUVSPu73mYctmUMDlOCA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Localization.Abstractions": "8.0.12", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Localization.Abstractions": {"type": "Transitive", "resolved": "8.0.12", "contentHash": "bgwe0gy9v12hr6gLAXIeEWaTYm295Nfmp7B/DLmS75GBJXvs4JHn7pi8gn3jXMNNkNIkx9iWG+pXKmLAmjdGZg=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "Microsoft.JSInterop": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "F0tvE3VYgr1bfFxatG7rpuO9XPSPPOIaZWAqDzX/BNgt359CtbC4rx9DqVt8j63s7u2SMrQNKYdNwbXEj/7REw=="}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.3.0", "contentHash": "/M0wVg6tJUOHutWD3BMOUVZAioJVXe0tCpFiovzv0T9T12TBf4MnaHP0efO8TCr1a6O9RZgQeZ9Gdark8L9XdA==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.6.0"}}, "SQLitePCLRaw.bundle_e_sqlite3": {"type": "Transitive", "resolved": "2.1.6", "contentHash": "BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}}, "SQLitePCLRaw.core": {"type": "Transitive", "resolved": "2.1.6", "contentHash": "wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "dependencies": {"System.Memory": "4.5.3"}}, "SQLitePCLRaw.lib.e_sqlite3": {"type": "Transitive", "resolved": "2.1.6", "contentHash": "2ObJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q=="}, "SQLitePCLRaw.provider.e_sqlite3": {"type": "Transitive", "resolved": "2.1.6", "contentHash": "PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}}, "System.Buffers": {"type": "Transitive", "resolved": "4.6.0", "contentHash": "lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA=="}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA=="}, "System.Linq.Async": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "cPtIuuH8TIjVHSi2ewwReWGW1PfChPE0LxPIDlfwVcLuTM9GANFTXiMB7k3aC4sk3f0cQU25LNKzx+jZMxijqw=="}, "System.Memory": {"type": "Transitive", "resolved": "4.5.3", "contentHash": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA=="}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ=="}, "arkanis.overlay.common": {"type": "Project", "dependencies": {"FluentValidation": "[12.0.0, )", "FluentValidation.DependencyInjectionExtensions": "[12.0.0, )", "FuzzySharp": "[2.0.2, )", "Humanizer": "[2.14.1, )", "Microsoft.Extensions.Configuration.Abstractions": "[8.0.0, )", "Microsoft.Extensions.Configuration.Binder": "[8.0.2, )", "Microsoft.Extensions.DependencyInjection.Abstractions": "[8.0.2, )", "Microsoft.Extensions.Hosting.Abstractions": "[8.0.1, )", "Microsoft.Extensions.Logging.Abstractions": "[8.0.3, )", "Microsoft.Extensions.Options.ConfigurationExtensions": "[8.0.0, )", "MoreAsyncLINQ": "[0.8.0, )", "NuGet.Versioning": "[6.14.0, )", "Riok.Mapperly": "[4.2.1, )", "morelinq": "[4.4.0, )"}}, "arkanis.overlay.domain": {"type": "Project", "dependencies": {"Arkanis.Overlay.Common": "[1.0.0, )", "MudBlazor.FontIcons.MaterialIcons": "[1.3.0, )", "MudBlazor.FontIcons.MaterialSymbols": "[1.3.0, )"}}, "arkanis.overlay.external.uex": {"type": "Project", "dependencies": {"Arkanis.Overlay.Common": "[1.0.0, )", "Microsoft.Extensions.Http": "[8.0.1, )"}}, "arkanis.overlay.infrastructure": {"type": "Project", "dependencies": {"Arkanis.Overlay.Domain": "[1.0.0, )", "Arkanis.Overlay.External.UEX": "[1.0.0, )", "Microsoft.AspNetCore.Http.Extensions": "[2.3.0, )", "Microsoft.Data.Sqlite": "[8.0.16, )", "Microsoft.EntityFrameworkCore.Sqlite": "[8.0.16, )", "Microsoft.Extensions.Http": "[8.0.1, )", "Quartz": "[3.14.0, )", "Quartz.Extensions.DependencyInjection": "[3.14.0, )", "Quartz.Extensions.Hosting": "[3.14.0, )", "Quartz.Jobs": "[3.14.0, )", "Scrutor": "[6.0.1, )"}}, "FluentValidation": {"type": "CentralTransitive", "requested": "[12.0.0, )", "resolved": "12.0.0", "contentHash": "8NVLxtMUXynRHJIX3Hn1ACovaqZIJASufXIIFkD0EUbcd5PmMsL1xUD5h548gCezJ5BzlITaR9CAMrGe29aWpA=="}, "FluentValidation.DependencyInjectionExtensions": {"type": "CentralTransitive", "requested": "[12.0.0, )", "resolved": "12.0.0", "contentHash": "B28fBRL1UjhGsBC8fwV6YBZosh+SiU1FxdD7l7p5dGPgRlVI7UnM+Lgzmg+unZtV1Zxzpaw96UY2MYfMaAd8cg==", "dependencies": {"FluentValidation": "12.0.0", "Microsoft.Extensions.Dependencyinjection.Abstractions": "2.1.0"}}, "FuzzySharp": {"type": "CentralTransitive", "requested": "[2.0.2, )", "resolved": "2.0.2", "contentHash": "sBKqWxw3g//peYxDZ8JipRlyPbIyBtgzqBVA5GqwHVeqtIrw75maGXAllztf+1aJhchD+drcQIgf2mFho8ZV8A=="}, "Humanizer": {"type": "CentralTransitive", "requested": "[2.14.1, )", "resolved": "2.14.1", "contentHash": "/FUTD3cEceAAmJSCPN9+J+VhGwmL/C12jvwlyM1DFXShEMsBzvLzLqSrJ2rb+k/W2znKw7JyflZgZpyE+tI7lA==", "dependencies": {"Humanizer.Core.af": "2.14.1", "Humanizer.Core.ar": "2.14.1", "Humanizer.Core.az": "2.14.1", "Humanizer.Core.bg": "2.14.1", "Humanizer.Core.bn-BD": "2.14.1", "Humanizer.Core.cs": "2.14.1", "Humanizer.Core.da": "2.14.1", "Humanizer.Core.de": "2.14.1", "Humanizer.Core.el": "2.14.1", "Humanizer.Core.es": "2.14.1", "Humanizer.Core.fa": "2.14.1", "Humanizer.Core.fi-FI": "2.14.1", "Humanizer.Core.fr": "2.14.1", "Humanizer.Core.fr-BE": "2.14.1", "Humanizer.Core.he": "2.14.1", "Humanizer.Core.hr": "2.14.1", "Humanizer.Core.hu": "2.14.1", "Humanizer.Core.hy": "2.14.1", "Humanizer.Core.id": "2.14.1", "Humanizer.Core.is": "2.14.1", "Humanizer.Core.it": "2.14.1", "Humanizer.Core.ja": "2.14.1", "Humanizer.Core.ko-KR": "2.14.1", "Humanizer.Core.ku": "2.14.1", "Humanizer.Core.lv": "2.14.1", "Humanizer.Core.ms-MY": "2.14.1", "Humanizer.Core.mt": "2.14.1", "Humanizer.Core.nb": "2.14.1", "Humanizer.Core.nb-NO": "2.14.1", "Humanizer.Core.nl": "2.14.1", "Humanizer.Core.pl": "2.14.1", "Humanizer.Core.pt": "2.14.1", "Humanizer.Core.ro": "2.14.1", "Humanizer.Core.ru": "2.14.1", "Humanizer.Core.sk": "2.14.1", "Humanizer.Core.sl": "2.14.1", "Humanizer.Core.sr": "2.14.1", "Humanizer.Core.sr-Latn": "2.14.1", "Humanizer.Core.sv": "2.14.1", "Humanizer.Core.th-TH": "2.14.1", "Humanizer.Core.tr": "2.14.1", "Humanizer.Core.uk": "2.14.1", "Humanizer.Core.uz-Cyrl-UZ": "2.14.1", "Humanizer.Core.uz-Latn-UZ": "2.14.1", "Humanizer.Core.vi": "2.14.1", "Humanizer.Core.zh-CN": "2.14.1", "Humanizer.Core.zh-Hans": "2.14.1", "Humanizer.Core.zh-Hant": "2.14.1"}}, "Microsoft.AspNetCore.Http.Extensions": {"type": "CentralTransitive", "requested": "[2.3.0, )", "resolved": "2.3.0", "contentHash": "EY2u/wFF5jsYwGXXswfQWrSsFPmiXsniAlUWo3rv/MGYf99ZFsENDnZcQP6W3c/+xQmQXq0NauzQ7jyy+o1LDQ==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Net.Http.Headers": "2.3.0", "System.Buffers": "4.6.0"}}, "Microsoft.Data.Sqlite": {"type": "CentralTransitive", "requested": "[8.0.16, )", "resolved": "8.0.16", "contentHash": "K4qA8VtCYnKX2lyRn1hJUgcCo1nxmlerBLMemlH84/DvsJqfnG1qEQeMOldd/yh/42D1wa5sK/oOHbkYzjdZiw==", "dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.16", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}}, "Microsoft.EntityFrameworkCore.Sqlite": {"type": "CentralTransitive", "requested": "[8.0.16, )", "resolved": "8.0.16", "contentHash": "n/Gy9u1Uu3xGDCDljxKov/Msbq2fuR/y9nzL2/NMd5CDwnBzg+tPQkQhSmnqd1AyNs38cNbZvmVkoKRSKYlmyg==", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "8.0.16", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}}, "Microsoft.Extensions.Configuration": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "CentralTransitive", "requested": "[8.0.2, )", "resolved": "8.0.2", "contentHash": "7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.2, )", "resolved": "8.0.2", "contentHash": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg=="}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Http": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "kDYeKJUzh0qeg/AI+nSr3ffthmXYQTEb0nS9qRC7YhSbbuN4M4NPbaB77AJwtkTnCV9XZ7qYj3dkZaNcyl73EA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Logging": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.3, )", "resolved": "8.0.3", "contentHash": "dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "MoreAsyncLINQ": {"type": "CentralTransitive", "requested": "[0.8.0, )", "resolved": "0.8.0", "contentHash": "28T50/WtvVK4k57VnLtXohaW+ySSM386lAkw+NE1/HuBhuCuRPTlJN5BZ2d/ILo77FCPCW6+eHXWvmz0V/O3pA==", "dependencies": {"System.Linq.Async": "5.0.0"}}, "morelinq": {"type": "CentralTransitive", "requested": "[4.4.0, )", "resolved": "4.4.0", "contentHash": "QX3bsK9oFeUXk8tFsc9NkI6NnCr8Ar/ex027p+ZZ/jdLCdX2RlryDtxUqZW5j45NVwn4E4Z4hzupsoMQd6Yxtg=="}, "MudBlazor.FontIcons.MaterialIcons": {"type": "CentralTransitive", "requested": "[1.3.0, )", "resolved": "1.3.0", "contentHash": "v4fPaZKINhb84hIWdVGa42k9JhputQ+EsH0oGK+6RpN5ComqQUo9mEbZaWSpYNIVGdo37cZXv5tz3KLRaX4s7g==", "dependencies": {"Microsoft.AspNetCore.Components.Web": "7.0.0"}}, "MudBlazor.FontIcons.MaterialSymbols": {"type": "CentralTransitive", "requested": "[1.3.0, )", "resolved": "1.3.0", "contentHash": "Sl2U2s157pWiD+GNkMI+yWeJdP41HoI+zJH/HTwNt59uMAfEF+lKCh3Ou4hOkkl/CU5fCi0ptzBkGRroYWxVQA==", "dependencies": {"Microsoft.AspNetCore.Components.Web": "7.0.0"}}, "NuGet.Versioning": {"type": "CentralTransitive", "requested": "[6.14.0, )", "resolved": "6.14.0", "contentHash": "4v4blkhCv8mpKtfx+z0G/X0daVCzdIaHSC51GkUspugi5JIMn2Bo8xm5PdZYF0U68gOBfz/+aPWMnpRd85Jbow=="}, "Quartz": {"type": "CentralTransitive", "requested": "[3.14.0, )", "resolved": "3.14.0", "contentHash": "j8mY6S0FWbtuEFbStUEZAkkGpZLbSuTT0us1w0DUQqhi+vwg/7XXS36TeH3o34zZoqYGyXvk6jrKnW/p6kS8sg==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1"}}, "Quartz.Extensions.DependencyInjection": {"type": "CentralTransitive", "requested": "[3.14.0, )", "resolved": "3.14.0", "contentHash": "CUY9ZaUsmnl8YxPcl3eXmg/1UVWv1NdyATKGaBjfxf8MYH36vgCoerv7aCSjM/oRbdiaBwNnPfNgHMzxUu1p1g==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Quartz": "3.14.0"}}, "Quartz.Extensions.Hosting": {"type": "CentralTransitive", "requested": "[3.14.0, )", "resolved": "3.14.0", "contentHash": "kbxBND5Nift+qwOud8cjywoLvjcQX+aLt6R7qQcQVg8w84NUNjTsjzZ5zIod9vz7ricSvIaQXGVbhpm7X6yVQA==", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Quartz.Extensions.DependencyInjection": "3.14.0"}}, "Quartz.Jobs": {"type": "CentralTransitive", "requested": "[3.14.0, )", "resolved": "3.14.0", "contentHash": "qOyqTZkG8cAjqOvCu5kUMTTO95wDh+ZC3JyS4PRvWr4dH/P7LoIJlDeblyOS5FOCGXHWUqdEwGgm2NGj1azUJA==", "dependencies": {"Quartz": "3.14.0"}}, "Riok.Mapperly": {"type": "CentralTransitive", "requested": "[4.2.1, )", "resolved": "4.2.1", "contentHash": "UZeQSieVlHr48t64J4k2s/lvbMeCXvzsXqV2A/0wyNdPpW8Cyn47+9mfWFJjouPxoSFfEhDbxg+WRbFIHvq4Zw=="}, "Scrutor": {"type": "CentralTransitive", "requested": "[6.0.1, )", "resolved": "6.0.1", "contentHash": "5xKT6ND5GqnFzwSaYozHCJe75GFL8sPy4yw/iRFqeBFGlmqPNFpOg1T9Q0Gl2h76Cklt0ZTg6Ypkri5iUBKXsA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.DependencyModel": "8.0.2"}}}}}