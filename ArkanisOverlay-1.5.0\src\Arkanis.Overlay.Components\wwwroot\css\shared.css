.w-100 {
    width: 100%;
}

.h-100 {
    height: 100%;
}

.text-primary {
    color: var(--mud-palette-text-primary);
}

.text-secondary {
    color: var(--mud-palette-text-secondary);
}

.text-disabled {
    color: var(--mud-palette-text-disabled);
}

.text-right {
    text-align: right !important;
}

.text-center {
    text-align: center !important;
}

.formatted {
    white-space: pre;
}

.no-wrap {
    white-space: nowrap;
}

.focus:focus,
.focus:focus-within {
    outline: 2px solid var(--mud-palette-primary);
}

.focus-animate-height {
    overflow: hidden;
    transition: height 100ms;
}

.focus:not(:focus):not(:focus-within) .focus-animate-height {
    height: 0 !important;
}

.focus:focus .focus-animate-height,
.focus:focus-within .focus-animate-height {
    transition: height 100ms;
}

.mud-badge {
    z-index: 100;
}

.mud-paper:not(.mud-paper-square) .mud-table-root:first-child tr:last-of-type > :first-child {
    border-top-left-radius: var(--mud-default-borderradius) !important;
}

.mud-paper:not(.mud-paper-square) .mud-table-root:first-child tr:last-of-type > :last-child {
    border-top-right-radius: var(--mud-default-borderradius) !important;
}

.mud-paper:not(.mud-paper-square) .mud-table-root tr:last-of-type > :first-child,
.mud-expansion-panels:not(.mud-expansion-panels-square) .mud-table-root tr:last-of-type > :first-child {
    border-bottom-left-radius: var(--mud-default-borderradius) !important;
}

.mud-paper:not(.mud-paper-square) .mud-table-root tr:last-of-type > :last-child,
.mud-expansion-panels:not(.mud-expansion-panels-square) .mud-table-root tr:last-of-type > :last-child {
    border-bottom-right-radius: var(--mud-default-borderradius) !important;
}

.flex-truncate {
    flex-shrink: 1;

    &, & * {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

@keyframes fadeInDown {
    0% {
        transform: translateY(-50%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInLeft {
    0% {
        transform: translateX(10%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.mud-popover .mud-list.mud-menu-list {
    overflow: visible;
}

.mud-data-grid .mud-table-row .mud-table-cell.mud-datagrid-group,
.mud-table .mud-table-row th.mud-table-cell {
    background-color: var(--mud-palette-surface);
}

.mud-table.bg-gray .mud-table-row .mud-table-cell:not(.mud-datagrid-group) {
    background-color: var(--mud-palette-background-gray);
}

.mud-table.bg-gray tfoot .mud-table-cell {
    background-color: var(--mud-palette-surface) !important;
}

.mud-table.bg-gray .mud-table-row.selected .mud-table-cell {
    background-color: var(--mud-palette-background);
}

.bg-gray .mud-timeline-item-dot {
    background-color: var(--mud-palette-background-gray);
}

.mud-data-grid .flex-row-reverse > .column-header {
    flex-direction: row-reverse;
}

:not(:hover) * .visible-on-parent-hover {
    display: none !important;
}

.hover-parent:hover .visible-on-parent-hover {
    display: initial !important;
}

.hover-parent:hover .hidden-on-parent-hover {
    display: none !important;
}

.mud-tabs {
    .mud-chip.mud-chip-size-small {
        margin: 0;
        height: 20px;
    }
}

.mud-tabs.with-badges {
    .mud-tabs-tabbar-content {
        overflow: visible !important;
    }

    .mud-tabs-tabbar-wrapper {
        display: flex !important;
        width: 100% !important;
        transform: none !important;
    }

    .mud-tabs-tabbar-wrapper .mud-badge-root {
        width: 100%;
    }

    .mud-tabs-tabbar-wrapper > * {
        flex: 1;
    }
}

.mud-expand-panel.mud-panel-expanded.mud-disabled {
    .mud-icon-root,
    .mud-svg-icon,
    .mud-icon-default {
        color: inherit;
    }
}

.game-entity-name {
    .header {
        height: 16px;
    }

    .footer {
        height: 18px;
        max-width: 50vw;
        overflow: hidden;
    }

    .primary .code {
        height: 18px;
    }

    .primary {
        height: 56px;
    }

    .primary:not(:last-child),
    .primary:not(:first-child) {
        height: 40px;
    }
}

.width-min-content {
    max-width: min-content !important;
    width: min-content !important;
}
