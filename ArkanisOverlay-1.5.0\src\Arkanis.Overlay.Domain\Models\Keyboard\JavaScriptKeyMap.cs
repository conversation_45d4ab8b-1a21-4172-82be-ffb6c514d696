namespace Arkanis.Overlay.Domain.Models.Keyboard;

/// <remarks>
///     Based on https://github.com/taublast/DrawnUi/blob/main/src/Engine/Maui/Features/Keyboard/MauiKeys.cs
/// </remarks>
public static class JavaScriptKeyMap
{
    private const int UnknownKeyCode = -1;

    private static readonly Dictionary<string, KeyboardKey> StringToEnum = new()
    {
        ["Backspace"] = KeyboardKey.Backspace,
        ["Tab"] = KeyboardKey.Tab,
        ["Enter"] = KeyboardKey.Enter,
        ["ShiftLeft"] = KeyboardKey.ShiftLeft,
        ["ShiftRight"] = KeyboardKey.ShiftRight,
        ["ControlLeft"] = KeyboardKey.ControlLeft,
        ["ControlRight"] = KeyboardKey.ControlRight,
        ["AltLeft"] = KeyboardKey.AltLeft,
        ["AltRight"] = KeyboardKey.AltRight,
        ["Pause"] = KeyboardKey.Pause,
        ["CapsLock"] = KeyboardKey.CapsLock,
        ["Escape"] = KeyboardKey.Escape,
        ["Space"] = KeyboardKey.Space,
        ["PageUp"] = KeyboardKey.PageUp,
        ["PageDown"] = KeyboardKey.PageDown,
        ["End"] = KeyboardKey.End,
        ["Home"] = KeyboardKey.Home,
        ["ArrowLeft"] = KeyboardKey.ArrowLeft,
        ["ArrowUp"] = KeyboardKey.ArrowUp,
        ["ArrowRight"] = KeyboardKey.ArrowRight,
        ["ArrowDown"] = KeyboardKey.ArrowDown,
        ["PrintScreen"] = KeyboardKey.PrintScreen,
        ["Insert"] = KeyboardKey.Insert,
        ["Delete"] = KeyboardKey.Delete,
        ["Digit0"] = KeyboardKey.Digit0,
        ["Digit1"] = KeyboardKey.Digit1,
        ["Digit2"] = KeyboardKey.Digit2,
        ["Digit3"] = KeyboardKey.Digit3,
        ["Digit4"] = KeyboardKey.Digit4,
        ["Digit5"] = KeyboardKey.Digit5,
        ["Digit6"] = KeyboardKey.Digit6,
        ["Digit7"] = KeyboardKey.Digit7,
        ["Digit8"] = KeyboardKey.Digit8,
        ["Digit9"] = KeyboardKey.Digit9,
        ["KeyA"] = KeyboardKey.KeyA,
        ["KeyB"] = KeyboardKey.KeyB,
        ["KeyC"] = KeyboardKey.KeyC,
        ["KeyD"] = KeyboardKey.KeyD,
        ["KeyE"] = KeyboardKey.KeyE,
        ["KeyF"] = KeyboardKey.KeyF,
        ["KeyG"] = KeyboardKey.KeyG,
        ["KeyH"] = KeyboardKey.KeyH,
        ["KeyI"] = KeyboardKey.KeyI,
        ["KeyJ"] = KeyboardKey.KeyJ,
        ["KeyK"] = KeyboardKey.KeyK,
        ["KeyL"] = KeyboardKey.KeyL,
        ["KeyM"] = KeyboardKey.KeyM,
        ["KeyN"] = KeyboardKey.KeyN,
        ["KeyO"] = KeyboardKey.KeyO,
        ["KeyP"] = KeyboardKey.KeyP,
        ["KeyQ"] = KeyboardKey.KeyQ,
        ["KeyR"] = KeyboardKey.KeyR,
        ["KeyS"] = KeyboardKey.KeyS,
        ["KeyT"] = KeyboardKey.KeyT,
        ["KeyU"] = KeyboardKey.KeyU,
        ["KeyV"] = KeyboardKey.KeyV,
        ["KeyW"] = KeyboardKey.KeyW,
        ["KeyX"] = KeyboardKey.KeyX,
        ["KeyY"] = KeyboardKey.KeyY,
        ["KeyZ"] = KeyboardKey.KeyZ,
        ["MetaLeft"] = KeyboardKey.MetaLeft,
        ["MetaRight"] = KeyboardKey.MetaRight,
        ["ContextMenu"] = KeyboardKey.ContextMenu,
        ["Numpad0"] = KeyboardKey.Numpad0,
        ["Numpad1"] = KeyboardKey.Numpad1,
        ["Numpad2"] = KeyboardKey.Numpad2,
        ["Numpad3"] = KeyboardKey.Numpad3,
        ["Numpad4"] = KeyboardKey.Numpad4,
        ["Numpad5"] = KeyboardKey.Numpad5,
        ["Numpad6"] = KeyboardKey.Numpad6,
        ["Numpad7"] = KeyboardKey.Numpad7,
        ["Numpad8"] = KeyboardKey.Numpad8,
        ["Numpad9"] = KeyboardKey.Numpad9,
        ["NumpadMultiply"] = KeyboardKey.NumpadMultiply,
        ["NumpadAdd"] = KeyboardKey.NumpadAdd,
        ["NumpadSubtract"] = KeyboardKey.NumpadSubtract,
        ["NumpadDecimal"] = KeyboardKey.NumpadDecimal,
        ["NumpadDivide"] = KeyboardKey.NumpadDivide,
        ["F1"] = KeyboardKey.F1,
        ["F2"] = KeyboardKey.F2,
        ["F3"] = KeyboardKey.F3,
        ["F4"] = KeyboardKey.F4,
        ["F5"] = KeyboardKey.F5,
        ["F6"] = KeyboardKey.F6,
        ["F7"] = KeyboardKey.F7,
        ["F8"] = KeyboardKey.F8,
        ["F9"] = KeyboardKey.F9,
        ["F10"] = KeyboardKey.F10,
        ["F11"] = KeyboardKey.F11,
        ["F12"] = KeyboardKey.F12,
        ["F13"] = KeyboardKey.F13,
        ["F14"] = KeyboardKey.F14,
        ["F15"] = KeyboardKey.F15,
        ["F16"] = KeyboardKey.F16,
        ["F17"] = KeyboardKey.F17,
        ["F18"] = KeyboardKey.F18,
        ["F19"] = KeyboardKey.F19,
        ["F20"] = KeyboardKey.F20,
        ["F21"] = KeyboardKey.F21,
        ["F22"] = KeyboardKey.F22,
        ["F23"] = KeyboardKey.F23,
        ["F24"] = KeyboardKey.F24,
        ["NumLock"] = KeyboardKey.NumLock,
        ["ScrollLock"] = KeyboardKey.ScrollLock,
        ["AudioVolumeMute"] = KeyboardKey.AudioVolumeMute,
        ["AudioVolumeDown"] = KeyboardKey.AudioVolumeDown,
        ["AudioVolumeUp"] = KeyboardKey.AudioVolumeUp,
        ["LaunchMediaPlayer"] = KeyboardKey.LaunchMediaPlayer,
        ["LaunchApplication1"] = KeyboardKey.LaunchApplication1,
        ["LaunchApplication2"] = KeyboardKey.LaunchApplication2,
        ["Semicolon"] = KeyboardKey.Semicolon,
        ["Equal"] = KeyboardKey.Equal,
        ["Comma"] = KeyboardKey.Comma,
        ["Minus"] = KeyboardKey.Minus,
        ["Period"] = KeyboardKey.Period,
        ["Slash"] = KeyboardKey.Slash,
        ["Backquote"] = KeyboardKey.Backquote,
        ["IntBackslash"] = KeyboardKey.IntBackslash,
        ["BracketLeft"] = KeyboardKey.BracketLeft,
        ["Backslash"] = KeyboardKey.Backslash,
        ["BracketRight"] = KeyboardKey.BracketRight,
        ["Quote"] = KeyboardKey.Quote,
    };

    private static readonly Dictionary<KeyboardKey, int> EnumToCode = new()
    {
        [KeyboardKey.Backspace] = 8,
        [KeyboardKey.Tab] = 9,
        [KeyboardKey.Enter] = 13,
        [KeyboardKey.ShiftLeft] = 16,
        [KeyboardKey.ShiftRight] = 16,
        [KeyboardKey.ControlLeft] = 17,
        [KeyboardKey.ControlRight] = 17,
        [KeyboardKey.AltLeft] = 18,
        [KeyboardKey.AltRight] = 18,
        [KeyboardKey.Pause] = 19,
        [KeyboardKey.CapsLock] = 20,
        [KeyboardKey.Escape] = 27,
        [KeyboardKey.Space] = 32,
        [KeyboardKey.PageUp] = 33,
        [KeyboardKey.PageDown] = 34,
        [KeyboardKey.End] = 35,
        [KeyboardKey.Home] = 36,
        [KeyboardKey.ArrowLeft] = 37,
        [KeyboardKey.ArrowUp] = 38,
        [KeyboardKey.ArrowRight] = 39,
        [KeyboardKey.ArrowDown] = 40,
        [KeyboardKey.PrintScreen] = 44,
        [KeyboardKey.Insert] = 45,
        [KeyboardKey.Delete] = 46,
        [KeyboardKey.Digit0] = 48,
        [KeyboardKey.Digit1] = 49,
        [KeyboardKey.Digit2] = 50,
        [KeyboardKey.Digit3] = 51,
        [KeyboardKey.Digit4] = 52,
        [KeyboardKey.Digit5] = 53,
        [KeyboardKey.Digit6] = 54,
        [KeyboardKey.Digit7] = 55,
        [KeyboardKey.Digit8] = 56,
        [KeyboardKey.Digit9] = 57,
        [KeyboardKey.KeyA] = 65,
        [KeyboardKey.KeyB] = 66,
        [KeyboardKey.KeyC] = 67,
        [KeyboardKey.KeyD] = 68,
        [KeyboardKey.KeyE] = 69,
        [KeyboardKey.KeyF] = 70,
        [KeyboardKey.KeyG] = 71,
        [KeyboardKey.KeyH] = 72,
        [KeyboardKey.KeyI] = 73,
        [KeyboardKey.KeyJ] = 74,
        [KeyboardKey.KeyK] = 75,
        [KeyboardKey.KeyL] = 76,
        [KeyboardKey.KeyM] = 77,
        [KeyboardKey.KeyN] = 78,
        [KeyboardKey.KeyO] = 79,
        [KeyboardKey.KeyP] = 80,
        [KeyboardKey.KeyQ] = 81,
        [KeyboardKey.KeyR] = 82,
        [KeyboardKey.KeyS] = 83,
        [KeyboardKey.KeyT] = 84,
        [KeyboardKey.KeyU] = 85,
        [KeyboardKey.KeyV] = 86,
        [KeyboardKey.KeyW] = 87,
        [KeyboardKey.KeyX] = 88,
        [KeyboardKey.KeyY] = 89,
        [KeyboardKey.KeyZ] = 90,
        [KeyboardKey.MetaLeft] = 91,
        [KeyboardKey.MetaRight] = 92,
        [KeyboardKey.ContextMenu] = 93,
        [KeyboardKey.Numpad0] = 96,
        [KeyboardKey.Numpad1] = 97,
        [KeyboardKey.Numpad2] = 98,
        [KeyboardKey.Numpad3] = 99,
        [KeyboardKey.Numpad4] = 100,
        [KeyboardKey.Numpad5] = 101,
        [KeyboardKey.Numpad6] = 102,
        [KeyboardKey.Numpad7] = 103,
        [KeyboardKey.Numpad8] = 104,
        [KeyboardKey.Numpad9] = 105,
        [KeyboardKey.NumpadMultiply] = 106,
        [KeyboardKey.NumpadAdd] = 107,
        [KeyboardKey.NumpadSubtract] = 109,
        [KeyboardKey.NumpadDecimal] = 110,
        [KeyboardKey.NumpadDivide] = 111,
        [KeyboardKey.F1] = 112,
        [KeyboardKey.F2] = 113,
        [KeyboardKey.F3] = 114,
        [KeyboardKey.F4] = 115,
        [KeyboardKey.F5] = 116,
        [KeyboardKey.F6] = 117,
        [KeyboardKey.F7] = 118,
        [KeyboardKey.F8] = 119,
        [KeyboardKey.F9] = 120,
        [KeyboardKey.F10] = 121,
        [KeyboardKey.F11] = 122,
        [KeyboardKey.F12] = 123,
        [KeyboardKey.F13] = 124,
        [KeyboardKey.F14] = 125,
        [KeyboardKey.F15] = 126,
        [KeyboardKey.F16] = 127,
        [KeyboardKey.F17] = 128,
        [KeyboardKey.F18] = 129,
        [KeyboardKey.F19] = 130,
        [KeyboardKey.F20] = 131,
        [KeyboardKey.F21] = 132,
        [KeyboardKey.F22] = 133,
        [KeyboardKey.F23] = 134,
        [KeyboardKey.F24] = 135,
        [KeyboardKey.NumLock] = 144,
        [KeyboardKey.ScrollLock] = 145,
        [KeyboardKey.AudioVolumeMute] = 173,
        [KeyboardKey.AudioVolumeDown] = 174,
        [KeyboardKey.AudioVolumeUp] = 175,
        [KeyboardKey.LaunchMediaPlayer] = 181,
        [KeyboardKey.LaunchApplication1] = 182,
        [KeyboardKey.LaunchApplication2] = 183,
        [KeyboardKey.Semicolon] = 186,
        [KeyboardKey.Equal] = 187,
        [KeyboardKey.Comma] = 188,
        [KeyboardKey.Minus] = 189,
        [KeyboardKey.Period] = 190,
        [KeyboardKey.Slash] = 191,
        [KeyboardKey.Backquote] = 192,
        [KeyboardKey.BracketLeft] = 219,
        [KeyboardKey.Backslash] = 220,
        [KeyboardKey.BracketRight] = 221,
        [KeyboardKey.Quote] = 222,
    };

    private static readonly Dictionary<int, KeyboardKey> CodeToEnum = new()
    {
        [8] = KeyboardKey.Backspace,
        [9] = KeyboardKey.Tab,
        [13] = KeyboardKey.Enter,
        [16] = KeyboardKey.ShiftLeft, // or JavaKeyCode.ShiftRight
        [17] = KeyboardKey.ControlLeft, // or JavaKeyCode.ControlRight
        [18] = KeyboardKey.AltLeft, // or JavaKeyCode.AltRight
        [19] = KeyboardKey.Pause,
        [20] = KeyboardKey.CapsLock,
        [27] = KeyboardKey.Escape,
        [32] = KeyboardKey.Space,
        [33] = KeyboardKey.PageUp,
        [34] = KeyboardKey.PageDown,
        [35] = KeyboardKey.End,
        [36] = KeyboardKey.Home,
        [37] = KeyboardKey.ArrowLeft,
        [38] = KeyboardKey.ArrowUp,
        [39] = KeyboardKey.ArrowRight,
        [40] = KeyboardKey.ArrowDown,
        [44] = KeyboardKey.PrintScreen,
        [45] = KeyboardKey.Insert,
        [46] = KeyboardKey.Delete,
        [48] = KeyboardKey.Digit0,
        [49] = KeyboardKey.Digit1,
        [50] = KeyboardKey.Digit2,
        [51] = KeyboardKey.Digit3,
        [52] = KeyboardKey.Digit4,
        [53] = KeyboardKey.Digit5,
        [54] = KeyboardKey.Digit6,
        [55] = KeyboardKey.Digit7,
        [56] = KeyboardKey.Digit8,
        [57] = KeyboardKey.Digit9,
        [65] = KeyboardKey.KeyA,
        [66] = KeyboardKey.KeyB,
        [67] = KeyboardKey.KeyC,
        [68] = KeyboardKey.KeyD,
        [69] = KeyboardKey.KeyE,
        [70] = KeyboardKey.KeyF,
        [71] = KeyboardKey.KeyG,
        [72] = KeyboardKey.KeyH,
        [73] = KeyboardKey.KeyI,
        [74] = KeyboardKey.KeyJ,
        [75] = KeyboardKey.KeyK,
        [76] = KeyboardKey.KeyL,
        [77] = KeyboardKey.KeyM,
        [78] = KeyboardKey.KeyN,
        [79] = KeyboardKey.KeyO,
        [80] = KeyboardKey.KeyP,
        [81] = KeyboardKey.KeyQ,
        [82] = KeyboardKey.KeyR,
        [83] = KeyboardKey.KeyS,
        [84] = KeyboardKey.KeyT,
        [85] = KeyboardKey.KeyU,
        [86] = KeyboardKey.KeyV,
        [87] = KeyboardKey.KeyW,
        [88] = KeyboardKey.KeyX,
        [89] = KeyboardKey.KeyY,
        [90] = KeyboardKey.KeyZ,
        [91] = KeyboardKey.MetaLeft,
        [92] = KeyboardKey.MetaRight,
        [93] = KeyboardKey.ContextMenu,
        [96] = KeyboardKey.Numpad0,
        [97] = KeyboardKey.Numpad1,
        [98] = KeyboardKey.Numpad2,
        [99] = KeyboardKey.Numpad3,
        [100] = KeyboardKey.Numpad4,
        [101] = KeyboardKey.Numpad5,
        [102] = KeyboardKey.Numpad6,
        [103] = KeyboardKey.Numpad7,
        [104] = KeyboardKey.Numpad8,
        [105] = KeyboardKey.Numpad9,
        [106] = KeyboardKey.NumpadMultiply,
        [107] = KeyboardKey.NumpadAdd,
        [109] = KeyboardKey.NumpadSubtract,
        [110] = KeyboardKey.NumpadDecimal,
        [111] = KeyboardKey.NumpadDivide,
        [112] = KeyboardKey.F1,
        [113] = KeyboardKey.F2,
        [114] = KeyboardKey.F3,
        [115] = KeyboardKey.F4,
        [116] = KeyboardKey.F5,
        [117] = KeyboardKey.F6,
        [118] = KeyboardKey.F7,
        [119] = KeyboardKey.F8,
        [120] = KeyboardKey.F9,
        [121] = KeyboardKey.F10,
        [122] = KeyboardKey.F11,
        [123] = KeyboardKey.F12,
        [124] = KeyboardKey.F13,
        [125] = KeyboardKey.F14,
        [126] = KeyboardKey.F15,
        [127] = KeyboardKey.F16,
        [128] = KeyboardKey.F17,
        [129] = KeyboardKey.F18,
        [130] = KeyboardKey.F19,
        [131] = KeyboardKey.F20,
        [132] = KeyboardKey.F21,
        [133] = KeyboardKey.F22,
        [134] = KeyboardKey.F23,
        [135] = KeyboardKey.F24,
        [144] = KeyboardKey.NumLock,
        [145] = KeyboardKey.ScrollLock,
        [173] = KeyboardKey.AudioVolumeMute,
        [174] = KeyboardKey.AudioVolumeDown,
        [175] = KeyboardKey.AudioVolumeUp,
        [181] = KeyboardKey.LaunchMediaPlayer,
        [182] = KeyboardKey.LaunchApplication1,
        [183] = KeyboardKey.LaunchApplication2,
        [186] = KeyboardKey.Semicolon,
        [187] = KeyboardKey.Equal,
        [188] = KeyboardKey.Comma,
        [189] = KeyboardKey.Minus,
        [190] = KeyboardKey.Period,
        [191] = KeyboardKey.Slash,
        [192] = KeyboardKey.Backquote,
        [219] = KeyboardKey.BracketLeft,
        [220] = KeyboardKey.Backslash,
        [221] = KeyboardKey.BracketRight,
        [222] = KeyboardKey.Quote,
    };

    public static KeyboardKey ToEnum(string stringCode)
        => StringToEnum.GetValueOrDefault(stringCode, KeyboardKey.Unknown);

    public static KeyboardKey ToEnum(int code)
        => CodeToEnum.GetValueOrDefault(code, KeyboardKey.Unknown);

    public static int ToCode(KeyboardKey key)
        => EnumToCode.GetValueOrDefault(key, UnknownKeyCode);
}
