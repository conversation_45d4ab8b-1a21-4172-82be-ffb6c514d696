namespace Arkanis.Overlay.Domain.Models.Keyboard;

/// <summary>
///     These are platform-independent.
///     They correspond to JavaScript keys.
/// </summary>
/// <remarks>
///     Appropriated from https://github.com/taublast/DrawnUi/blob/main/src/Engine/Maui/Features/Keyboard/MauiKeys.cs
/// </remarks>
public enum KeyboardKey
{
    Unknown,
    Backspace,
    Tab,
    Enter,
    ShiftLeft,
    ShiftRight,
    ControlLeft,
    ControlRight,
    AltLeft,
    AltRight,
    Pause,
    CapsLock,
    Escape,
    Space,
    PageUp,
    PageDown,
    End,
    Home,
    IntBackslash,
    ArrowLeft,
    ArrowUp,
    ArrowRight,
    ArrowDown,
    PrintScreen,
    Insert,
    Delete,
    Digit0,
    Digit1,
    Digit2,
    Digit3,
    Digit4,
    Digit5,
    Digit6,
    Digit7,
    Digit8,
    Digit9,
    KeyA,
    KeyB,
    KeyC,
    KeyD,
    KeyE,
    KeyF,
    KeyG,
    KeyH,
    KeyI,
    KeyJ,
    KeyK,
    KeyL,
    KeyM,
    KeyN,
    KeyO,
    KeyP,
    KeyQ,
    KeyR,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    KeyW,
    KeyX,
    KeyY,
    KeyZ,
    MetaLeft,
    MetaRight,
    ContextMenu,
    Numpad0,
    Numpad1,
    <PERSON>umpad2,
    Numpad3,
    Numpad4,
    Numpad5,
    Numpad6,
    Numpad7,
    Numpad8,
    Numpad9,
    NumpadMultiply,
    NumpadAdd,
    NumpadSubtract,
    NumpadDecimal,
    NumpadDivide,
    F1,
    F2,
    F3,
    F4,
    F5,
    F6,
    F7,
    F8,
    F9,
    F10,
    F11,
    F12,
    F13,
    F14,
    F15,
    F16,
    F17,
    F18,
    F19,
    F20,
    F21,
    F22,
    F23,
    F24,
    NumLock,
    ScrollLock,
    AudioVolumeMute,
    AudioVolumeDown,
    AudioVolumeUp,
    LaunchMediaPlayer,
    LaunchApplication1,
    LaunchApplication2,
    Semicolon,
    Equal,
    Comma,
    Minus,
    Period,
    Slash,
    Backquote,
    BracketLeft,
    Backslash,
    BracketRight,
    Quote,
}
