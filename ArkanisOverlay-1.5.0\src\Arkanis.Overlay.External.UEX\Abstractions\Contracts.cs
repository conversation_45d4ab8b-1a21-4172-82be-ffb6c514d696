//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

#nullable enable

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON>son()' hides inherited member '{dtoBase}.To<PERSON><PERSON>()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 612 // Disable "CS0612 '...' is obsolete"
#pragma warning disable 649 // Disable "CS0649 Field is never assigned to, and will always have its default value null"
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"
#pragma warning disable 3016 // Disable "CS3016 Arrays as attribute arguments is not CLS-compliant"
#pragma warning disable 8600 // Disable "CS8600 Converting null literal or possible null value to non-nullable type"
#pragma warning disable 8602 // Disable "CS8602 Dereference of a possibly null reference"
#pragma warning disable 8603 // Disable "CS8603 Possible null reference return"
#pragma warning disable 8604 // Disable "CS8604 Possible null reference argument for parameter"
#pragma warning disable 8625 // Disable "CS8625 Cannot convert null literal to non-nullable reference type"
#pragma warning disable 8765 // Disable "CS8765 Nullability of type of parameter doesn't match overridden member (possibly because of nullability attributes)."

namespace Arkanis.Overlay.External.UEX.Abstractions
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexGameApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Get a list of item and service categories.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCategoriesOkResponse>> GetCategoriesAsync(string? type = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Get attributes from categories (type 'item' only)
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCategoriesAttributesOkResponse>> GetCategoriesAttributesAsync(double? id_category = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve the list of cities within a star system.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCitiesOkResponse>> GetCitiesAsync(double? id_moon = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all companies in the Star Citizen universe.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCompaniesOkResponse>> GetCompaniesAsync(string? is_item_manufacturer = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of all known Star Citizen contacts (mission givers)
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetContactsOkResponse>> GetContactsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of all known Star Citizen contacts (mission givers)
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetContractsOkResponse>> GetContractsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all known Star Citizen factions
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFactionsOkResponse>> GetFactionsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain the Star Citizen versions currently operated by UEX. It may be out of sync with Star Citizen releases sometimes.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetGameVersionsOkResponse>> GetGameVersionsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all jump points in the game
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetJumpPointsOkResponse>> GetJumpPointsAsync(double? id_orbit_origin = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all known Star Citizen jurisdictions
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetJurisdictionsOkResponse>> GetJurisdictionsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all moons within a star system.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetMoonsOkResponse>> GetMoonsAsync(double? id_star_system = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all planets, planetoids and lagrange points orbiting a star.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetOrbitsOkResponse>> GetOrbitsAsync(double? id_star_system = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain the last orbital distances reported by Datarunners
        /// </summary>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse> GetOrbitsDistancesAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all outposts within a star system.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetOutpostsOkResponse>> GetOutpostsAsync(double? id_moon = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all planets within a star system.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetPlanetsOkResponse>> GetPlanetsAsync(double? id_star_system = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of points of interest
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetPoiOkResponse>> GetPoiAsync(double? id_outpost = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Output UEX dev notes
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetReleaseNotesOkResponse>> GetReleaseNotesAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all space stations within a star system.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetSpaceStationsOkResponse>> GetSpaceStationsAsync(double? id_moon = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all star systems in the Star Citizen universe.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetStarSystemsOkResponse>> GetStarSystemsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of all terminals in the game, including trade terminals, item terminals, vehicle rentals, and more.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetTerminalsOkResponse>> GetTerminalsAsync(double? id_outpost = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Estimate the distance (in gigameters) between two terminals within the Star Citizen universe.
        /// </summary>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse> GetTerminalsDistancesAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of Star Citizen vehicles, including spaceships and ground vehicles.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetVehiclesOkResponse>> GetVehiclesAsync(double? id_company = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexCommoditiesApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Get a list of all commodities covered by UEX.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesOkResponse>> GetCommoditiesAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of the latest commodities alerts
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesAlertsOkResponse>> GetCommoditiesAlertsAsync(double? id_commodity = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of average prices and stock data of a specific commodity in the last 15 days. (CAX Index)
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesAveragesOkResponse>> GetCommoditiesAveragesByCommodityAsync(string id_commodity, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByCommodityCodeAsync(string commodity_code, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByCommodityNameAsync(string commodity_name, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByCommoditySlugAsync(string commodity_slug, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByCommodityAsync(string id_commodity, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByTerminalAsync(string id_terminal, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByTerminalCodeAsync(string terminal_code, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByTerminalNameAsync(string terminal_name, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesOkResponse>> GetCommoditiesPricesByTerminalSlugAsync(string terminal_slug, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all commodities in all terminals, all at once
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesAllOkResponse>> GetCommoditiesPricesAllAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a price history of a commodity at a specific location
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesPricesHistoryOkResponse>> GetCommoditiesPricesHistoryByTerminalAndCommodityAsync(string id_terminal, string id_commodity, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieves the UEX Commodities Average Index™ Ranking
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRankingOkResponse>> GetCommoditiesRankingAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of average prices of a specific commodity (raw) in the last 15 days. (CAX Index)
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRawAveragesOkResponse>> GetCommoditiesRawAveragesByCommodityAsync(string id_commodity, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all unrefined (raw/ore) commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRawPricesOkResponse>> GetCommoditiesRawPricesByCommodityAsync(string id_commodity, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all unrefined (raw/ore) commodities.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRawPricesOkResponse>> GetCommoditiesRawPricesByTerminalAsync(string id_terminal, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all raw commodities in all terminals, all at once
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRawPricesAllOkResponse>> GetCommoditiesRawPricesAllAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of common routes calculated based on data reports
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRoutesOkResponse>> GetCommoditiesRoutesByCommodityAsync(string id_commodity, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of common routes calculated based on data reports
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRoutesOkResponse>> GetCommoditiesRoutesByOrbitOriginAsync(string id_orbit_origin, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of common routes calculated based on data reports
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRoutesOkResponse>> GetCommoditiesRoutesByPlanetOriginAsync(string id_planet_origin, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of common routes calculated based on data reports
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesRoutesOkResponse>> GetCommoditiesRoutesByTerminalOriginAsync(string id_terminal_origin, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of inventory states that are displayed at every trading terminal.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCommoditiesStatusOkResponse>> GetCommoditiesStatusAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexCrewApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Search for users listed in the Crew Directory
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetCrewOkResponse>> GetCrewBySpecializationAsync(string specialization, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexStaticApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// This command does not require API authentication.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse> GetDataExtractAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of specific parameters that UEX uses for managing prices and updates.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetDataParametersOkResponse>> GetDataParametersAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexUserApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain user fleet vehicles
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFleetOkResponse>> GetFleetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain details from a specific user such as name, avatar, etc.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetUserOkResponse>> GetUserAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of refinery jobs made by an user
        /// </summary>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse> GetUserRefineriesJobsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of trade transactions made by an user
        /// </summary>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse> GetUserTradesAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve user wallet balance
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetWalletBalanceOkResponse>> GetWalletBalanceAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexFuelApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByCommodityCodeAsync(string commodity_code, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByCommodityNameAsync(string commodity_name, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByCommoditySlugAsync(string commodity_slug, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByCommodityAsync(string id_commodity, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByTerminalAsync(string id_terminal, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByTerminalCodeAsync(string terminal_code, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByTerminalNameAsync(string terminal_name, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesOkResponse>> GetFuelPricesByTerminalSlugAsync(string terminal_slug, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all fuel prices in all terminals, all at once
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetFuelPricesAllOkResponse>> GetFuelPricesAllAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexItemsApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of Star Citizen items, including ship components, weapons, and more.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsOkResponse>> GetItemsByCategoryAsync(string id_category, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of Star Citizen items, including ship components, weapons, and more.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsOkResponse>> GetItemsByCompanyAsync(string id_company, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of Star Citizen items, including ship components, weapons, and more.
        /// </summary>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse> GetItemsBySizeAsync(string size, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of Star Citizen items, including ship components, weapons, and more.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsOkResponse>> GetItemsByUuidAsync(string uuid, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of attributes of a specific item
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsAttributesOkResponse>> GetItemsAttributesByCategoryAsync(string id_category, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of attributes of a specific item
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsAttributesOkResponse>> GetItemsAttributesByItemAsync(string id_item, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a list of attributes of a specific item
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsAttributesOkResponse>> GetItemsAttributesByUuidAsync(string uuid, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of prices for all items, including armor, ship components, weapons, and more.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsPricesOkResponse>> GetItemsPricesByCategoryAsync(string id_category, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of prices for all items, including armor, ship components, weapons, and more.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsPricesOkResponse>> GetItemsPricesByItemAsync(string id_item, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a comprehensive list of prices for all items, including armor, ship components, weapons, and more.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsPricesOkResponse>> GetItemsPricesByTerminalAsync(string id_terminal, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all items in all terminals, all at once
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetItemsPricesAllOkResponse>> GetItemsPricesAllAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexMarketplaceApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// List all advertisements favorited by an user
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetMarketplaceFavoritesOkResponse>> GetMarketplaceFavoritesAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// List all active marketplace advertisements, limited by 100
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetMarketplaceListingsOkResponse>> GetMarketplaceListingsAsync(string? slug = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexOrganizationsApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all organizations added to the UEX website
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetOrganizationsOkResponse>> GetOrganizationsByOrganizationAsync(string id_organization, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all organizations added to the UEX website
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetOrganizationsOkResponse>> GetOrganizationsBySlugAsync(string slug, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexRefineriesApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all refinery audits submitted by Data Runners.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetRefineriesAuditsOkResponse>> GetRefineriesAuditsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of the estimated capacity percentages for all refineries.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetRefineriesCapacitiesOkResponse>> GetRefineriesCapacitiesAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of the refining methods used by all in-game refineries
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetRefineriesMethodsOkResponse>> GetRefineriesMethodsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all refineries yields bonuses per commodity
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetRefineriesYieldsOkResponse>> GetRefineriesYieldsAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial interface IUexVehiclesApi
    {

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of Star Citizen vehicles loaners for a specific vehicle ID
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetVehiclesLoanersOkResponse>> GetVehiclesLoanersAsync(double? id_vehicle = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Obtain a daily updated list of vehicle prices in CIG's pledge store, managed either automatically by our bot or manually by the staff.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetVehiclesPricesOkResponse>> GetVehiclesPricesAsync(double? id_vehicle = null, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all in-game vehicle purchase prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetVehiclesPurchasesPricesOkResponse>> GetVehiclesPurchasesPricesByTerminalAsync(string id_terminal, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all vehicles purchases in all terminals, all at once
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetVehiclesPurchasesPricesAllOkResponse>> GetVehiclesPurchasesPricesAllAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of all in-game vehicle rental prices.
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetVehiclesRentalsPricesOkResponse>> GetVehiclesRentalsPricesByTerminalAsync(string id_terminal, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>
        /// Retrieve a list of prices for all vehicles rentals in all terminals, all at once
        /// </summary>
        /// <returns>OK</returns>
        /// <exception cref="UexApiException">A server side error occurred.</exception>
        System.Threading.Tasks.Task<UexApiResponse<GetVehiclesRentalsPricesAllOkResponse>> GetVehiclesRentalsPricesAllAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CategoryDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_game_related")]
        public double? Is_game_related { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_mining")]
        public double? Is_mining { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("section")]
        public string? Section { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string? Type { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CategoryDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CategoryDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityAveragePriceDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("cax_score")]
        public double? Cax_score { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_code")]
        public string? Commodity_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_slug")]
        public string? Commodity_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_month")]
        public double? Price_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_week")]
        public double? Price_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max")]
        public double? Price_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_month")]
        public double? Price_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_week")]
        public double? Price_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min")]
        public double? Price_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_month")]
        public double? Price_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_week")]
        public double? Price_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_users")]
        public double? Price_buy_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_users_rows")]
        public double? Price_buy_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg")]
        public double? Price_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_month")]
        public double? Price_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_week")]
        public double? Price_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max")]
        public double? Price_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_month")]
        public double? Price_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_week")]
        public double? Price_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min")]
        public double? Price_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_month")]
        public double? Price_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_week")]
        public double? Price_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_users")]
        public double? Price_sell_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_users_rows")]
        public double? Price_sell_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy")]
        public double? Scu_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg")]
        public double? Scu_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg_month")]
        public double? Scu_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg_week")]
        public double? Scu_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_max")]
        public double? Scu_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_max_month")]
        public double? Scu_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_max_week")]
        public double? Scu_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_min")]
        public double? Scu_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_min_month")]
        public double? Scu_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_min_week")]
        public double? Scu_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_total")]
        public double? Scu_buy_total { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_total_month")]
        public double? Scu_buy_total_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_total_week")]
        public double? Scu_buy_total_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_users")]
        public double? Scu_buy_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_users_rows")]
        public double? Scu_buy_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell")]
        public double? Scu_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg")]
        public double? Scu_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg_month")]
        public double? Scu_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg_week")]
        public double? Scu_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_max")]
        public double? Scu_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_max_month")]
        public double? Scu_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_max_week")]
        public double? Scu_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_min")]
        public double? Scu_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_min_month")]
        public double? Scu_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_min_week")]
        public double? Scu_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock")]
        public double? Scu_sell_stock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock_month")]
        public double? Scu_sell_stock_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock_week")]
        public double? Scu_sell_stock_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_total")]
        public double? Scu_sell_total { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_total_month")]
        public double? Scu_sell_total_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_total_week")]
        public double? Scu_sell_total_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_users")]
        public double? Scu_sell_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_users_rows")]
        public double? Scu_sell_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy")]
        public double? Status_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_avg")]
        public double? Status_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_avg_month")]
        public double? Status_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_avg_week")]
        public double? Status_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_max")]
        public double? Status_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_max_month")]
        public double? Status_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_max_week")]
        public double? Status_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_min")]
        public double? Status_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_min_month")]
        public double? Status_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_min_week")]
        public double? Status_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell")]
        public double? Status_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_avg")]
        public double? Status_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_avg_month")]
        public double? Status_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_avg_week")]
        public double? Status_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_max")]
        public double? Status_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_max_month")]
        public double? Status_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_max_week")]
        public double? Status_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_min")]
        public double? Status_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_min_month")]
        public double? Status_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_min_week")]
        public double? Status_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_buy")]
        public double? Volatility_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_price_buy")]
        public double? Volatility_price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_price_sell")]
        public double? Volatility_price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_scu_buy")]
        public double? Volatility_scu_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_scu_sell")]
        public double? Volatility_scu_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_sell")]
        public double? Volatility_sell { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityAveragePriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityAveragePriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_parent")]
        public double? Id_parent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_buggy")]
        public double? Is_buggy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_buyable")]
        public double? Is_buyable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_explosive")]
        public double? Is_explosive { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_extractable")]
        public double? Is_extractable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_fuel")]
        public double? Is_fuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_harvestable")]
        public double? Is_harvestable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_illegal")]
        public double? Is_illegal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_inert")]
        public double? Is_inert { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_mineral")]
        public double? Is_mineral { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_raw")]
        public double? Is_raw { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refinable")]
        public double? Is_refinable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refined")]
        public double? Is_refined { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_sellable")]
        public double? Is_sellable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_temporary")]
        public double? Is_temporary { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_volatile_qt")]
        public double? Is_volatile_qt { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_volatile_time")]
        public double? Is_volatile_time { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("kind")]
        public string? Kind { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("weight_scu")]
        public double? Weight_scu { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("wiki")]
        public string? Wiki { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityPriceBriefDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("container_sizes")]
        public string? Container_sizes { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg")]
        public double? Price_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy")]
        public double? Scu_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg")]
        public double? Scu_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell")]
        public double? Scu_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg")]
        public double? Scu_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock")]
        public double? Scu_sell_stock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock_avg")]
        public double? Scu_sell_stock_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy")]
        public double? Status_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell")]
        public double? Status_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityPriceBriefDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityPriceBriefDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityPriceDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public string? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_code")]
        public string? Commodity_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_slug")]
        public string? Commodity_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("container_sizes")]
        public string? Container_sizes { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_affinity")]
        public double? Faction_affinity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_month")]
        public double? Price_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_week")]
        public double? Price_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max")]
        public double? Price_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_month")]
        public double? Price_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_week")]
        public double? Price_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min")]
        public double? Price_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_month")]
        public double? Price_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_week")]
        public double? Price_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_users")]
        public double? Price_buy_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_users_rows")]
        public double? Price_buy_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg")]
        public double? Price_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_month")]
        public double? Price_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_week")]
        public double? Price_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max")]
        public double? Price_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_month")]
        public double? Price_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_week")]
        public double? Price_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min")]
        public double? Price_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_month")]
        public double? Price_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_week")]
        public double? Price_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_users")]
        public double? Price_sell_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_users_rows")]
        public double? Price_sell_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy")]
        public double? Scu_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg")]
        public double? Scu_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg_month")]
        public double? Scu_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg_week")]
        public double? Scu_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_max")]
        public double? Scu_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_max_month")]
        public double? Scu_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_max_week")]
        public double? Scu_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_min")]
        public double? Scu_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_min_month")]
        public double? Scu_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_min_week")]
        public double? Scu_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_users")]
        public double? Scu_buy_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_users_rows")]
        public double? Scu_buy_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell")]
        public double? Scu_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg")]
        public double? Scu_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg_month")]
        public double? Scu_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg_week")]
        public double? Scu_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_max")]
        public double? Scu_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_max_month")]
        public double? Scu_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_max_week")]
        public double? Scu_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_min")]
        public double? Scu_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_min_month")]
        public double? Scu_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_min_week")]
        public double? Scu_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock")]
        public double? Scu_sell_stock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock_avg")]
        public double? Scu_sell_stock_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock_avg_month")]
        public double? Scu_sell_stock_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock_avg_week")]
        public double? Scu_sell_stock_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_users")]
        public double? Scu_sell_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_users_rows")]
        public double? Scu_sell_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy")]
        public double? Status_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_avg")]
        public double? Status_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_avg_month")]
        public double? Status_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_avg_week")]
        public double? Status_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_max")]
        public double? Status_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_max_month")]
        public double? Status_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_max_week")]
        public double? Status_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_min")]
        public double? Status_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_min_month")]
        public double? Status_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_min_week")]
        public double? Status_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell")]
        public double? Status_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_avg")]
        public double? Status_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_avg_month")]
        public double? Status_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_avg_week")]
        public double? Status_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_max")]
        public double? Status_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_max_month")]
        public double? Status_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_max_week")]
        public double? Status_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_min")]
        public double? Status_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_min_month")]
        public double? Status_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_min_week")]
        public double? Status_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_code")]
        public string? Terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_is_player_owned")]
        public double? Terminal_is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_mcs")]
        public double? Terminal_mcs { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_slug")]
        public string? Terminal_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_buy")]
        public double? Volatility_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_price_buy")]
        public double? Volatility_price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_price_sell")]
        public double? Volatility_price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_scu_buy")]
        public double? Volatility_scu_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_scu_sell")]
        public double? Volatility_scu_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_sell")]
        public double? Volatility_sell { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityPriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityPriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityRankingDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("availability_buy")]
        public double? Availability_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("availability_sell")]
        public double? Availability_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("cax_score")]
        public double? Cax_score { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("investment")]
        public string? Investment { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("investment_per_scu")]
        public string? Investment_per_scu { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_average_trade_price_invalid")]
        public double? Is_average_trade_price_invalid { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_illegal")]
        public double? Is_illegal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_temporary")]
        public double? Is_temporary { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public string? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min")]
        public string? Price_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_minimum")]
        public object? Price_buy_minimum { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg")]
        public string? Price_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max")]
        public string? Price_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_maximum")]
        public object? Price_sell_maximum { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("profitability")]
        public string? Profitability { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("profitability_best")]
        public string? Profitability_best { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("profitability_per_scu")]
        public string? Profitability_per_scu { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("profitability_per_scu_best")]
        public string? Profitability_per_scu_best { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("profitability_relative_percentage")]
        public string? Profitability_relative_percentage { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("profitability_relative_percentage_best")]
        public string? Profitability_relative_percentage_best { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_avg")]
        public string? Scu_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_users")]
        public string? Scu_buy_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy_users_rows")]
        public double? Scu_buy_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_avg")]
        public string? Scu_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_users")]
        public string? Scu_sell_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_users_rows")]
        public double? Scu_sell_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("slug")]
        public string? Slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy_avg")]
        public double? Status_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell_avg")]
        public double? Status_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_id_price_buy_minimum")]
        public object? Terminal_id_price_buy_minimum { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_id_price_sell_maximum")]
        public object? Terminal_id_price_sell_maximum { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_slug_price_buy_minimum")]
        public object? Terminal_slug_price_buy_minimum { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_slug_price_sell_maximum")]
        public object? Terminal_slug_price_sell_maximum { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_price_buy")]
        public string? Volatility_price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_price_sell")]
        public string? Volatility_price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_scu_buy")]
        public string? Volatility_scu_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("volatility_scu_sell")]
        public string? Volatility_scu_sell { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityRankingDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityRankingDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityRawAverageDTO
    {

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityRawAverageDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityRawAverageDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityRouteDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_slug")]
        public string? Commodity_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("container_sizes_destination")]
        public string? Container_sizes_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("container_sizes_origin")]
        public string? Container_sizes_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_faction_name")]
        public string? Destination_faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_orbit_name")]
        public string? Destination_orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_planet_name")]
        public string? Destination_planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_star_system_name")]
        public string? Destination_star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_terminal_code")]
        public string? Destination_terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_terminal_is_player_owned")]
        public double? Destination_terminal_is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_terminal_name")]
        public string? Destination_terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("destination_terminal_slug")]
        public string? Destination_terminal_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("distance")]
        public double? Distance { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version_destination")]
        public string? Game_version_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version_origin")]
        public string? Game_version_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_cargo_center_destination")]
        public double? Has_cargo_center_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_cargo_center_origin")]
        public double? Has_cargo_center_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_docking_port_destination")]
        public double? Has_docking_port_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_docking_port_origin")]
        public double? Has_docking_port_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_freight_elevator_destination")]
        public double? Has_freight_elevator_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_freight_elevator_origin")]
        public double? Has_freight_elevator_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_loading_dock_destination")]
        public double? Has_loading_dock_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_loading_dock_origin")]
        public double? Has_loading_dock_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_quantum_marker_destination")]
        public double? Has_quantum_marker_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_quantum_marker_origin")]
        public double? Has_quantum_marker_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refuel_destination")]
        public double? Has_refuel_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refuel_origin")]
        public double? Has_refuel_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction_destination")]
        public double? Id_faction_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction_origin")]
        public double? Id_faction_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit_destination")]
        public double? Id_orbit_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit_origin")]
        public double? Id_orbit_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet_destination")]
        public double? Id_planet_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet_origin")]
        public double? Id_planet_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system_destination")]
        public double? Id_star_system_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system_origin")]
        public double? Id_star_system_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal_destination")]
        public double? Id_terminal_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal_origin")]
        public double? Id_terminal_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("investment")]
        public double? Investment { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_monitored_destination")]
        public double? Is_monitored_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_monitored_origin")]
        public double? Is_monitored_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_on_ground_destination")]
        public double? Is_on_ground_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_on_ground_origin")]
        public double? Is_on_ground_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_space_station_destination")]
        public double? Is_space_station_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_space_station_origin")]
        public double? Is_space_station_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_faction_name")]
        public string? Origin_faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_orbit_name")]
        public string? Origin_orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_planet_name")]
        public string? Origin_planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_star_system_name")]
        public string? Origin_star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_terminal_code")]
        public string? Origin_terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_terminal_is_player_owned")]
        public double? Origin_terminal_is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_terminal_name")]
        public string? Origin_terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("origin_terminal_slug")]
        public string? Origin_terminal_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_destination")]
        public double? Price_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_destination_users")]
        public double? Price_destination_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_destination_users_rows")]
        public double? Price_destination_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_margin")]
        public double? Price_margin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_origin")]
        public double? Price_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_origin_users")]
        public double? Price_origin_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_origin_users_rows")]
        public double? Price_origin_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_roi")]
        public double? Price_roi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("profit")]
        public double? Profit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("score")]
        public double? Score { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_destination")]
        public double? Scu_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_destination_users")]
        public double? Scu_destination_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_destination_users_rows")]
        public double? Scu_destination_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_margin")]
        public double? Scu_margin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_origin")]
        public double? Scu_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_origin_users")]
        public double? Scu_origin_users { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_origin_users_rows")]
        public double? Scu_origin_users_rows { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_reachable")]
        public double? Scu_reachable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_destination")]
        public double? Status_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_origin")]
        public double? Status_origin { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityRouteDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityRouteDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CommodityStatusDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public double? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("colors")]
        public string? Colors { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_abbr")]
        public string? Name_abbr { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_short")]
        public string? Name_short { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("percentage")]
        public string? Percentage { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("percentage_end")]
        public double? Percentage_end { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("percentage_start")]
        public double? Percentage_start { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CommodityStatusDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CommodityStatusDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CompanyDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("industry")]
        public string? Industry { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_item_manufacturer")]
        public double? Is_item_manufacturer { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_vehicle_manufacturer")]
        public double? Is_vehicle_manufacturer { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("nickname")]
        public string? Nickname { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("wiki")]
        public string? Wiki { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CompanyDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CompanyDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ContactDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public string? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("company_name")]
        public string? Company_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string? Description { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public object? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_company")]
        public double? Id_company { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_space_station")]
        public double? Id_space_station { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ContactDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ContactDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ContractDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public string? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("company_name")]
        public string? Company_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("contact_description")]
        public string? Contact_description { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("contact_name")]
        public string? Contact_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string? Description { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public object? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_company")]
        public double? Id_company { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_parent")]
        public double? Id_parent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_space_station")]
        public double? Id_space_station { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("payout")]
        public string? Payout { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ContractDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ContractDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class CrewProfileDTO
    {

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static CrewProfileDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<CrewProfileDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FactionDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_factions_friendly")]
        public string? Ids_factions_friendly { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_factions_hostile")]
        public string? Ids_factions_hostile { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_star_systems")]
        public string? Ids_star_systems { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_bounty_hunting")]
        public double? Is_bounty_hunting { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_piracy")]
        public double? Is_piracy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("wiki")]
        public string? Wiki { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static FactionDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<FactionDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FleetVehicleDTO
    {

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static FleetVehicleDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<FleetVehicleDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FuelPriceBriefDTO : System.Collections.ObjectModel.Collection<object>
    {

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static FuelPriceBriefDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<FuelPriceBriefDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class FuelPriceDTO : System.Collections.ObjectModel.Collection<Anonymous>
    {

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static FuelPriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<FuelPriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GameVersionsDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("live")]
        public string? Live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ptu")]
        public string? Ptu { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GameVersionsDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GameVersionsDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCategoriesAttributesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<Data>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCategoriesAttributesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCategoriesAttributesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCategoriesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CategoryDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCategoriesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCategoriesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCitiesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniverseCityDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCitiesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCitiesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesAlertsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<object>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesAlertsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesAlertsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesAveragesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CommodityAveragePriceDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesAveragesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesAveragesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CommodityDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesPricesAllOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CommodityPriceBriefDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesPricesAllOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesPricesAllOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesPricesHistoryOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<HistoricalCommodityPriceDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesPricesHistoryOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesPricesHistoryOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesPricesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CommodityPriceDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesPricesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesPricesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesRankingOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CommodityRankingDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesRankingOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesRankingOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesRawAveragesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<object>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesRawAveragesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesRawAveragesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesRawPricesAllOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<RawCommodityPriceBriefDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesRawPricesAllOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesRawPricesAllOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesRawPricesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<RawCommodityPriceDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesRawPricesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesRawPricesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesRoutesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CommodityRouteDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesRoutesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesRoutesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCommoditiesStatusOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public Data2? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCommoditiesStatusOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCommoditiesStatusOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCompaniesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CompanyDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCompaniesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCompaniesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetContactsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<ContactDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetContactsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetContactsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetContractsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<ContractDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetContractsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetContractsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetCrewOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<CrewProfileDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetCrewOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetCrewOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetDataParametersOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public ParametersDTO? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetDataParametersOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetDataParametersOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFactionsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<FactionDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetFactionsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetFactionsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFleetOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<FleetVehicleDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetFleetOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetFleetOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFuelPricesAllOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public FuelPriceDTO? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetFuelPricesAllOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetFuelPricesAllOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetFuelPricesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public FuelPriceBriefDTO? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetFuelPricesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetFuelPricesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetGameVersionsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public GameVersionsDTO? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetGameVersionsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetGameVersionsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetItemsAttributesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<ItemAttributeDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetItemsAttributesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetItemsAttributesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetItemsBadRequestResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public object? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetItemsBadRequestResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetItemsBadRequestResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetItemsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<ItemDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetItemsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetItemsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetItemsPricesAllOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<ItemPriceBriefDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetItemsPricesAllOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetItemsPricesAllOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetItemsPricesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<ItemPriceDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetItemsPricesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetItemsPricesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetJumpPointsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<JumpPointDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetJumpPointsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetJumpPointsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetJurisdictionsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<JurisdictionDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetJurisdictionsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetJurisdictionsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetMarketplaceFavoritesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<object>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetMarketplaceFavoritesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetMarketplaceFavoritesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetMarketplaceListingsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<MarketplaceListingDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetMarketplaceListingsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetMarketplaceListingsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetMoonsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniverseMoonDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetMoonsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetMoonsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetOrbitsDistancesBadRequestResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public object? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetOrbitsDistancesBadRequestResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetOrbitsDistancesBadRequestResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetOrbitsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniverseOrbitDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetOrbitsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetOrbitsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetOrganizationsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<OrganizationDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetOrganizationsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetOrganizationsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetOutpostsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniverseOutpostDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetOutpostsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetOutpostsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetPlanetsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniversePlanetDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetPlanetsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetPlanetsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetPoiOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<PointOfInterestDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetPoiOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetPoiOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetRefineriesAuditsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<RefineryAuditDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetRefineriesAuditsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetRefineriesAuditsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetRefineriesCapacitiesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<RefineryCapacityDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetRefineriesCapacitiesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetRefineriesCapacitiesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetRefineriesMethodsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<RefineryMethodDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetRefineriesMethodsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetRefineriesMethodsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetRefineriesYieldsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<RefineryYieldDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetRefineriesYieldsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetRefineriesYieldsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetReleaseNotesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<ReleaseNotDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetReleaseNotesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetReleaseNotesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetSpaceStationsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniverseSpaceStationDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetSpaceStationsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetSpaceStationsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetStarSystemsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniverseStarSystemDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetStarSystemsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetStarSystemsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetTerminalsDistancesBadRequestResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public object? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetTerminalsDistancesBadRequestResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetTerminalsDistancesBadRequestResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetTerminalsOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<UniverseTerminalDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetTerminalsOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetTerminalsOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetUserOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public UexUserDTO? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetUserOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetUserOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetUserRefineriesJobsBadRequestResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public object? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetUserRefineriesJobsBadRequestResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetUserRefineriesJobsBadRequestResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetUserTradesBadRequestResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public object? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetUserTradesBadRequestResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetUserTradesBadRequestResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetVehiclesLoanersOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public LoanerVehicleDTO? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetVehiclesLoanersOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetVehiclesLoanersOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetVehiclesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<VehicleDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetVehiclesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetVehiclesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetVehiclesPricesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<VehiclePricesDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetVehiclesPricesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetVehiclesPricesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetVehiclesPurchasesPricesAllOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<VehiclePurchasePriceBriefDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetVehiclesPurchasesPricesAllOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetVehiclesPurchasesPricesAllOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetVehiclesPurchasesPricesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<VehiclePurchasePriceDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetVehiclesPurchasesPricesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetVehiclesPurchasesPricesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetVehiclesRentalsPricesAllOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<VehicleRentalPriceBriefDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetVehiclesRentalsPricesAllOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetVehiclesRentalsPricesAllOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetVehiclesRentalsPricesOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public System.Collections.ObjectModel.Collection<VehicleRentalPriceDTO>? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetVehiclesRentalsPricesOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetVehiclesRentalsPricesOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class GetWalletBalanceOkResponse
    {

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public Data3? Data { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("http_code")]
        public double? Http_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("message")]
        public string? Message { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status")]
        public string? Status { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static GetWalletBalanceOkResponse FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<GetWalletBalanceOkResponse>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class HistoricalCommodityPriceDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_code")]
        public string? Commodity_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_slug")]
        public string? Commodity_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public string? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_buy")]
        public double? Scu_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell")]
        public double? Scu_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_sell_stock")]
        public double? Scu_sell_stock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_buy")]
        public double? Status_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("status_sell")]
        public double? Status_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_code")]
        public string? Terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_slug")]
        public string? Terminal_slug { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static HistoricalCommodityPriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<HistoricalCommodityPriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ItemAttributeDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("attribute_name")]
        public string? Attribute_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("category_name")]
        public string? Category_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_category")]
        public double? Id_category { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_category_attribute")]
        public double? Id_category_attribute { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_item")]
        public double? Id_item { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("item_name")]
        public string? Item_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("item_uuid")]
        public string? Item_uuid { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("unit")]
        public string? Unit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public string? Value { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ItemAttributeDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ItemAttributeDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ItemDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("category")]
        public string? Category { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("company_name")]
        public string? Company_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_category")]
        public double? Id_category { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_company")]
        public double? Id_company { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_parent")]
        public double? Id_parent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_vehicle")]
        public double? Id_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_exclusive_concierge")]
        public double? Is_exclusive_concierge { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_exclusive_pledge")]
        public double? Is_exclusive_pledge { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_exclusive_subscriber")]
        public double? Is_exclusive_subscriber { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("notification")]
        public object? Notification { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("screenshot")]
        public string? Screenshot { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("section")]
        public string? Section { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("size")]
        public object? Size { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("slug")]
        public string? Slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_store")]
        public string? Url_store { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("uuid")]
        public string? Uuid { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("vehicle_name")]
        public object? Vehicle_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ItemDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ItemDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ItemPriceBriefDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_category")]
        public double? Id_category { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_item")]
        public double? Id_item { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("item_name")]
        public string? Item_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("item_uuid")]
        public string? Item_uuid { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ItemPriceBriefDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ItemPriceBriefDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ItemPriceDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public string? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability")]
        public double? Durability { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_avg")]
        public double? Durability_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_avg_month")]
        public double? Durability_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_avg_week")]
        public double? Durability_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_max")]
        public double? Durability_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_max_month")]
        public double? Durability_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_max_week")]
        public double? Durability_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_min")]
        public double? Durability_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_min_month")]
        public double? Durability_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("durability_min_week")]
        public double? Durability_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_affinity")]
        public double? Faction_affinity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_category")]
        public double? Id_category { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_item")]
        public double? Id_item { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_parent")]
        public double? Id_parent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_vehicle")]
        public double? Id_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("item_name")]
        public string? Item_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_month")]
        public double? Price_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_week")]
        public double? Price_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max")]
        public double? Price_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_month")]
        public double? Price_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_week")]
        public double? Price_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min")]
        public double? Price_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_month")]
        public double? Price_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_week")]
        public double? Price_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg")]
        public double? Price_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_month")]
        public double? Price_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_week")]
        public double? Price_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max")]
        public double? Price_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_month")]
        public double? Price_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_week")]
        public double? Price_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min")]
        public double? Price_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_month")]
        public double? Price_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_week")]
        public double? Price_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_code")]
        public string? Terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_is_player_owned")]
        public double? Terminal_is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ItemPriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ItemPriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class JumpPointDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit_destination")]
        public double? Id_orbit_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit_origin")]
        public double? Id_orbit_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system_destination")]
        public double? Id_star_system_destination { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system_origin")]
        public double? Id_star_system_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_destination_name")]
        public string? Orbit_destination_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_origin_name")]
        public string? Orbit_origin_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_destination_name")]
        public string? Star_system_destination_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_origin_name")]
        public string? Star_system_origin_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static JumpPointDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<JumpPointDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class JurisdictionDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("nickname")]
        public string? Nickname { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("wiki")]
        public string? Wiki { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static JurisdictionDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<JurisdictionDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class LoanerVehicleDTO : System.Collections.ObjectModel.Collection<Anonymous2>
    {

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static LoanerVehicleDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<LoanerVehicleDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class MarketplaceListingDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_approved")]
        public double? Date_approved { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_expiration")]
        public double? Date_expiration { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string? Description { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_category")]
        public double? Id_category { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_item")]
        public double? Id_item { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_organization")]
        public double? Id_organization { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("in_stock")]
        public double? In_stock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("operation")]
        public string? Operation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("photos")]
        public string? Photos { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public double? Price { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("slug")]
        public string? Slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("title")]
        public string? Title { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("total_negotiations")]
        public double? Total_negotiations { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("total_views")]
        public double? Total_views { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string? Type { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("unit")]
        public string? Unit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("user_avatar")]
        public string? User_avatar { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("user_name")]
        public string? User_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("user_username")]
        public string? User_username { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("video_url")]
        public string? Video_url { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static MarketplaceListingDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<MarketplaceListingDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class OrganizationDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("description")]
        public string? Description { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("logo")]
        public string? Logo { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("slug")]
        public string? Slug { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static OrganizationDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<OrganizationDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ParametersDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("commodity")]
        public Commodity? Commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("global")]
        public Global? Global { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("item")]
        public Item? Item { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("vehicle_buy")]
        public Vehicle_buy? Vehicle_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("vehicle_rent")]
        public Vehicle_rent? Vehicle_rent { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ParametersDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ParametersDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class PointOfInterestDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public object? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_cargo_center")]
        public double? Has_cargo_center { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_clinic")]
        public double? Has_clinic { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_docking_port")]
        public double? Has_docking_port { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_food")]
        public double? Has_food { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_freight_elevator")]
        public double? Has_freight_elevator { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_gravity")]
        public double? Has_gravity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_habitation")]
        public double? Has_habitation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_loading_dock")]
        public double? Has_loading_dock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_quantum_marker")]
        public double? Has_quantum_marker { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refinery")]
        public double? Has_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refuel")]
        public double? Has_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_repair")]
        public double? Has_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_shops")]
        public double? Has_shops { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_trade_terminal")]
        public double? Has_trade_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_space_station")]
        public double? Id_space_station { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_armistice")]
        public double? Is_armistice { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_decommissioned")]
        public double? Is_decommissioned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_landable")]
        public double? Is_landable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_monitored")]
        public double? Is_monitored { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("nickname")]
        public string? Nickname { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("pad_types")]
        public object? Pad_types { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("subtype")]
        public string? Subtype { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string? Type { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static PointOfInterestDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<PointOfInterestDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RawCommodityPriceBriefDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg")]
        public double? Price_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static RawCommodityPriceBriefDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<RawCommodityPriceBriefDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RawCommodityPriceDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_code")]
        public string? Commodity_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_slug")]
        public string? Commodity_slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_month")]
        public double? Price_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_week")]
        public double? Price_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max")]
        public double? Price_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_month")]
        public double? Price_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_week")]
        public double? Price_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min")]
        public double? Price_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_month")]
        public double? Price_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_week")]
        public double? Price_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell")]
        public double? Price_sell { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg")]
        public double? Price_sell_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_month")]
        public double? Price_sell_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_avg_week")]
        public double? Price_sell_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max")]
        public double? Price_sell_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_month")]
        public double? Price_sell_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_max_week")]
        public double? Price_sell_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min")]
        public double? Price_sell_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_month")]
        public double? Price_sell_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_sell_min_week")]
        public double? Price_sell_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public string? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_code")]
        public string? Terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_is_player_owned")]
        public double? Terminal_is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_slug")]
        public string? Terminal_slug { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static RawCommodityPriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<RawCommodityPriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RefineryAuditDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("capacity")]
        public double? Capacity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("datarunner")]
        public string? Datarunner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_reported")]
        public double? Date_reported { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_space_station")]
        public double? Id_space_station { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("method")]
        public string? Method { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("quantity")]
        public double? Quantity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("quantity_inert")]
        public double? Quantity_inert { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("quantity_yield")]
        public double? Quantity_yield { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public string? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("total_cost")]
        public double? Total_cost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("total_time")]
        public double? Total_time { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("yield")]
        public double? Yield { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static RefineryAuditDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<RefineryAuditDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RefineryCapacityDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_report")]
        public double? Id_report { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_space_station")]
        public double? Id_space_station { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public string? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public double? Value { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("value_month")]
        public double? Value_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("value_week")]
        public double? Value_week { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static RefineryCapacityDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<RefineryCapacityDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RefineryMethodDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("rating_cost")]
        public double? Rating_cost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("rating_speed")]
        public double? Rating_speed { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("rating_yield")]
        public double? Rating_yield { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static RefineryMethodDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<RefineryMethodDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class RefineryYieldDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_report")]
        public double? Id_report { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_space_station")]
        public double? Id_space_station { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public string? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("value")]
        public double? Value { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("value_month")]
        public double? Value_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("value_week")]
        public double? Value_week { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static RefineryYieldDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<RefineryYieldDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class ReleaseNotDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("content")]
        public string? Content { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_updated")]
        public double? Date_updated { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static ReleaseNotDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<ReleaseNotDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UexUserDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("avatar")]
        public string? Avatar { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("bio")]
        public string? Bio { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_rsi_verified")]
        public double? Date_rsi_verified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_twitch_verified")]
        public double? Date_twitch_verified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("day_availability")]
        public string? Day_availability { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("discord_username")]
        public string? Discord_username { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("email")]
        public string? Email { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_factions")]
        public string? Ids_factions { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_star_systems")]
        public string? Ids_star_systems { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("language")]
        public string? Language { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("languages")]
        public string? Languages { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("specializations")]
        public string? Specializations { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("time_availability")]
        public string? Time_availability { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("timezone")]
        public string? Timezone { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("twitch_username")]
        public object? Twitch_username { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("username")]
        public string? Username { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("website_url")]
        public string? Website_url { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UexUserDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UexUserDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniverseCityDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_cargo_center")]
        public double? Has_cargo_center { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_clinic")]
        public double? Has_clinic { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_docking_port")]
        public double? Has_docking_port { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_food")]
        public double? Has_food { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_freight_elevator")]
        public double? Has_freight_elevator { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_gravity")]
        public double? Has_gravity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_habitation")]
        public double? Has_habitation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_loading_dock")]
        public double? Has_loading_dock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_quantum_marker")]
        public double? Has_quantum_marker { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refinery")]
        public double? Has_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refuel")]
        public double? Has_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_repair")]
        public double? Has_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_shops")]
        public double? Has_shops { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_trade_terminal")]
        public double? Has_trade_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_armistice")]
        public double? Is_armistice { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_decommissioned")]
        public double? Is_decommissioned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_landable")]
        public double? Is_landable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_monitored")]
        public double? Is_monitored { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("pad_types")]
        public object? Pad_types { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("wiki")]
        public string? Wiki { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniverseCityDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniverseCityDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniverseMoonDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_origin")]
        public string? Name_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniverseMoonDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniverseMoonDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniverseOrbitDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public object? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_asteroid")]
        public double? Is_asteroid { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_jump_point")]
        public double? Is_jump_point { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_lagrange")]
        public double? Is_lagrange { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_man_made")]
        public double? Is_man_made { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_planet")]
        public double? Is_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_star")]
        public double? Is_star { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_origin")]
        public string? Name_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniverseOrbitDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniverseOrbitDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniverseOutpostDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_cargo_center")]
        public double? Has_cargo_center { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_clinic")]
        public double? Has_clinic { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_docking_port")]
        public double? Has_docking_port { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_food")]
        public double? Has_food { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_freight_elevator")]
        public double? Has_freight_elevator { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_gravity")]
        public double? Has_gravity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_habitation")]
        public double? Has_habitation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_loading_dock")]
        public double? Has_loading_dock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_quantum_marker")]
        public double? Has_quantum_marker { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refinery")]
        public double? Has_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refuel")]
        public double? Has_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_repair")]
        public double? Has_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_shops")]
        public double? Has_shops { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_trade_terminal")]
        public double? Has_trade_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_armistice")]
        public double? Is_armistice { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_decommissioned")]
        public double? Is_decommissioned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_landable")]
        public double? Is_landable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_monitored")]
        public double? Is_monitored { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public string? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("nickname")]
        public string? Nickname { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("pad_types")]
        public object? Pad_types { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniverseOutpostDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniverseOutpostDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniversePlanetDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public object? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_origin")]
        public string? Name_origin { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniversePlanetDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniversePlanetDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniverseSpaceStationDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_cargo_center")]
        public double? Has_cargo_center { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_clinic")]
        public double? Has_clinic { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_docking_port")]
        public double? Has_docking_port { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_food")]
        public double? Has_food { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_freight_elevator")]
        public double? Has_freight_elevator { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_gravity")]
        public double? Has_gravity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_habitation")]
        public double? Has_habitation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_loading_dock")]
        public double? Has_loading_dock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_quantum_marker")]
        public double? Has_quantum_marker { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refinery")]
        public double? Has_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_refuel")]
        public double? Has_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_repair")]
        public double? Has_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_shops")]
        public double? Has_shops { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_trade_terminal")]
        public double? Has_trade_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_armistice")]
        public double? Is_armistice { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_decommissioned")]
        public double? Is_decommissioned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_jump_point")]
        public double? Is_jump_point { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_lagrange")]
        public double? Is_lagrange { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_landable")]
        public double? Is_landable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_monitored")]
        public double? Is_monitored { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("nickname")]
        public string? Nickname { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("pad_types")]
        public object? Pad_types { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniverseSpaceStationDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniverseSpaceStationDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniverseStarSystemDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public object? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_jurisdiction")]
        public double? Id_jurisdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default")]
        public double? Is_default { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("jurisdiction_name")]
        public object? Jurisdiction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("wiki")]
        public string? Wiki { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniverseStarSystemDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniverseStarSystemDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UniverseTerminalDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public object? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("code")]
        public string? Code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("company_name")]
        public string? Company_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("contact_url")]
        public object? Contact_url { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("screenshot")]
        public string? Screenshot { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("screenshot_full")]
        public string? Screenshot_full { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("screenshot_author")]
        public string? Screenshot_author { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_name")]
        public string? Faction_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_docking_port")]
        public double? Has_docking_port { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_freight_elevator")]
        public double? Has_freight_elevator { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("has_loading_dock")]
        public double? Has_loading_dock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_company")]
        public double? Id_company { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_faction")]
        public double? Id_faction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_space_station")]
        public double? Id_space_station { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_affinity_influenceable")]
        public double? Is_affinity_influenceable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_auto_load")]
        public double? Is_auto_load { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available")]
        public double? Is_available { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_available_live")]
        public double? Is_available_live { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_cargo_center")]
        public double? Is_cargo_center { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_default_system")]
        public double? Is_default_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_food")]
        public double? Is_food { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_habitation")]
        public double? Is_habitation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_jump_point")]
        public double? Is_jump_point { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_medical")]
        public double? Is_medical { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_nqa")]
        public double? Is_nqa { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_player_owned")]
        public double? Is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refinery")]
        public double? Is_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refuel")]
        public double? Is_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_repair")]
        public double? Is_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_shop_fps")]
        public double? Is_shop_fps { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_shop_vehicle")]
        public double? Is_shop_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_visible")]
        public double? Is_visible { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("max_container_size")]
        public double? Max_container_size { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("mcs")]
        public double? Mcs { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("nickname")]
        public string? Nickname { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public string? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string? Type { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static UniverseTerminalDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<UniverseTerminalDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class VehicleDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("company_name")]
        public string? Company_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("container_sizes")]
        public string? Container_sizes { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("crew")]
        public string? Crew { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("fuel_hydrogen")]
        public double? Fuel_hydrogen { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("fuel_quantum")]
        public double? Fuel_quantum { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("height")]
        public double? Height { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_company")]
        public double? Id_company { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_parent")]
        public double? Id_parent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_vehicles_loaners")]
        public string? Ids_vehicles_loaners { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_addon")]
        public double? Is_addon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_boarding")]
        public double? Is_boarding { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_bomber")]
        public double? Is_bomber { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_cargo")]
        public double? Is_cargo { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_carrier")]
        public double? Is_carrier { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_civilian")]
        public double? Is_civilian { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_concept")]
        public double? Is_concept { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_construction")]
        public double? Is_construction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_datarunner")]
        public double? Is_datarunner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_docking")]
        public double? Is_docking { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_emp")]
        public double? Is_emp { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_exploration")]
        public double? Is_exploration { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_ground_vehicle")]
        public double? Is_ground_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_hangar")]
        public double? Is_hangar { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_industrial")]
        public double? Is_industrial { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_interdiction")]
        public double? Is_interdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_loading_dock")]
        public double? Is_loading_dock { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_medical")]
        public double? Is_medical { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_military")]
        public double? Is_military { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_mining")]
        public double? Is_mining { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_passenger")]
        public double? Is_passenger { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_qed")]
        public double? Is_qed { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_quantum_capable")]
        public double? Is_quantum_capable { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_racing")]
        public double? Is_racing { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refinery")]
        public double? Is_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refuel")]
        public double? Is_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_repair")]
        public double? Is_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_research")]
        public double? Is_research { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_salvage")]
        public double? Is_salvage { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_scanning")]
        public double? Is_scanning { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_science")]
        public double? Is_science { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_showdown_winner")]
        public double? Is_showdown_winner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_spaceship")]
        public double? Is_spaceship { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_starter")]
        public double? Is_starter { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_stealth")]
        public double? Is_stealth { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_tractor_beam")]
        public double? Is_tractor_beam { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("length")]
        public double? Length { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("mass")]
        public double? Mass { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_full")]
        public string? Name_full { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("pad_type")]
        public string? Pad_type { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu")]
        public double? Scu { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("slug")]
        public string? Slug { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_brochure")]
        public string? Url_brochure { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_hotsite")]
        public string? Url_hotsite { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_photos")]
        public string? Url_photos { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_store")]
        public string? Url_store { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_video")]
        public string? Url_video { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("uuid")]
        public string? Uuid { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("width")]
        public double? Width { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static VehicleDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<VehicleDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class VehiclePricesDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("currency")]
        public string? Currency { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_vehicle")]
        public double? Id_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("on_sale")]
        public double? On_sale { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("on_sale_concierge")]
        public double? On_sale_concierge { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("on_sale_package")]
        public double? On_sale_package { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("on_sale_warbond")]
        public double? On_sale_warbond { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price")]
        public double? Price { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_concierge")]
        public double? Price_concierge { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_package")]
        public double? Price_package { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_warbond")]
        public double? Price_warbond { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("vehicle_name")]
        public string? Vehicle_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static VehiclePricesDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<VehiclePricesDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class VehiclePurchasePriceBriefDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_vehicle")]
        public double? Id_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("vehicle_name")]
        public string? Vehicle_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static VehiclePurchasePriceBriefDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<VehiclePurchasePriceBriefDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class VehiclePurchasePriceDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public string? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("datarunner")]
        public string? Datarunner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_affinity")]
        public double? Faction_affinity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_report")]
        public double? Id_report { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_vehicle")]
        public double? Id_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_month")]
        public double? Price_buy_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg_week")]
        public double? Price_buy_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max")]
        public double? Price_buy_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_month")]
        public double? Price_buy_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_max_week")]
        public double? Price_buy_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min")]
        public double? Price_buy_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_month")]
        public double? Price_buy_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_min_week")]
        public double? Price_buy_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_code")]
        public string? Terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_is_player_owned")]
        public double? Terminal_is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static VehiclePurchasePriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<VehiclePurchasePriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class VehicleRentalPriceBriefDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_vehicle")]
        public double? Id_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent")]
        public double? Price_rent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("vehicle_name")]
        public string? Vehicle_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static VehicleRentalPriceBriefDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<VehicleRentalPriceBriefDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class VehicleRentalPriceDTO
    {

        [System.Text.Json.Serialization.JsonPropertyName("city_name")]
        public string? City_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("datarunner")]
        public string? Datarunner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("faction_affinity")]
        public double? Faction_affinity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_city")]
        public double? Id_city { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_moon")]
        public double? Id_moon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_orbit")]
        public double? Id_orbit { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_outpost")]
        public double? Id_outpost { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_planet")]
        public double? Id_planet { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_poi")]
        public double? Id_poi { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_report")]
        public double? Id_report { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_star_system")]
        public double? Id_star_system { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_vehicle")]
        public double? Id_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("moon_name")]
        public object? Moon_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("orbit_name")]
        public string? Orbit_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("outpost_name")]
        public object? Outpost_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("planet_name")]
        public string? Planet_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("poi_name")]
        public object? Poi_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent")]
        public double? Price_rent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_avg")]
        public double? Price_rent_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_avg_month")]
        public double? Price_rent_avg_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_avg_week")]
        public double? Price_rent_avg_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_max")]
        public double? Price_rent_max { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_max_month")]
        public double? Price_rent_max_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_max_week")]
        public double? Price_rent_max_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_min")]
        public double? Price_rent_min { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_min_month")]
        public double? Price_rent_min_month { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_rent_min_week")]
        public double? Price_rent_min_week { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("space_station_name")]
        public object? Space_station_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("star_system_name")]
        public string? Star_system_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_code")]
        public string? Terminal_code { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_is_player_owned")]
        public double? Terminal_is_player_owned { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static VehicleRentalPriceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<VehicleRentalPriceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class WalletBalanceDTO
    {

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static WalletBalanceDTO FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<WalletBalanceDTO>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Anonymous
    {

        [System.Text.Json.Serialization.JsonPropertyName("commodity_name")]
        public string? Commodity_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_commodity")]
        public double? Id_commodity { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_terminal")]
        public double? Id_terminal { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy")]
        public double? Price_buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_buy_avg")]
        public double? Price_buy_avg { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("terminal_name")]
        public string? Terminal_name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Anonymous FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Anonymous>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Data
    {

        [System.Text.Json.Serialization.JsonPropertyName("category_name")]
        public string? Category_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_category")]
        public double? Id_category { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Data FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Data>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Data2
    {

        [System.Text.Json.Serialization.JsonPropertyName("buy")]
        public System.Collections.ObjectModel.Collection<CommodityStatusDTO>? Buy { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("sell")]
        public System.Collections.ObjectModel.Collection<CommodityStatusDTO>? Sell { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Data2 FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Data2>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Data3
    {

        [System.Text.Json.Serialization.JsonPropertyName("balance")]
        public double? Balance { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Data3 FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Data3>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Anonymous2
    {

        [System.Text.Json.Serialization.JsonPropertyName("company_name")]
        public string? Company_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("crew")]
        public string? Crew { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_company")]
        public double? Id_company { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_parent")]
        public double? Id_parent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_vehicles_loaners")]
        public string? Ids_vehicles_loaners { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_addon")]
        public double? Is_addon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_boarding")]
        public double? Is_boarding { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_cargo")]
        public double? Is_cargo { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_carrier")]
        public double? Is_carrier { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_civilian")]
        public double? Is_civilian { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_concept")]
        public double? Is_concept { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_construction")]
        public double? Is_construction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_datarunner")]
        public double? Is_datarunner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_emp")]
        public double? Is_emp { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_exploration")]
        public double? Is_exploration { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_ground_vehicle")]
        public double? Is_ground_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_industrial")]
        public double? Is_industrial { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_interdiction")]
        public double? Is_interdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_medical")]
        public double? Is_medical { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_military")]
        public double? Is_military { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_mining")]
        public double? Is_mining { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_passenger")]
        public double? Is_passenger { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_qed")]
        public double? Is_qed { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_racing")]
        public double? Is_racing { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refinery")]
        public double? Is_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refuel")]
        public double? Is_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_repair")]
        public double? Is_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_research")]
        public double? Is_research { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_salvage")]
        public double? Is_salvage { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_science")]
        public double? Is_science { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_showdown_winner")]
        public double? Is_showdown_winner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_spaceship")]
        public double? Is_spaceship { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_stealth")]
        public double? Is_stealth { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_tractor_beam")]
        public double? Is_tractor_beam { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("loaners")]
        public System.Collections.ObjectModel.Collection<Loaners>? Loaners { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_full")]
        public string? Name_full { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu")]
        public double? Scu { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_brochure")]
        public string? Url_brochure { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_hotsite")]
        public string? Url_hotsite { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_photos")]
        public System.Collections.ObjectModel.Collection<string>? Url_photos { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_store")]
        public string? Url_store { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_video")]
        public string? Url_video { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("uuid")]
        public string? Uuid { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Anonymous2 FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Anonymous2>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Commodity
    {

        [System.Text.Json.Serialization.JsonPropertyName("is_accepted")]
        public double? Is_accepted { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_temporary_enabled")]
        public double? Is_temporary_enabled { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_variation")]
        public double? Price_variation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu_variation")]
        public double? Scu_variation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ttl")]
        public double? Ttl { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Commodity FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Commodity>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Global
    {

        [System.Text.Json.Serialization.JsonPropertyName("evaluation_period_days")]
        public double? Evaluation_period_days { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version_ptu")]
        public string? Game_version_ptu { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_accepting_ptu_reports")]
        public double? Is_accepting_ptu_reports { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_accepting_reports")]
        public double? Is_accepting_reports { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_datacenter_enabled")]
        public double? Is_datacenter_enabled { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Global FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Global>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Item
    {

        [System.Text.Json.Serialization.JsonPropertyName("is_accepted")]
        public double? Is_accepted { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_variation")]
        public double? Price_variation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ttl")]
        public double? Ttl { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Item FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Item>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Vehicle_buy
    {

        [System.Text.Json.Serialization.JsonPropertyName("is_accepted")]
        public double? Is_accepted { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_variation")]
        public double? Price_variation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ttl")]
        public double? Ttl { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Vehicle_buy FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Vehicle_buy>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Vehicle_rent
    {

        [System.Text.Json.Serialization.JsonPropertyName("is_accepted")]
        public double? Is_accepted { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("price_variation")]
        public double? Price_variation { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ttl")]
        public double? Ttl { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Vehicle_rent FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Vehicle_rent>(data, options);

        }

    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class Loaners
    {

        [System.Text.Json.Serialization.JsonPropertyName("company_name")]
        public string? Company_name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("crew")]
        public string? Crew { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_added")]
        public double? Date_added { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("date_modified")]
        public double? Date_modified { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("game_version")]
        public string? Game_version { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id")]
        public double? Id { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_company")]
        public double? Id_company { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("id_parent")]
        public double? Id_parent { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("ids_vehicles_loaners")]
        public string? Ids_vehicles_loaners { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_addon")]
        public double? Is_addon { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_boarding")]
        public double? Is_boarding { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_cargo")]
        public double? Is_cargo { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_carrier")]
        public double? Is_carrier { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_civilian")]
        public double? Is_civilian { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_concept")]
        public double? Is_concept { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_construction")]
        public double? Is_construction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_datarunner")]
        public double? Is_datarunner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_emp")]
        public double? Is_emp { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_exploration")]
        public double? Is_exploration { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_ground_vehicle")]
        public double? Is_ground_vehicle { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_industrial")]
        public double? Is_industrial { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_interdiction")]
        public double? Is_interdiction { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_medical")]
        public double? Is_medical { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_military")]
        public double? Is_military { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_mining")]
        public double? Is_mining { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_passenger")]
        public double? Is_passenger { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_qed")]
        public double? Is_qed { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_racing")]
        public double? Is_racing { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refinery")]
        public double? Is_refinery { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_refuel")]
        public double? Is_refuel { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_repair")]
        public double? Is_repair { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_research")]
        public double? Is_research { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_salvage")]
        public double? Is_salvage { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_science")]
        public double? Is_science { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_showdown_winner")]
        public double? Is_showdown_winner { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_spaceship")]
        public double? Is_spaceship { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_stealth")]
        public double? Is_stealth { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("is_tractor_beam")]
        public double? Is_tractor_beam { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name")]
        public string? Name { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("name_full")]
        public string? Name_full { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("scu")]
        public double? Scu { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_brochure")]
        public string? Url_brochure { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_hotsite")]
        public string? Url_hotsite { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_photos")]
        public System.Collections.ObjectModel.Collection<string>? Url_photos { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_store")]
        public string? Url_store { get; set; } = default!;

        [System.Text.Json.Serialization.JsonPropertyName("url_video")]
        public string? Url_video { get; set; } = default!;

        private System.Collections.Generic.IDictionary<string, object>? _additionalProperties;

        [System.Text.Json.Serialization.JsonExtensionData]
        public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
        {
            get { return _additionalProperties ?? (_additionalProperties = new System.Collections.Generic.Dictionary<string, object>()); }
            set { _additionalProperties = value; }
        }

        public string ToJson()
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Serialize(this, options);

        }
        public static Loaners FromJson(string data)
        {

            var options = new System.Text.Json.JsonSerializerOptions();

            return System.Text.Json.JsonSerializer.Deserialize<Loaners>(data, options);

        }

    }


    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UexApiResponse
    {
        public int StatusCode { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public UexApiResponse(int statusCode, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers)
        {
            StatusCode = statusCode;
            Headers = headers;
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UexApiResponse<TResult> : UexApiResponse
    {
        public TResult Result { get; private set; }

        public UexApiResponse(int statusCode, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result)
            : base(statusCode, headers)
        {
            Result = result;
        }
    }


    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UexApiException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string? Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public UexApiException(string message, int statusCode, string? response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception? innerException)
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + ((response == null) ? "(null)" : response.Substring(0, response.Length >= 512 ? 512 : response.Length)), innerException)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "******** (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0))")]
    public partial class UexApiException<TResult> : UexApiException
    {
        public TResult Result { get; private set; }

        public UexApiException(string message, int statusCode, string? response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception? innerException)
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore  108
#pragma warning restore  114
#pragma warning restore  472
#pragma warning restore  612
#pragma warning restore 1573
#pragma warning restore 1591
#pragma warning restore 8073
#pragma warning restore 3016
#pragma warning restore 8600
#pragma warning restore 8602
#pragma warning restore 8603
#pragma warning restore 8604
#pragma warning restore 8625