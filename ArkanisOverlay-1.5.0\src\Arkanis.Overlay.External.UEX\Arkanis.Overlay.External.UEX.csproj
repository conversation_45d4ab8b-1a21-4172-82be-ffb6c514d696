<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <NoWarn>
            $(NoWarn);
            CS8618; <!-- Non-nullable property 'VehicleName' must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring the property as nullable. -->
        </NoWarn>
        <PackageId>Arkanis.Overlay.External.UEX</PackageId>
    </PropertyGroup>

    <PropertyGroup>
        <GeneratedClientConfig>$(MSBuildProjectDirectory)/nswag.json</GeneratedClientConfig>
        <GeneratedClientDir>$(MSBuildProjectDirectory)</GeneratedClientDir>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Arkanis.Overlay.Common\Arkanis.Overlay.Common.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Http"/>
    </ItemGroup>

    <Target Name="Restore dotnet tools" BeforeTargets="GenerateAPIClient">
        <Exec Command="dotnet tool restore"/>
    </Target>

    <Target Name="GenerateAPIClient"
            Inputs="$(GeneratedClientApiSpec);$(GeneratedClientConfig)"
            Outputs="$(GeneratedClientDir)"
            BeforeTargets="BeforeBuild">
        <Message Text="Generating API client from $(GeneratedClientApiSpec) using $(GeneratedClientConfig)" Importance="high"/>
        <Exec Command="dotnet nswag run $(GeneratedClientConfig)"/>
    </Target>

</Project>
