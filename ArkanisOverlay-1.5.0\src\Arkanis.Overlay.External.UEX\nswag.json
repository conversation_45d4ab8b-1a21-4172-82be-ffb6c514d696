{
    "runtime": "Net80",
    "defaultVariables": "OutputDir=.",
    "documentGenerator": {
        "fromDocument": {
            // https://raw.githubusercontent.com/dolejska-daniel/uexcorp-openapi/refs/heads/master/openapi.yaml
            "url": "./openapi.yaml",
            "newLineBehavior": "LF"
        }
    },
    "codeGenerators": {
        "openApiToCSharpClient": {
            "clientBaseClass": "UexApiClientBase",
            "configurationClass": null,
            "generateClientClasses": true,
            "suppressClientClassesOutput": false,
            "generateClientInterfaces": true,
            "suppressClientInterfacesOutput": false,
            "clientBaseInterface": null,
            "injectHttpClient": false,
            "disposeHttpClient": true,
            "protectedMethods": [],
            "generateExceptionClasses": true,
            "exceptionClass": "UexApiException",
            "wrapDtoExceptions": true,
            "useHttpClientCreationMethod": true,
            "httpClientType": "System.Net.Http.HttpClient",
            "useHttpRequestMessageCreationMethod": false,
            "useBaseUrl": true,
            "generateBaseUrlProperty": true,
            "generateSyncMethods": false,
            "generatePrepareRequestAndProcessResponseAsAsyncMethods": false,
            "exposeJsonSerializerSettings": true,
            "clientClassAccessModifier": "internal",
            "typeAccessModifier": "public",
            "propertySetterAccessModifier": "",
            "generateNativeRecords": false,
            "generateContractsOutput": true,
            "contractsNamespace": "Arkanis.Overlay.External.UEX.Abstractions",
            "contractsOutputFilePath": "Abstractions/Contracts.cs",
            "parameterDateTimeFormat": "s",
            "parameterDateFormat": "yyyy-MM-dd",
            "generateUpdateJsonSerializerSettingsMethod": true,
            "useRequestAndResponseSerializationSettings": true,
            "serializeTypeInformation": false,
            "queryNullValue": "",
            "className": "Uex{controller}Api",
            "operationGenerationMode": "MultipleClientsFromFirstTagAndOperationId",
            "additionalNamespaceUsages": [],
            "additionalContractNamespaceUsages": [],
            "generateOptionalParameters": true,
            "generateJsonMethods": true,
            "enforceFlagEnums": false,
            "parameterArrayType": "System.Collections.Generic.IEnumerable",
            "parameterDictionaryType": "System.Collections.Generic.IDictionary",
            "responseArrayType": "System.Collections.ObjectModel.Collection",
            "responseDictionaryType": "System.Collections.Generic.Dictionary",
            "wrapResponses": true,
            "wrapResponseMethods": [],
            "generateResponseClasses": true,
            "responseClass": "UexApiResponse",
            "namespace": "Arkanis.Overlay.External.UEX",
            "requiredPropertiesMustBeDefined": true,
            "dateType": "System.DateTime",
            "jsonConverters": null,
            "anyType": "object",
            "dateTimeType": "System.DateTime",
            "timeType": "System.TimeSpan",
            "timeSpanType": "System.TimeSpan",
            "arrayType": "System.Collections.ObjectModel.Collection",
            "arrayInstanceType": "System.Collections.ObjectModel.Collection",
            "dictionaryType": "System.Collections.Generic.Dictionary",
            "dictionaryInstanceType": "System.Collections.Generic.Dictionary",
            "arrayBaseType": "System.Collections.ObjectModel.Collection",
            "dictionaryBaseType": "System.Collections.Generic.Dictionary",
            "classStyle": "Poco",
            "jsonLibrary": "SystemTextJson",
            "generateDefaultValues": true,
            "generateDataAnnotations": true,
            "excludedTypeNames": [],
            "excludedParameterNames": [],
            "handleReferences": false,
            "generateImmutableArrayProperties": false,
            "generateImmutableDictionaryProperties": false,
            "jsonSerializerSettingsTransformationMethod": null,
            "inlineNamedArrays": false,
            "inlineNamedDictionaries": false,
            "inlineNamedTuples": true,
            "inlineNamedAny": false,
            "generateDtoTypes": true,
            "generateOptionalPropertiesAsNullable": true,
            "generateNullableReferenceTypes": true,
            // "templateDirectory": "Templates",
            "serviceHost": null,
            "serviceSchemes": null,
            "output": "Client.cs",
            "newLineBehavior": "LF"
        }
    }
}
