﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <!-- Specific windows version required due to the use of toast notifications -->
        <!-- see: https://learn.microsoft.com/en-us/windows/apps/design/shell/tiles-and-notifications/send-local-toast?tabs=uwp#step-1-install-nuget-package -->
        <TargetFramework>net8.0-windows10.0.17763.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <UseWindowsForms>true</UseWindowsForms>
        <IncludePackageReferencesDuringMarkupCompilation>true</IncludePackageReferencesDuringMarkupCompilation>
        <Platform>x64</Platform>
        <StartupObject>Arkanis.Overlay.Host.Desktop.Program</StartupObject>
        <ServerGarbageCollection>false</ServerGarbageCollection>
        <PackageId>Arkanis.Overlay.Host.Desktop</PackageId>
        <AssemblyName>ArkanisOverlay</AssemblyName>
    </PropertyGroup>

    <PropertyGroup>
        <!-- Exclude unsupported languages -->
        <ResourceLanguages>en</ResourceLanguages>
        <SatelliteResourceLanguages>en-US;en</SatelliteResourceLanguages>
    </PropertyGroup>

    <PropertyGroup>
        <ApplicationIcon>Resources/favicon.ico</ApplicationIcon>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Dapplo.Microsoft.Extensions.Hosting.AppServices"/>
        <PackageReference Include="Dapplo.Microsoft.Extensions.Hosting.Plugins"/>
        <PackageReference Include="Dapplo.Microsoft.Extensions.Hosting.Wpf"/>
        <PackageReference Include="Microsoft.AspNetCore.Components.Authorization"/>
        <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Wpf"/>
        <PackageReference Include="Microsoft.AspNetCore.Http"/>
        <PackageReference Include="Microsoft.Data.Sqlite"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection"/>
        <PackageReference Include="Microsoft.Extensions.Hosting"/>
        <PackageReference Include="Microsoft.Extensions.Http"/>
        <PackageReference Include="Microsoft.Extensions.Logging"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Console"/>
        <PackageReference Include="Microsoft.Extensions.Options.DataAnnotations"/>
        <PackageReference Include="Microsoft.Toolkit.Uwp.Notifications"/>
        <PackageReference Include="Microsoft.Win32.Registry"/>
        <PackageReference Include="Microsoft.Windows.CsWin32">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Serilog"/>
        <PackageReference Include="Serilog.Expressions" />
        <PackageReference Include="Serilog.Extensions.Hosting"/>
        <PackageReference Include="Serilog.Settings.Configuration"/>
        <PackageReference Include="Serilog.Sinks.Console"/>
        <PackageReference Include="Serilog.Sinks.File"/>
        <PackageReference Include="SingleInstanceCore"/>
        <PackageReference Include="TrayIcon"/>
        <PackageReference Include="Velopack"/>
    </ItemGroup>

    <ItemGroup>
        <Resource Include="Resources\*.png"/>
        <Resource Include="Resources\favicon.ico"/>
        <Resource Include="wwwroot\assets\img\uex-api-badge-powered.png"/>
    </ItemGroup>

    <ItemGroup>
        <File Include="NativeMethods.txt"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Arkanis.Overlay.Common\Arkanis.Overlay.Common.csproj"/>
        <ProjectReference Include="..\Arkanis.Overlay.External.UEX\Arkanis.Overlay.External.UEX.csproj"/>
        <ProjectReference Include="..\Arkanis.Overlay.Components\Arkanis.Overlay.Components.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Content Remove="packages.lock.json"/>
        <None Include="packages.lock.json">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </None>
    </ItemGroup>

</Project>
