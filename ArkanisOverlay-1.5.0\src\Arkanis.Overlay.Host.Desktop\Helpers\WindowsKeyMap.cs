namespace Arkanis.Overlay.Host.Desktop.Helpers;

using Windows.Win32.UI.Input.KeyboardAndMouse;
using Domain.Models.Keyboard;

public static class WindowsKeyMap
{
    private static readonly Dictionary<KeyboardKey, VIRTUAL_KEY> EnumToVirtualKey = new()
    {
        [KeyboardKey.Backspace] = VIRTUAL_KEY.VK_BACK,
        [KeyboardKey.Tab] = VIRTUAL_KEY.VK_TAB,
        [KeyboardKey.Enter] = VIRTUAL_KEY.VK_RETURN,
        [KeyboardKey.ShiftLeft] = VIRTUAL_KEY.VK_LSHIFT,
        [KeyboardKey.ShiftRight] = VIRTUAL_KEY.VK_RSHIFT,
        [KeyboardKey.ControlLeft] = VIRTUAL_KEY.VK_LCONTROL,
        [KeyboardKey.ControlRight] = VIRTUAL_KEY.VK_RCONTROL,
        [KeyboardKey.AltLeft] = VIRTUAL_KEY.VK_LMENU,
        [KeyboardKey.AltRight] = VIRTUAL_KEY.VK_RMENU,
        [KeyboardKey.Pause] = VIRTUAL_KEY.VK_PAUSE,
        [KeyboardKey.CapsLock] = VIRTUAL_KEY.VK_CAPITAL,
        [KeyboardKey.Escape] = VIRTUAL_KEY.VK_ESCAPE,
        [KeyboardKey.Space] = VIRTUAL_KEY.VK_SPACE,
        [KeyboardKey.PageUp] = VIRTUAL_KEY.VK_PRIOR,
        [KeyboardKey.PageDown] = VIRTUAL_KEY.VK_NEXT,
        [KeyboardKey.End] = VIRTUAL_KEY.VK_END,
        [KeyboardKey.Home] = VIRTUAL_KEY.VK_HOME,
        [KeyboardKey.ArrowLeft] = VIRTUAL_KEY.VK_LEFT,
        [KeyboardKey.ArrowUp] = VIRTUAL_KEY.VK_UP,
        [KeyboardKey.ArrowRight] = VIRTUAL_KEY.VK_RIGHT,
        [KeyboardKey.ArrowDown] = VIRTUAL_KEY.VK_DOWN,
        [KeyboardKey.PrintScreen] = VIRTUAL_KEY.VK_SNAPSHOT,
        [KeyboardKey.Insert] = VIRTUAL_KEY.VK_INSERT,
        [KeyboardKey.Delete] = VIRTUAL_KEY.VK_DELETE,
        [KeyboardKey.Digit0] = VIRTUAL_KEY.VK_0,
        [KeyboardKey.Digit1] = VIRTUAL_KEY.VK_1,
        [KeyboardKey.Digit2] = VIRTUAL_KEY.VK_2,
        [KeyboardKey.Digit3] = VIRTUAL_KEY.VK_3,
        [KeyboardKey.Digit4] = VIRTUAL_KEY.VK_4,
        [KeyboardKey.Digit5] = VIRTUAL_KEY.VK_5,
        [KeyboardKey.Digit6] = VIRTUAL_KEY.VK_6,
        [KeyboardKey.Digit7] = VIRTUAL_KEY.VK_7,
        [KeyboardKey.Digit8] = VIRTUAL_KEY.VK_8,
        [KeyboardKey.Digit9] = VIRTUAL_KEY.VK_9,
        [KeyboardKey.KeyA] = VIRTUAL_KEY.VK_A,
        [KeyboardKey.KeyB] = VIRTUAL_KEY.VK_B,
        [KeyboardKey.KeyC] = VIRTUAL_KEY.VK_C,
        [KeyboardKey.KeyD] = VIRTUAL_KEY.VK_D,
        [KeyboardKey.KeyE] = VIRTUAL_KEY.VK_E,
        [KeyboardKey.KeyF] = VIRTUAL_KEY.VK_F,
        [KeyboardKey.KeyG] = VIRTUAL_KEY.VK_G,
        [KeyboardKey.KeyH] = VIRTUAL_KEY.VK_H,
        [KeyboardKey.KeyI] = VIRTUAL_KEY.VK_I,
        [KeyboardKey.KeyJ] = VIRTUAL_KEY.VK_J,
        [KeyboardKey.KeyK] = VIRTUAL_KEY.VK_K,
        [KeyboardKey.KeyL] = VIRTUAL_KEY.VK_L,
        [KeyboardKey.KeyM] = VIRTUAL_KEY.VK_M,
        [KeyboardKey.KeyN] = VIRTUAL_KEY.VK_N,
        [KeyboardKey.KeyO] = VIRTUAL_KEY.VK_O,
        [KeyboardKey.KeyP] = VIRTUAL_KEY.VK_P,
        [KeyboardKey.KeyQ] = VIRTUAL_KEY.VK_Q,
        [KeyboardKey.KeyR] = VIRTUAL_KEY.VK_R,
        [KeyboardKey.KeyS] = VIRTUAL_KEY.VK_S,
        [KeyboardKey.KeyT] = VIRTUAL_KEY.VK_T,
        [KeyboardKey.KeyU] = VIRTUAL_KEY.VK_U,
        [KeyboardKey.KeyV] = VIRTUAL_KEY.VK_V,
        [KeyboardKey.KeyW] = VIRTUAL_KEY.VK_W,
        [KeyboardKey.KeyX] = VIRTUAL_KEY.VK_X,
        [KeyboardKey.KeyY] = VIRTUAL_KEY.VK_Y,
        [KeyboardKey.KeyZ] = VIRTUAL_KEY.VK_Z,
        [KeyboardKey.MetaLeft] = VIRTUAL_KEY.VK_LWIN,
        [KeyboardKey.MetaRight] = VIRTUAL_KEY.VK_RWIN,
        [KeyboardKey.ContextMenu] = VIRTUAL_KEY.VK_APPS,
        [KeyboardKey.Numpad0] = VIRTUAL_KEY.VK_NUMPAD0,
        [KeyboardKey.Numpad1] = VIRTUAL_KEY.VK_NUMPAD1,
        [KeyboardKey.Numpad2] = VIRTUAL_KEY.VK_NUMPAD2,
        [KeyboardKey.Numpad3] = VIRTUAL_KEY.VK_NUMPAD3,
        [KeyboardKey.Numpad4] = VIRTUAL_KEY.VK_NUMPAD4,
        [KeyboardKey.Numpad5] = VIRTUAL_KEY.VK_NUMPAD5,
        [KeyboardKey.Numpad6] = VIRTUAL_KEY.VK_NUMPAD6,
        [KeyboardKey.Numpad7] = VIRTUAL_KEY.VK_NUMPAD7,
        [KeyboardKey.Numpad8] = VIRTUAL_KEY.VK_NUMPAD8,
        [KeyboardKey.Numpad9] = VIRTUAL_KEY.VK_NUMPAD9,
        [KeyboardKey.NumpadMultiply] = VIRTUAL_KEY.VK_MULTIPLY,
        [KeyboardKey.NumpadAdd] = VIRTUAL_KEY.VK_ADD,
        [KeyboardKey.NumpadSubtract] = VIRTUAL_KEY.VK_SUBTRACT,
        [KeyboardKey.NumpadDecimal] = VIRTUAL_KEY.VK_DECIMAL,
        [KeyboardKey.NumpadDivide] = VIRTUAL_KEY.VK_DIVIDE,
        [KeyboardKey.F1] = VIRTUAL_KEY.VK_F1,
        [KeyboardKey.F2] = VIRTUAL_KEY.VK_F2,
        [KeyboardKey.F3] = VIRTUAL_KEY.VK_F3,
        [KeyboardKey.F4] = VIRTUAL_KEY.VK_F4,
        [KeyboardKey.F5] = VIRTUAL_KEY.VK_F5,
        [KeyboardKey.F6] = VIRTUAL_KEY.VK_F6,
        [KeyboardKey.F7] = VIRTUAL_KEY.VK_F7,
        [KeyboardKey.F8] = VIRTUAL_KEY.VK_F8,
        [KeyboardKey.F9] = VIRTUAL_KEY.VK_F9,
        [KeyboardKey.F10] = VIRTUAL_KEY.VK_F10,
        [KeyboardKey.F11] = VIRTUAL_KEY.VK_F11,
        [KeyboardKey.F12] = VIRTUAL_KEY.VK_F12,
        [KeyboardKey.F13] = VIRTUAL_KEY.VK_F13,
        [KeyboardKey.F14] = VIRTUAL_KEY.VK_F14,
        [KeyboardKey.F15] = VIRTUAL_KEY.VK_F15,
        [KeyboardKey.F16] = VIRTUAL_KEY.VK_F16,
        [KeyboardKey.F17] = VIRTUAL_KEY.VK_F17,
        [KeyboardKey.F18] = VIRTUAL_KEY.VK_F18,
        [KeyboardKey.F19] = VIRTUAL_KEY.VK_F19,
        [KeyboardKey.F20] = VIRTUAL_KEY.VK_F20,
        [KeyboardKey.F21] = VIRTUAL_KEY.VK_F21,
        [KeyboardKey.F22] = VIRTUAL_KEY.VK_F22,
        [KeyboardKey.F23] = VIRTUAL_KEY.VK_F23,
        [KeyboardKey.F24] = VIRTUAL_KEY.VK_F24,
        [KeyboardKey.NumLock] = VIRTUAL_KEY.VK_NUMLOCK,
        [KeyboardKey.ScrollLock] = VIRTUAL_KEY.VK_SCROLL,
        [KeyboardKey.Semicolon] = VIRTUAL_KEY.VK_OEM_1,
        [KeyboardKey.Equal] = VIRTUAL_KEY.VK_OEM_PLUS,
        [KeyboardKey.Comma] = VIRTUAL_KEY.VK_OEM_COMMA,
        [KeyboardKey.Minus] = VIRTUAL_KEY.VK_OEM_MINUS,
        [KeyboardKey.Period] = VIRTUAL_KEY.VK_OEM_PERIOD,
        [KeyboardKey.Slash] = VIRTUAL_KEY.VK_OEM_2,
        [KeyboardKey.Backquote] = VIRTUAL_KEY.VK_OEM_3,
        [KeyboardKey.BracketLeft] = VIRTUAL_KEY.VK_OEM_4,
        [KeyboardKey.Backslash] = VIRTUAL_KEY.VK_OEM_5,
        [KeyboardKey.BracketRight] = VIRTUAL_KEY.VK_OEM_6,
        [KeyboardKey.Quote] = VIRTUAL_KEY.VK_OEM_7,
    };

    internal static VIRTUAL_KEY ToCode(KeyboardKey key)
        => EnumToVirtualKey.GetValueOrDefault(key, VIRTUAL_KEY.VK__none_);
}
