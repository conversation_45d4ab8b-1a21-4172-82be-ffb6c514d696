// Error Handling
// Warning PInvoke003 : This API will not be generated. Do not generate GetLastError.
// Call Marshal.GetLastWin32Error() instead. Learn more from
// https://docs.microsoft.com/dotnet/api/system.runtime.interopservices.marshal.getlastwin32error
// GetLastError
// SetLastError

// Window Event Hook
SetWinEventHook
UnhookWinEvent
OBJECT_IDENTIFIER
CHILDID_SELF

// Window Functions
FindWindowW
GetClientRect
GetWindowTextW
GetClassNameW
GetWindowTextLengthW
GetClientRect
ClientToScreen
GetActiveWindow
GetWindowThreadProcessId
SetForegroundWindow
GetForegroundWindow
SetFocus
SetActiveWindow
GetWindowLong
SetWindowLong
GetParent
GetAncestor

WINDOW_LONG_PTR_INDEX
WINDOW_EX_STYLE

// DPI Scaling
GetDpiForMonitor
MonitorFromWindow
MONITOR_DPI_TYPE

// Winuser.h Event Constants
// https://learn.microsoft.com/en-us/windows/win32/winauto/event-constants
EVENT_*

// SetWinEventHook dwFlags Constants
// https://learn.microsoft.com/en-us/windows/win32/api/winuser/nf-winuser-setwineventhook
WINEVENT_*

// Message Loop
GetMessage
TranslateMessage
DispatchMessage

// Keyboard Tracking
RegisterHotKey
UnregisterHotKey

// Keyboard HotKey Message
// WM_HOTKEY
WM_*

// Keyboard Modifiers
HOT_KEY_MODIFIERS

// Keyboard Virtual Key Codes
VIRTUAL_KEY

// Global Hotkey
SetWindowsHookEx
UnhookWindowsHookEx
CallNextHookEx
GetModuleHandle
WINDOWS_HOOK_ID
HC_ACTION
KBDLLHOOKSTRUCT
GetAsyncKeyState
GetCurrentThreadId
AttachThreadInput
BringWindowToTop
ShowWindow

// ProcessExitWatcher + Process & Window InterOp
PROCESS_ACCESS_RIGHTS
WAIT_EVENT
INFINITE

OpenProcess
CloseHandle
WaitForSingleObject
GetModuleFileNameEx

PostThreadMessage
GetCurrentThreadId

// Console Allocation
AttachConsole
ATTACH_PARENT_PROCESS
