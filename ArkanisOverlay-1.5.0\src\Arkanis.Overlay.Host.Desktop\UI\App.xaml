﻿<Application x:Class="Arkanis.Overlay.Host.Desktop.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- Referenced manually in `App.xaml.cs` because of DI -->
    <!-- xmlns:local="clr-namespace:Arkanis.Overlay.Host.Desktop" -->
    <!-- StartupUri="/Windows/OverlayWindow.xaml"> -->
    <Application.Resources>

    </Application.Resources>
</Application>
