<Window x:Class="Arkanis.Overlay.Host.Desktop.UI.Windows.AboutWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation/blazor"
        xmlns:ui="clr-namespace:Arkanis.Overlay.Host.Desktop.UI"
        mc:Ignorable="d"
        Title="About" Height="760" Width="600"
        ResizeMode="NoResize"
        FocusManager.FocusedElement="{Binding ElementName=BlazorWebView}">
    <Grid>
        <wpf:BlazorWebView HostPage="wwwroot\index.html"
                           x:Name="BlazorWebView"
                           Loaded="MainWindow_Loaded"
                           Panel.ZIndex="99"
                           Focusable="True"
                           Services="{DynamicResource services}"
                           StartPath="/about">
            <wpf:BlazorWebView.RootComponents>
                <wpf:RootComponent Selector="#app" ComponentType="{x:Type ui:RazorApp}" />
            </wpf:BlazorWebView.RootComponents>
        </wpf:BlazorWebView>
    </Grid>
</Window>
