﻿<Window x:Class="Arkanis.Overlay.Host.Desktop.UI.Windows.OverlayWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:blazor="clr-namespace:Microsoft.AspNetCore.Components.WebView.Wpf;assembly=Microsoft.AspNetCore.Components.WebView.Wpf"
        mc:Ignorable="d"
        Width="800"
        Height="600"
        ResizeMode="NoResize"
        Topmost="False"
        ShowInTaskbar="False"
        WindowStyle="None"
        Visibility="Hidden"
        Focusable="True"
        AllowsTransparency="True"
        FocusManager.FocusedElement="{Binding ElementName=BlazorWebView}"
        xmlns:icon="https://github.com/nullsoftware/TrayIcon"
        xmlns:ui="clr-namespace:Arkanis.Overlay.Host.Desktop.UI">
    <Window.Background>
        <SolidColorBrush Color="#010000" Opacity="0.05" />
    </Window.Background>
    <Grid>
        <blazor:BlazorWebView HostPage="wwwroot\index.html"
                              x:Name="BlazorWebView"
                              Loaded="MainWindow_Loaded"
                              Panel.ZIndex="99"
                              Focusable="True"
                              Services="{DynamicResource services}"
                              StartPath="/search">
            <blazor:BlazorWebView.RootComponents>
                <blazor:RootComponent Selector="#app" ComponentType="{x:Type ui:RazorApp}" />
            </blazor:BlazorWebView.RootComponents>
        </blazor:BlazorWebView>
    </Grid>
    <icon:TrayIconHandlers.TrayIcons>
        <icon:TrayIcon
            Title="Arkanis Overlay"
            IconSource="../../Resources/favicon.ico"
            NotificationServiceMemberPath="NotificationService"
            ContextMenuVariation="ContextMenuStrip">

            <icon:TrayIcon.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="About" Click="OnAboutCommand" />
                    <MenuItem Header="Preferences" Click="OnPreferenceCommand" />
                    <Separator />
                    <MenuItem Header="Exit" Click="OnExitCommand" />
                </ContextMenu>
            </icon:TrayIcon.ContextMenu>
        </icon:TrayIcon>

    </icon:TrayIconHandlers.TrayIcons>
</Window>
