<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <EnableSdkContainerSupport>true</EnableSdkContainerSupport>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Arkanis.Overlay.Components\Arkanis.Overlay.Components.csproj"/>
        <ProjectReference Include="..\Arkanis.Overlay.Infrastructure\Arkanis.Overlay.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Content Remove="packages.lock.json"/>
        <Content Include="..\..\.dockerignore">
          <Link>.dockerignore</Link>
        </Content>
        <None Include="packages.lock.json">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Octokit"/>
        <PackageReference Include="Serilog"/>
        <PackageReference Include="Serilog.Expressions" />
        <PackageReference Include="Serilog.Extensions.Hosting"/>
        <PackageReference Include="Serilog.Settings.Configuration"/>
        <PackageReference Include="Serilog.Sinks.Console"/>
    </ItemGroup>

</Project>
