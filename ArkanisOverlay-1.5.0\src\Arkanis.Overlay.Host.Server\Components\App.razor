﻿<!DOCTYPE html>
<html lang="en">

<!--suppress HtmlRequiredTitleElement -->
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <base href="/"/>
    <link rel="stylesheet" href="app.css"/>
    <link rel="stylesheet" href="Arkanis.Overlay.Host.Server.styles.css"/>
    <link rel="stylesheet" href="_content/Arkanis.Overlay.Components/css/shared.css"/>
    <link rel="stylesheet" href="_content/MudBlazor.FontIcons.MaterialSymbols/css/font.min.css"/>
    <link rel="stylesheet" href="_content/MudBlazor.FontIcons.MaterialIcons/css/font.min.css"/>
    <link rel="stylesheet" href="_content/MudBlazor/MudBlazor.min.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"/>
    <link rel="icon" type="image/png" href="favicon-96x96.png" sizes="96x96"/>
    <link rel="icon" type="image/svg+xml" href="favicon.svg"/>
    <link rel="shortcut icon" href="favicon.ico"/>
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png"/>
    <meta name="apple-mobile-web-app-title" content="Arkanis Overlay"/>
    <link rel="manifest" href="site.webmanifest"/>
    <HeadOutlet @rendermode="@InteractiveServer"/>
</head>

<body>
<Routes @rendermode="@InteractiveServer"/>
<script src="_framework/blazor.web.js"></script>
<script src="_content/Arkanis.Overlay.Components/js/EventInterop.js"></script>
<script src="_content/Blazor-Analytics/blazor-analytics.js"></script>
<script src="_content/MudBlazor/MudBlazor.min.js"></script>
</body>

</html>
