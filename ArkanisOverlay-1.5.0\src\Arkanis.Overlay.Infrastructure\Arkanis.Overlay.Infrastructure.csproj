﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Http.Extensions"/>
        <PackageReference Include="Microsoft.Data.Sqlite"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite"/>
        <PackageReference Include="Microsoft.Extensions.Http"/>
        <PackageReference Include="Quartz"/>
        <PackageReference Include="Quartz.Extensions.DependencyInjection"/>
        <PackageReference Include="Quartz.Extensions.Hosting"/>
        <PackageReference Include="Quartz.Jobs"/>
        <PackageReference Include="Scrutor"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Arkanis.Overlay.External.UEX\Arkanis.Overlay.External.UEX.csproj"/>
        <ProjectReference Include="..\Arkanis.Overlay.Domain\Arkanis.Overlay.Domain.csproj"/>
    </ItemGroup>

</Project>
