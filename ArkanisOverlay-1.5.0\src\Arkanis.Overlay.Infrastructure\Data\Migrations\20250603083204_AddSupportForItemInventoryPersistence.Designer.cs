﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Arkanis.Overlay.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Arkanis.Overlay.Infrastructure.Data.Migrations
{
    [DbContext(typeof(OverlayDbContext))]
    [Migration("20250603083204_AddSupportForItemInventoryPersistence")]
    partial class AddSupportForItemInventoryPersistence
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.16");

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.ExternalSourceDataCache", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("CachedUntil")
                        .HasColumnType("TEXT");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("JSONB");

                    b.ComplexProperty<Dictionary<string, object>>("DataAvailableState", "Arkanis.Overlay.Infrastructure.Data.Entities.ExternalSourceDataCache.DataAvailableState#ServiceAvailableState", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<DateTimeOffset>("UpdatedAt")
                                .HasColumnType("TEXT");

                            b1.Property<string>("Version")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("TEXT");
                        });

                    b.HasKey("Id");

                    b.ToTable("ExternalSourceDataCache");
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryEntityBase", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("TEXT");

                    b.Property<string>("GameEntityId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("ListId")
                        .HasColumnType("TEXT");

                    b.ComplexProperty<Dictionary<string, object>>("Quantity", "Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryEntityBase.Quantity#Quantity", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<int>("Amount")
                                .HasColumnType("INTEGER");

                            b1.Property<int>("Unit")
                                .HasColumnType("INTEGER");
                        });

                    b.HasKey("Id");

                    b.HasIndex("GameEntityId");

                    b.HasIndex("ListId");

                    b.ToTable("InventoryEntries");

                    b.HasDiscriminator().HasValue("Undefined_Undefined");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryListEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(10000)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("InventoryLists");
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.PhysicalCommodityInventoryEntryEntity", b =>
                {
                    b.HasBaseType("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryEntityBase");

                    b.Property<string>("LocationId")
                        .IsRequired()
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT")
                        .HasColumnName("LocationId");

                    b.HasDiscriminator().HasValue("Physical_Commodity");
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.PhysicalItemInventoryEntryEntity", b =>
                {
                    b.HasBaseType("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryEntityBase");

                    b.Property<string>("LocationId")
                        .IsRequired()
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT")
                        .HasColumnName("LocationId");

                    b.HasDiscriminator().HasValue("Physical_Item");
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.VirtualCommodityInventoryEntryEntity", b =>
                {
                    b.HasBaseType("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryEntityBase");

                    b.HasDiscriminator().HasValue("Virtual_Commodity");
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.VirtualItemInventoryEntryEntity", b =>
                {
                    b.HasBaseType("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryEntityBase");

                    b.HasDiscriminator().HasValue("Virtual_Item");
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryEntityBase", b =>
                {
                    b.HasOne("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryListEntity", "List")
                        .WithMany("Entries")
                        .HasForeignKey("ListId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("List");
                });

            modelBuilder.Entity("Arkanis.Overlay.Infrastructure.Data.Entities.InventoryEntryListEntity", b =>
                {
                    b.Navigation("Entries");
                });
#pragma warning restore 612, 618
        }
    }
}
