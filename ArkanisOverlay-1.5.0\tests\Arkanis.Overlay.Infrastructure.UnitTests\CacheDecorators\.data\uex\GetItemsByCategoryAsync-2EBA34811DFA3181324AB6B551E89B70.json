{"Result": {"data": [{"category": "Foods", "company_name": null, "date_added": 1703523015, "date_modified": 1747181418, "id": 205, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Buster's Chocolate Bar", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "busters-chocolate-bar", "url_store": "", "uuid": "fae33afb-e2e1-459d-9159-f9caf72533a5", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703523015, "date_modified": 1747181388, "id": 206, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON><PERSON> (New Austin Bold)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "snaggle-stick-new-austin-bold-", "url_store": "", "uuid": "c3b7ea0f-3ee0-4a07-a168-67377e09b62b", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703523016, "date_modified": 1747181390, "id": 207, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Citrus Beef Burrito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "citrus-beef-burrito", "url_store": "", "uuid": "1dfebe81-c6b7-4102-9b5e-0ebe2faa44d3", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703523016, "date_modified": 1747181391, "id": 208, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Vermillion Apple", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "vermillion-apple", "url_store": "", "uuid": "96c4cbe8-61a1-4de3-b624-453bfd576bdd", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703523016, "date_modified": 1747181391, "id": 209, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Watermelon (Slice)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "watermelon-slice-", "url_store": "", "uuid": "4382c1cb-16f9-4649-8b2d-f902e2590a15", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703523016, "date_modified": 1747181394, "id": 210, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "godmother-sandwich", "url_store": "", "uuid": "61b3eb5a-7edc-4803-b564-1874c70d7204", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703532038, "date_modified": 1747181384, "id": 559, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Cal-O-Meal 'Chocolate Deluxe' Protein Bar", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "cal-o-meal-chocolate-deluxe-protein-bar", "url_store": null, "uuid": null, "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703532038, "date_modified": 1747181385, "id": 560, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Cal-O-<PERSON><PERSON> '<PERSON><PERSON>' <PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "cal-o-meal-lunes-protein-bar", "url_store": null, "uuid": null, "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703532038, "date_modified": 1747181386, "id": 561, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Karoby Energy Bar (Tahini & Carob)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "karoby-energy-bar-tahini--carob-", "url_store": "", "uuid": "e7746c26-4094-46e8-9784-40c596b49560", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703532038, "date_modified": 1747181388, "id": 562, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "OneMeal Nutrition Bar (Fried Tofu)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "onemeal-nutrition-bar-fried-tofu-", "url_store": "", "uuid": "6dc7fccb-9e6a-437c-924b-ade224667b66", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703532038, "date_modified": 1747181419, "id": 563, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "ReadyMeal (Beef Chunks)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "readymeal-beef-chunks-", "url_store": "", "uuid": "72256518-19bd-47ac-b39f-0f7504b025f3", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703532038, "date_modified": 1740442126, "id": 564, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "ReadyMeal (Vegetarian)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "readymeal-vegetarian-", "url_store": "", "uuid": "d9290fb6-3b8a-45bd-ac1e-c6f41d182951", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1703696568, "date_modified": 1747181386, "id": 1599, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Karoby Energy Bar (Cranberry, Green Tea & Carob)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "karoby-energy-bar-cranberry-green-tea--carob-", "url_store": "", "uuid": "b2497354-217a-4310-8ab8-d3c633d2e8a2", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703797338, "date_modified": 1747181389, "id": 1763, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON><PERSON> (Pepper^3)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "snaggle-protein-stick-pepper-3-", "url_store": "", "uuid": "4a1d5697-fb73-45e0-99ee-59afddbe1bb3", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703797338, "date_modified": 1747181389, "id": 1764, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON><PERSON> <PERSON> (Smoke Daddy)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "snaggle-stick-smoke-daddy-", "url_store": "", "uuid": "*************-46f6-8fb2-e7eaf2e60a42", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703911938, "date_modified": 1747181385, "id": 1782, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Cal-<PERSON>-<PERSON><PERSON> '<PERSON><PERSON>' <PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "cal-o-meal-vanilla-protein-bar", "url_store": null, "uuid": null, "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1703911938, "date_modified": 1747181386, "id": 1783, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "OneMeal Nutrition Bar (Roast Chicken)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "onemeal-nutrition-bar-roast-chicken-", "url_store": "", "uuid": "b90ef8cd-8c71-4e37-b354-fe6fab41c07a", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454555, "date_modified": 1747181387, "id": 2174, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "OneMeal Nutrition Bar (Spicy Salmon)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "onemeal-nutrition-bar-spicy-salmon-", "url_store": "", "uuid": "64bf56d9-4f9b-4510-820a-7807ce3c5ff8", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454555, "date_modified": 1747181387, "id": 2175, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "OneMeal Nutrition Bar (Grilled Steak)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "onemeal-nutrition-bar-grilled-steak-", "url_store": "", "uuid": "b63510ef-b886-42a9-beb9-5d1cfe0ad085", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454555, "date_modified": 1747181388, "id": 2176, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON><PERSON> Stick (Original)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "snaggle-stick-original-", "url_store": "", "uuid": "b0e04746-9e54-42e7-a95a-f212140816d0", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181389, "id": 2177, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>ggle Stick (<PERSON><PERSON><PERSON>)", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "snaggle-stick-tikoro-bite-", "url_store": "", "uuid": "92ef1f8b-0fb4-49cc-9f7b-b9bd023c1e6b", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181391, "id": 2178, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Chile Birria <PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "chile-birria-burrito", "url_store": "", "uuid": "e5d0e1cf-4960-4cfc-970c-397d1d31760d", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181391, "id": 2179, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "musaka-burrito", "url_store": "", "uuid": "2a4ca069-db0b-4f54-bbdc-087c488d87b8", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181392, "id": 2180, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Classic Dog", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "classic-dog", "url_store": "", "uuid": "bf832a70-cbed-4be4-a867-af6715bdb57f", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181392, "id": 2181, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Breakfast Dog", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "breakfast-dog", "url_store": "", "uuid": "1af97566-2ca7-4ac8-ad4f-f7ec6421f331", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181393, "id": 2182, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "chili-dog", "url_store": "", "uuid": "2d7b0668-af02-41ed-b712-ef553c79a99a", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181393, "id": 2183, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Double Dog", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "double-dog", "url_store": "", "uuid": "aa514342-dc16-44ce-afc7-34b962228b5e", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181393, "id": 2184, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Veggie Dog", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "veggie-dog", "url_store": "", "uuid": "63817eec-15df-4121-9e62-011e8efcc7c5", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181394, "id": 2185, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Avocado and Corn Sandwich", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "avocado-and-corn-sandwich", "url_store": "", "uuid": "33ce1b51-c592-4024-8040-fd2a26cb4b9b", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181394, "id": 2186, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Bacon Club Sandwich", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "bacon-club-sandwich", "url_store": "", "uuid": "f243d9b6-39c3-43e0-beb1-ca267a122a23", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181395, "id": 2187, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Bologna Sandwich", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "bologna-sandwich", "url_store": "", "uuid": "4ef4f879-**************-e81e5122bad5", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181395, "id": 2188, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Me<PERSON><PERSON>z <PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "merguez-sandwich", "url_store": "", "uuid": "a1ebd3de-aa8d-46f6-a3c1-6a66737a4435", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181396, "id": 2189, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Pork Katsu Sandwich", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "pork-katsu-sandwich", "url_store": "", "uuid": "68734d4c-9716-4aa7-9d68-81a7f0cfa27f", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706454556, "date_modified": 1747181396, "id": 2190, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Turkey Sandwich", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "turkey-sandwich", "url_store": "", "uuid": "4b02f40a-c9e0-4fa7-86d3-a512c9549d4a", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1706770277, "date_modified": 1740471257, "id": 2302, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Ma's Ready to Eat Chicken Patty Dinner", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "mas-ready-to-eat-chicken-patty-dinner", "url_store": "", "uuid": "a8b8db91-ab89-45f1-912d-6b21459239b6", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708500817, "date_modified": 1740471414, "id": 2646, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON>ied <PERSON> with <PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "fried-seanut-with-sauce", "url_store": "", "uuid": "e52e45bc-99ea-4479-b0c3-bdf6e6342957", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708500864, "date_modified": 1740471415, "id": 2647, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "aloprat-skewer", "url_store": "", "uuid": "94c2ca2d-8725-4261-bf45-2e700ce98074", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708500911, "date_modified": 1740471415, "id": 2648, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Meat Skewer", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "meat-skewer", "url_store": "", "uuid": "37bc36b8-3b35-461e-a553-3a83130fd2e7", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708500947, "date_modified": 1740471416, "id": 2649, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Nomo Grub Packet", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "nomo-grub-packet", "url_store": "", "uuid": "b830a23a-d949-431b-a4bd-042969f39762", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708500993, "date_modified": 1740471416, "id": 2650, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Carafi Packet", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "carafi-packet", "url_store": "", "uuid": "2a5c0893-9cef-487c-ad6c-06608307d669", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708501019, "date_modified": 1740471417, "id": 2651, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "carafi-cube", "url_store": "", "uuid": "3317c57a-5bfc-4dea-8a2e-9394f0626de6", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708501051, "date_modified": 1740471417, "id": 2652, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Carafi Noodles", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "carafi-noodles", "url_store": "", "uuid": "81af2e1b-7776-4142-bed4-be9c8c3789ce", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708796642, "date_modified": 1740471442, "id": 2714, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Ermer Family Farms Lunes Ice Cream", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "ermer-family-farms-lunes-ice-cream", "url_store": "", "uuid": "9a3fab17-5efc-40c5-ac50-f4c752c0a7bf", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708796642, "date_modified": 1740471443, "id": 2715, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Ermer Family Farms Chibanzoo Ice Cream", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "ermer-family-farms-chibanzoo-ice-cream", "url_store": "", "uuid": "30afcb8f-3afc-42c7-a860-34c630729823", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708796643, "date_modified": 1740471443, "id": 2716, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON>rmer Family Farms Chocolate Ice Cream", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "ermer-family-farms-chocolate-ice-cream", "url_store": "", "uuid": "ff44929e-c067-4a2d-95cf-41809d61dde0", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708796643, "date_modified": 1740471444, "id": 2717, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>er Family Farms Coffee Ice Cream", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "ermer-family-farms-coffee-ice-cream", "url_store": "", "uuid": "9b0bba60-25e5-4bb1-a31b-cafdbca14271", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1708796643, "date_modified": 1740471444, "id": 2718, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON>rmer Family Farms Fat Free Ice Cream", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "ermer-family-farms-fat-free-ice-cream", "url_store": "", "uuid": "7441f131-fa39-452c-89a7-ef23b41fee52", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710178643, "date_modified": 1747181390, "id": 2818, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Dak Galbi Chicken Burrito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "dak-galbi-chicken-burrito", "url_store": "", "uuid": "d030076e-3747-4b1b-bb81-7eb1e5f1f98d", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1710179582, "date_modified": 1740471489, "id": 2819, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "melty-dog", "url_store": "", "uuid": "859ee528-a8ea-4cd7-a8bb-ae69adbba264", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710179695, "date_modified": 1740471490, "id": 2820, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Cruiser Dog", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "cruiser-dog", "url_store": "", "uuid": "bbe3f611-07ba-4029-88b3-1b8bd300562b", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710179814, "date_modified": 1740471490, "id": 2821, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Yakisoba Dog", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "yakisoba-dog", "url_store": "", "uuid": "4fe49ee9-b1e9-4403-86c6-ada48b076142", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710288716, "date_modified": 1740471503, "id": 2846, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Tilapia Verde Burrito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "tilapia-verde-burrito", "url_store": "", "uuid": "0f7ea96f-c501-4a35-b3e5-2066c3de469b", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710288847, "date_modified": 1740471503, "id": 2847, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Carnita-ritas Burrito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "carnita-ritas-burrito", "url_store": "", "uuid": "82706d17-3eba-4d0a-b1ab-e679c858c8cf", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710289000, "date_modified": 1740471504, "id": 2848, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Shrimp Typhoon <PERSON>ito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "shrimp-typhoon-burrito", "url_store": "", "uuid": "d3e2f617-458b-4351-964f-e41b81d2267d", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710289049, "date_modified": 1740471504, "id": 2849, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Strog-N-Off Burrito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "strog-n-off-burrito", "url_store": "", "uuid": "07c9d587-6da6-4c21-82d0-bd718e3ccb20", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710289185, "date_modified": 1740471505, "id": 2850, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Madras Asada Burrito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "madras-asada-burrito", "url_store": "", "uuid": "1f634ecf-d8d1-44cd-b5fa-ff9f5cea9074", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1710485928, "date_modified": 1747181291, "id": 2896, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Chicken Wham", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "chicken-wham", "url_store": "", "uuid": "9e4b35dd-85b8-4fd8-bf63-68b6a2c8a828", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1710486629, "date_modified": 1747181293, "id": 2932, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Whamburger D-Lux", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "whamburger-d-lux", "url_store": "", "uuid": "d5301c1a-01c1-41f8-82e4-9561dc25fb56", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1710486689, "date_modified": 1747181292, "id": 2933, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Whamburger", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "whamburger", "url_store": "", "uuid": "cffa8202-6e3b-4354-86e7-8d73eefeb582", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1710486733, "date_modified": 1747181292, "id": 2934, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "veggie-wham", "url_store": "", "uuid": "1430c4f4-9974-4c28-9bba-8e02a4670e91", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Foods", "company_name": null, "date_added": 1712296646, "date_modified": 1745723946, "id": 3362, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Ma\"s Ready To Eat Fish Home Stew", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "ma-s-ready-to-eat-fish-home-stew", "url_store": null, "uuid": null, "vehicle_name": null, "game_version": null}, {"category": "Foods", "company_name": null, "date_added": 1712296723, "date_modified": 1740471709, "id": 3363, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Chicken Pesto Omni Pack", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "chicken-pesto-omni-pack", "url_store": "", "uuid": "fed3908f-5c2f-4aa2-ba0b-2e7c9f830d98", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1732313490, "date_modified": 1740471906, "id": 4045, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "seanut", "url_store": "", "uuid": "032829bc-5eab-4fd5-81cd-95fa8e93eaef", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1732314905, "date_modified": 1740471906, "id": 4046, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON>ied <PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "fried-seanut", "url_store": "", "uuid": "e6084232-38a4-407a-8029-5dec1c67cbd0", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1734389220, "date_modified": 1740471989, "id": 4249, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Cheddar <PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "cheddar-ham-burrito", "url_store": "", "uuid": "2034cae6-00a2-48c8-8c4a-b7cbb1ecd1e1", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1734389220, "date_modified": 1740471990, "id": 4250, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Locked-on <PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "locked-on-lentil-burrito", "url_store": "", "uuid": "ddd58477-b505-4093-a0a0-7a83f67ed55f", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Foods", "company_name": null, "date_added": 1734389220, "date_modified": 1740471991, "id": 4251, "id_category": 63, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "PPC Burrito", "notification": null, "screenshot": "", "section": "Miscellaneous", "size": null, "slug": "ppc-burrito", "url_store": "", "uuid": "f3b88160-c5ac-495c-a2e6-2f20d32f0324", "vehicle_name": null, "game_version": "4.0.1"}], "http_code": 200, "message": "", "status": "ok"}, "StatusCode": 200, "Headers": {"Date": ["Thu, 22 May 2025 06:19:39 GMT"], "Transfer-Encoding": ["chunked"], "Connection": ["keep-alive"], "Server": ["cloudflare"], "Nel": ["{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"], "Cf-Ray": ["943a34038d8a23fa-PRG"], "Strict-Transport-Security": ["max-age=31536000"], "Cache-Control": ["public, must-revalidate, max-age=86400"], "Access-Control-Allow-Origin": ["*"], "X-Frame-Options": ["SAMEORIGIN"], "Content-Security-Policy": ["frame-ancestors 'self'"], "Vary": ["Accept-Encoding"], "Cf-Cache-Status": ["DYNAMIC"], "Report-To": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=HQdZEfW0XYooma%2B6Jv5CLSGW%2BG2DRr%2FtwQbG5VebXm%2BhaY1LAftUCx0lCYT7NbQPVf%2B6vmBWadjQtC2sZmYrAswMr7FVvFlOb3cC%2B00nvw%2BKVzdUsSmKLMR2v671zLn3NCuklw%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}"], "Alt-Svc": ["h3=\":443\""], "Server-Timing": ["cfL4;desc=\"?proto=TCP&rtt=4107&min_rtt=3805&rtt_var=169&sent=429&recv=189&lost=0&retrans=0&sent_bytes=568368&recv_bytes=1953&delivery_rate=33556789&cwnd=386&unsent_bytes=0&cid=bc9f5fda215b8a87&ts=2593&x=0\""], "Content-Type": ["application/json"], "Expires": ["Fri, 23 May 2025 06:19:39 GMT"]}}