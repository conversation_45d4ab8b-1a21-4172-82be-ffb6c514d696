{"Result": {"data": [{"category": "Container", "company_name": null, "date_added": 1703524580, "date_modified": 1747181245, "id": 243, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Stor*All 1 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "stor-all-1-scu-self-storage-container", "url_store": "", "uuid": "44512a35-3a37-48b9-bf0e-c1bf87b4503b", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Container", "company_name": null, "date_added": 1703534478, "date_modified": 1747181246, "id": 608, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Stor*All 2 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "stor-all-2-scu-self-storage-container", "url_store": "", "uuid": "d6f1b600-353d-42d7-a592-450172889f00", "vehicle_name": null, "game_version": "4.1.1"}, {"category": "Container", "company_name": null, "date_added": 1703543531, "date_modified": 1740445315, "id": 881, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Stor*All 4 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "stor-all-4-scu-self-storage-container", "url_store": "", "uuid": "ced4ab55-c8d0-42d9-86d9-71f97eb38872", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Container", "company_name": null, "date_added": 1703543531, "date_modified": 1740445316, "id": 882, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Stor*All 8 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "stor-all-8-scu-self-storage-container", "url_store": "", "uuid": "64d1c96f-a7c4-492f-a12e-91699e3b01bc", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Container", "company_name": null, "date_added": 1726705733, "date_modified": 1726705803, "id": 3917, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 1, "is_exclusive_subscriber": 0, "name": "Salvaged Skull 1 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "salvaged-skull-1-scu-self-storage-container", "url_store": "https://robertsspaceindustries.com/pledge/Packs/Scourge-Of-The-Stars-Pack-Warbond", "uuid": null, "vehicle_name": null, "game_version": "3.24.1"}, {"category": "Container", "company_name": null, "date_added": 1726705806, "date_modified": 1726705816, "id": 3918, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 1, "is_exclusive_subscriber": 0, "name": "Salvaged Skull 2 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "salvaged-skull-2-scu-self-storage-container", "url_store": "https://robertsspaceindustries.com/pledge/Packs/Scourge-Of-The-Stars-Pack-Warbond", "uuid": null, "vehicle_name": null, "game_version": "3.24.1"}, {"category": "Container", "company_name": null, "date_added": 1726705860, "date_modified": 1726705871, "id": 3919, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 1, "is_exclusive_subscriber": 0, "name": "Salvaged Skull 4 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "salvaged-skull-4-scu-self-storage-container", "url_store": "https://robertsspaceindustries.com/pledge/Packs/Scourge-Of-The-Stars-Pack-Warbond", "uuid": null, "vehicle_name": null, "game_version": "3.24.1"}, {"category": "Container", "company_name": null, "date_added": 1726705897, "date_modified": 1726705911, "id": 3920, "id_category": 64, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 1, "is_exclusive_subscriber": 0, "name": "Salvaged Skull 8 SCU Self-Storage Container", "notification": null, "screenshot": "", "section": "Utility", "size": null, "slug": "salvaged-skull-8-scu-self-storage-container", "url_store": "https://robertsspaceindustries.com/pledge/Packs/Scourge-Of-The-Stars-Pack-Warbond", "uuid": null, "vehicle_name": null, "game_version": "3.24.1"}], "http_code": 200, "message": "", "status": "ok"}, "StatusCode": 200, "Headers": {"Date": ["Thu, 22 May 2025 06:19:39 GMT"], "Transfer-Encoding": ["chunked"], "Connection": ["keep-alive"], "Server": ["cloudflare"], "Nel": ["{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"], "Cf-Ray": ["943a34047a04cd2b-PRG"], "Strict-Transport-Security": ["max-age=31536000"], "Cache-Control": ["public, must-revalidate, max-age=86400"], "Access-Control-Allow-Origin": ["*"], "X-Frame-Options": ["SAMEORIGIN"], "Content-Security-Policy": ["frame-ancestors 'self'"], "Vary": ["Accept-Encoding"], "Cf-Cache-Status": ["DYNAMIC"], "Report-To": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=V92UYiUStF1gfcgokJhii2MhEVr6YIrdwe2gc%2FwelX1e7h1U3Veapy8v6nRs04TayMbMwPwzBW4LufcHlwnHrSpIv3vyUTrY2MGazodgIAst%2B56KNZB8OKM%2B3CHK63jC5Rf3vA%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}"], "Alt-Svc": ["h3=\":443\""], "Server-Timing": ["cfL4;desc=\"?proto=TCP&rtt=4408&min_rtt=4218&rtt_var=153&sent=420&recv=179&lost=0&retrans=0&sent_bytes=550192&recv_bytes=2069&delivery_rate=25768402&cwnd=349&unsent_bytes=0&cid=e558bd9e947b5640&ts=2738&x=0\""], "Content-Type": ["application/json"], "Expires": ["Fri, 23 May 2025 06:19:39 GMT"]}}