{"Result": {"data": [{"category": "Eyeware", "company_name": "Derion", "date_added": 1707985936, "date_modified": 1740471346, "id": 2480, "id_category": 68, "id_company": 71, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 1, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "<PERSON><PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "jacopo-monocle", "url_store": "", "uuid": "f9c45e20-1522-4f87-9f9c-932d089514e0", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Eyeware", "company_name": "<PERSON><PERSON>", "date_added": 1708126687, "date_modified": 1740471359, "id": 2532, "id_category": 68, "id_company": 93, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 1, "name": "<PERSON><PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "sarrab-glasses", "url_store": "https://robertsspaceindustries.com/pledge/Subscribers-Store/Urban-Collection-By-Element-Authority", "uuid": "70262488-00c4-4607-b735-7528736c799a", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Eyeware", "company_name": "Spar <PERSON>", "date_added": 1708126724, "date_modified": 1740471359, "id": 2534, "id_category": 68, "id_company": 227, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 1, "name": "AvantX Glasses", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "avantx-glasses", "url_store": "https://robertsspaceindustries.com/pledge/Subscribers-Store/Urban-Collection-By-Element-Authority", "uuid": "eddd508a-ed10-4b07-bfc5-dad7ae6fa462", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Eyeware", "company_name": null, "date_added": 1708126770, "date_modified": 1715784734, "id": 2535, "id_category": 68, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 1, "name": "<PERSON><PERSON>", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "avalos-scout-glasses", "url_store": "https://robertsspaceindustries.com/pledge/Subscribers-Store/Adventurer-Collection-By-Element-Authority", "uuid": null, "vehicle_name": null, "game_version": null}, {"category": "Eyeware", "company_name": null, "date_added": 1708126788, "date_modified": 1715784744, "id": 2536, "id_category": 68, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 1, "name": "Haruspec Glasses", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "haruspec-glasses", "url_store": "https://robertsspaceindustries.com/pledge/Subscribers-Store/Adventurer-Collection-By-Element-Authority", "uuid": null, "vehicle_name": null, "game_version": null}, {"category": "Eyeware", "company_name": null, "date_added": 1708126814, "date_modified": 1740471360, "id": 2537, "id_category": 68, "id_company": 0, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 1, "name": "Aponte Explorer Goggles", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "aponte-explorer-goggles", "url_store": "https://robertsspaceindustries.com/pledge/Subscribers-Store/Adventurer-Collection-By-Element-Authority", "uuid": "66b88d76-b20f-4880-9f98-11835398af3e", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Eyeware", "company_name": "Octagon", "date_added": 1710312693, "date_modified": 1740471505, "id": 2851, "id_category": 68, "id_company": 189, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 0, "name": "Apex Glasses", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "apex-glasses", "url_store": "", "uuid": "ff0582a1-5a7d-40b4-b7bc-b1e9ead8f83f", "vehicle_name": null, "game_version": "4.0.1"}, {"category": "Eyeware", "company_name": "<PERSON><PERSON>", "date_added": 1712328433, "date_modified": 1740471729, "id": 3453, "id_category": 68, "id_company": 93, "id_parent": 0, "id_vehicle": 0, "is_exclusive_concierge": 0, "is_exclusive_pledge": 0, "is_exclusive_subscriber": 1, "name": "Ectio Glasses", "notification": null, "screenshot": "", "section": "Clothing", "size": null, "slug": "ectio-glasses", "url_store": "https://robertsspaceindustries.com/pledge/Subscribers-Store/Urban-Collection-By-Element-Authority", "uuid": "2ddeaeb6-fd84-450a-9dbb-9f9869ed68a3", "vehicle_name": null, "game_version": "4.0.1"}], "http_code": 200, "message": "", "status": "ok"}, "StatusCode": 200, "Headers": {"Date": ["Thu, 22 May 2025 06:19:39 GMT"], "Transfer-Encoding": ["chunked"], "Connection": ["keep-alive"], "Server": ["cloudflare"], "Nel": ["{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"], "Cf-Ray": ["943a34047bfe8176-PRG"], "Strict-Transport-Security": ["max-age=31536000"], "Cache-Control": ["public, must-revalidate, max-age=86400"], "Access-Control-Allow-Origin": ["*"], "X-Frame-Options": ["SAMEORIGIN"], "Content-Security-Policy": ["frame-ancestors 'self'"], "Vary": ["Accept-Encoding"], "Cf-Cache-Status": ["DYNAMIC"], "Report-To": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=amMCag%2BBSekllA9lOfP1GcNM6OfG4yyDP8adgqFMVzG59Gk4dsYwyUaG2mhFyCw8dbUWZ3FZ7paQHc8y2eB5%2BGXc4hcECkrEHI86GTO%2BEgYCOEUe8lPlbRZu9UyjlaAS8hT7fg%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}"], "Alt-Svc": ["h3=\":443\""], "Server-Timing": ["cfL4;desc=\"?proto=TCP&rtt=8150&min_rtt=4232&rtt_var=6566&sent=351&recv=138&lost=0&retrans=0&sent_bytes=458647&recv_bytes=1798&delivery_rate=23925710&cwnd=336&unsent_bytes=0&cid=495ace438151ab96&ts=2716&x=0\""], "Content-Type": ["application/json"], "Expires": ["Fri, 23 May 2025 06:19:39 GMT"]}}