import discord
from discord.ext import commands
from discord import app_commands
import asyncio
import datetime
import json
import io

class ApplicationView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Apply", style=discord.ButtonStyle.primary, custom_id="apply_button")
    async def apply_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            # Questions to ask
            questions = [
                "Are you ready to apply for Aegis Nox? (yes to continue, no to cancel)",
                "Age:",
                "Do you have a Mic?(Must be yes)",
                "RSI Profile Link:",
                "Why do you want to join Aegis Nox?",
                "What experience do you have on Star Citizen?(New Player? Vet? Etc)",
                "What would you consider your role would be in a group?",
                "You and your team have to enter a room where u have a 80% chance of encountering enemy fire would you want to go first second etc and why",
                "What are your Active Hours? (ex. 8pm US EST-10pm US EST everyday)",
                "What role would you like to be a part of? (Ground Forces, Navy, Industry/Logistics)",
                "What are your expectations with Aegis Nox?",
                "Additional Info:"
            ]

            answers = []
            
            # Send initial confirmation
            await interaction.response.send_message("I've sent you a DM to start the application process!", ephemeral=True)
            
            # Ask each question in DMs
            for question in questions:
                # Send question
                dm_message = await interaction.user.send(question)
                
                def check(m):
                    return m.author == interaction.user and isinstance(m.channel, discord.DMChannel)

                try:
                    # Wait for response
                    response = await interaction.client.wait_for('message', timeout=300.0, check=check)
                    
                    # Check first question for cancellation
                    if question == questions[0] and response.content.lower() != "yes":
                        await interaction.user.send("Application cancelled.")
                        return
                    
                    # For mic question, validate it's yes
                    if question == questions[2] and response.content.lower() != "yes":
                        await interaction.user.send("Sorry, a microphone is required to join Aegis Nox. Application cancelled.")
                        return
                    
                    answers.append(response.content)
                    
                except asyncio.TimeoutError:
                    await interaction.user.send("Application timed out. Please try again.")
                    return

            # Create application channel
            category = interaction.guild.get_channel(1389660299367284746)
            
            # Get roles
            founder_role = interaction.guild.get_role(1389657163252760636)
            general_role = interaction.guild.get_role(1389657332782334112)
            commander_role = interaction.guild.get_role(1389657392685252659)

            # Set up channel permissions
            overwrites = {
                interaction.guild.default_role: discord.PermissionOverwrite(read_messages=False),
                interaction.user: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    read_message_history=True,
                    attach_files=True,
                    embed_links=True
                ),
                founder_role: discord.PermissionOverwrite(read_messages=True, send_messages=True),
                general_role: discord.PermissionOverwrite(read_messages=True, send_messages=True),
                commander_role: discord.PermissionOverwrite(read_messages=True, send_messages=True)
            }

            # Find next available channel number
            existing_channels = [c for c in category.channels if c.name.startswith(f"{interaction.user.name}-application-")]
            channel_number = 1
            while any(c.name == f"{interaction.user.name}-application-{channel_number}" for c in existing_channels):
                channel_number += 1

            # Create channel
            channel = await interaction.guild.create_text_channel(
                f"{interaction.user.name}-application-{channel_number}",
                category=category,
                overwrites=overwrites
            )

            # Create embed with application answers
            embed = discord.Embed(
                title="Aegis Nox Application",
                color=discord.Color.default()
            )
            
            for question, answer in zip(questions[1:], answers[1:]):  # Skip the "Are you ready" question
                embed.add_field(name=question, value=answer, inline=False)

            # Send initial ping and embed with buttons
            ping_message = f"{interaction.user.mention} {founder_role.mention} {general_role.mention} {commander_role.mention}"
            await channel.send(ping_message)
            await channel.send(embed=embed, view=ApplicationActionView(interaction.user))
            
            # Send completion message with channel link to user
            await interaction.user.send(f"Application Complete! Your application channel has been created: {channel.mention}")

        except discord.Forbidden:
            await interaction.followup.send("I couldn't send you a DM. Please enable DMs from server members and try again.", ephemeral=True)
        except Exception as e:
            await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

class ApplicationActionView(discord.ui.View):
    def __init__(self, applicant):
        super().__init__(timeout=None)
        self.applicant = applicant
        self.claimed_by = None
        self.claimed_at = None

    @discord.ui.button(label="Claim", style=discord.ButtonStyle.primary, custom_id="claim_application")
    async def claim(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return
        
        self.claimed_by = interaction.user
        self.claimed_at = datetime.datetime.now()
        embed = interaction.message.embeds[0]
        embed.add_field(name="Claimed By", value=f"{interaction.user.mention} at {self.claimed_at.strftime('%Y-%m-%d %H:%M:%S')}", inline=False)
        await interaction.message.edit(embed=embed)
        await interaction.response.send_message("You have claimed this application.", ephemeral=True)

    @discord.ui.button(label="Close", style=discord.ButtonStyle.danger, custom_id="close_application")
    async def close(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return

        class CloseModal(discord.ui.Modal, title="Close Application"):
            def __init__(self, parent_view):
                super().__init__()
                self.parent_view = parent_view

            reason = discord.ui.TextInput(
                label="Reason for closing",
                style=discord.TextStyle.paragraph,
                required=True
            )

            async def on_submit(self, interaction: discord.Interaction):
                # Respond to the interaction immediately to prevent timeout
                await interaction.response.send_message("Processing application closure...", ephemeral=True)

                close_time = datetime.datetime.now()

                # Create transcript embed
                transcript_embed = discord.Embed(
                    title="Application Transcript",
                    color=discord.Color.default()
                )
                transcript_embed.add_field(name="Applicant", value=f"{self.parent_view.applicant.mention}", inline=False)
                if self.parent_view.claimed_by:
                    transcript_embed.add_field(name="Claimed By", value=f"{self.parent_view.claimed_by.mention}", inline=False)
                    transcript_embed.add_field(name="Claimed At", value=self.parent_view.claimed_at.strftime("%Y-%m-%d %H:%M:%S"), inline=False)
                transcript_embed.add_field(name="Closed By", value=f"{interaction.user.mention}", inline=False)
                transcript_embed.add_field(name="Closed At", value=close_time.strftime("%Y-%m-%d %H:%M:%S"), inline=False)
                transcript_embed.add_field(name="Close Reason", value=self.reason.value, inline=False)

                # Create text transcript
                transcript_text = f"Transcript for {interaction.channel.name}\n"
                transcript_text += f"Created at: {interaction.channel.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                transcript_text += "-" * 50 + "\n\n"

                messages = [message async for message in interaction.channel.history(limit=None, oldest_first=True)]
                
                for msg in messages:
                    timestamp = msg.created_at.strftime("%Y-%m-%d %H:%M:%S")
                    transcript_text += f"[{timestamp}] {msg.author.name}: {msg.content}\n"
                    for embed in msg.embeds:
                        if embed.title:
                            transcript_text += f"Embed Title: {embed.title}\n"
                        if embed.description:
                            transcript_text += f"Embed Description: {embed.description}\n"
                        for field in embed.fields:
                            transcript_text += f"{field.name}: {field.value}\n"
                    transcript_text += "\n"

                transcript_file = discord.File(
                    io.StringIO(transcript_text),
                    filename=f"transcript-{interaction.channel.name}.txt"
                )

                logs_channel = interaction.guild.get_channel(1389705853095120942)
                await logs_channel.send(embed=transcript_embed, file=transcript_file)

                try:
                    await self.parent_view.applicant.send(embed=transcript_embed, file=discord.File(
                        io.StringIO(transcript_text),
                        filename=f"transcript-{interaction.channel.name}.txt"
                    ))
                    await interaction.followup.send("Application closed and transcript sent.", ephemeral=True)
                    await interaction.channel.send("This application has been closed. This channel will be deleted in 5 seconds.")
                    await asyncio.sleep(5)
                    await interaction.channel.delete()
                except discord.Forbidden:
                    await interaction.followup.send("Failed to DM the user, but the channel will be deleted.", ephemeral=True)
                    await interaction.channel.delete()

        await interaction.response.send_modal(CloseModal(self))

    @discord.ui.button(label="Accept", style=discord.ButtonStyle.success, custom_id="accept_application")
    async def accept(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return

        # Respond to the interaction immediately to prevent timeout
        await interaction.response.send_message("Processing application acceptance...", ephemeral=True)

        # Get the roles
        aegis_nox_role = interaction.guild.get_role(1389657832063897721)
        initiate_role = interaction.guild.get_role(1389657645887000762)

        try:
            # Add roles
            await self.applicant.add_roles(aegis_nox_role, initiate_role)
            
            # Create transcript embed
            transcript_embed = discord.Embed(
                title="Application Transcript",
                color=discord.Color.default()
            )
            transcript_embed.add_field(name="Status", value="Accepted", inline=False)
            transcript_embed.add_field(name="Applicant", value=f"{self.applicant.mention}", inline=False)
            if self.claimed_by:
                transcript_embed.add_field(name="Claimed By", value=f"{self.claimed_by.mention}", inline=False)
                transcript_embed.add_field(name="Claimed At", value=self.claimed_at.strftime("%Y-%m-%d %H:%M:%S"), inline=False)
            transcript_embed.add_field(name="Accepted By", value=f"{interaction.user.mention}", inline=False)
            transcript_embed.add_field(name="Accepted At", value=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), inline=False)

            # Create text transcript
            transcript_text = f"Transcript for {interaction.channel.name}\n"
            transcript_text += f"Created at: {interaction.channel.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            transcript_text += "-" * 50 + "\n\n"

            messages = [message async for message in interaction.channel.history(limit=None, oldest_first=True)]
            
            for msg in messages:
                timestamp = msg.created_at.strftime("%Y-%m-%d %H:%M:%S")
                transcript_text += f"[{timestamp}] {msg.author.name}: {msg.content}\n"
                for embed in msg.embeds:
                    if embed.title:
                        transcript_text += f"Embed Title: {embed.title}\n"
                    if embed.description:
                        transcript_text += f"Embed Description: {embed.description}\n"
                    for field in embed.fields:
                        transcript_text += f"{field.name}: {field.value}\n"
                transcript_text += "\n"

            transcript_file = discord.File(
                io.StringIO(transcript_text),
                filename=f"transcript-{interaction.channel.name}.txt"
            )

            # Send transcript to logs channel
            logs_channel = interaction.guild.get_channel(1389705853095120942)
            await logs_channel.send(embed=transcript_embed, file=transcript_file)
            
            # Send welcome message and transcript to applicant
            welcome_embed = discord.Embed(
                title="Welcome to Aegis Nox!",
                description=f"Congratulations! Your application has been accepted. Welcome to the organization!\n\nPlease introduce yourself in <#1389725267777552424> and say hey in <#1389659214817001532>",
                color=discord.Color.default()
            )
            await self.applicant.send(embed=welcome_embed)
            await self.applicant.send(embed=transcript_embed, file=discord.File(
                io.StringIO(transcript_text),
                filename=f"transcript-{interaction.channel.name}.txt"
            ))
            
            # Close the channel
            await interaction.followup.send("Application accepted! The channel will be closed in 5 seconds.", ephemeral=True)
            await interaction.channel.send("Application accepted! This channel will be deleted in 5 seconds.")
            await asyncio.sleep(5)
            await interaction.channel.delete()

        except discord.Forbidden:
            await interaction.followup.send("Failed to add roles or send welcome message. Please check permissions.", ephemeral=True)

    @discord.ui.button(label="Deny", style=discord.ButtonStyle.danger, custom_id="deny_application")
    async def deny(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return

        class DenyModal(discord.ui.Modal, title="Deny Application"):
            def __init__(self, parent_view):
                super().__init__()
                self.parent_view = parent_view

            reason = discord.ui.TextInput(
                label="Reason for denial",
                style=discord.TextStyle.paragraph,
                required=True
            )

            async def on_submit(self, interaction: discord.Interaction):
                # Respond to the interaction immediately to prevent timeout
                await interaction.response.send_message("Processing application denial...", ephemeral=True)

                deny_time = datetime.datetime.now()

                # Create denial embed
                denial_embed = discord.Embed(
                    title="Application Denied",
                    description="Your application to Aegis Nox has been denied.",
                    color=discord.Color.default()
                )
                denial_embed.add_field(name="Reason", value=self.reason.value, inline=False)
                
                # Create transcript like in close button
                transcript_text = f"Transcript for {interaction.channel.name}\n"
                transcript_text += f"Created at: {interaction.channel.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                transcript_text += "-" * 50 + "\n\n"

                messages = [message async for message in interaction.channel.history(limit=None, oldest_first=True)]
                
                for msg in messages:
                    timestamp = msg.created_at.strftime("%Y-%m-%d %H:%M:%S")
                    transcript_text += f"[{timestamp}] {msg.author.name}: {msg.content}\n"
                    for embed in msg.embeds:
                        if embed.title:
                            transcript_text += f"Embed Title: {embed.title}\n"
                        if embed.description:
                            transcript_text += f"Embed Description: {embed.description}\n"
                        for field in embed.fields:
                            transcript_text += f"{field.name}: {field.value}\n"
                    transcript_text += "\n"

                transcript_file = discord.File(
                    io.StringIO(transcript_text),
                    filename=f"transcript-{interaction.channel.name}.txt"
                )

                try:
                    # Send denial message and transcript to applicant
                    await self.parent_view.applicant.send(embed=denial_embed, file=transcript_file)

                    # Close the channel
                    await interaction.followup.send("Application denied and message sent to applicant.", ephemeral=True)
                    await interaction.channel.send("Application denied. This channel will be deleted in 5 seconds.")
                    await asyncio.sleep(5)
                    await interaction.channel.delete()
                except discord.Forbidden:
                    await interaction.followup.send("Failed to send denial message to the applicant, but the channel will be deleted.", ephemeral=True)
                    await interaction.channel.delete()

        await interaction.response.send_modal(DenyModal(self))

class ApplyToAegis(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.config_file = 'data/apply_config.json'
        self.load_config()

    def load_config(self):
        """Load the configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.config = {'message_id': None}
            self.save_config()

    def save_config(self):
        """Save the configuration to JSON file"""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=4)

    @commands.Cog.listener()
    async def on_ready(self):
        # Add the persistent view
        self.bot.add_view(ApplicationView())
        
        # Get the application channel
        channel = self.bot.get_channel(1389659100802973718)
        if channel:
            # Delete all messages in the channel
            await channel.purge()
            
            # Create and send the application embed
            embed = discord.Embed(
                title="Aegis Nox Application",
                description="Welcome to Aegis Nox! Interested in joining our Organization? Hit the Apply button below to start the process!",
                color=discord.Color.default()
            )
            message = await channel.send(embed=embed, view=ApplicationView())
            
            # Save the message ID
            self.config['message_id'] = message.id
            self.save_config()

async def setup(bot):
    await bot.add_cog(ApplyToAegis(bot)) 