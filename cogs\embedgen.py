import discord
from discord.ext import commands
from discord import app_commands
import json
import os
from datetime import datetime
import logging
import traceback
import asyncio

DATA_DIR = 'data'
EMBEDS_FILE = os.path.join(DATA_DIR, 'embeds.json')

if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

def load_embeds():
    try:
        if os.path.exists(EMBEDS_FILE):
            with open(EMBEDS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"embeds": {}, "last_id": 0}
    except Exception as e:
        logging.error(f"Error loading embeds: {traceback.format_exc()}")
        return {"embeds": {}, "last_id": 0}

def save_embed_data(embed_data, message_id=None):
    try:
        data = load_embeds()
        new_id = data["last_id"] + 1
        
        # Create the embed entry with both IDs
        embed_entry = {
            "id": new_id,
            "message_id": message_id,
            "data": embed_data,
            "created_at": datetime.utcnow().isoformat(),
            "last_modified": datetime.utcnow().isoformat()
        }
        
        # Use the sequential ID as the key
        data["embeds"][str(new_id)] = embed_entry
        data["last_id"] = new_id
        
        # Save to file
        with open(EMBEDS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return new_id
    except Exception as e:
        logging.error(f"Error saving embed: {traceback.format_exc()}")
        return None

def get_embed_data(embed_id):
    try:
        data = load_embeds()
        # Try to find by sequential ID first
        embed = data["embeds"].get(str(embed_id))
        if embed:
            return embed["data"]
        
        # If not found, try to find by message ID
        for embed_entry in data["embeds"].values():
            if str(embed_entry.get("message_id")) == str(embed_id):
                return embed_entry["data"]
        return None
    except Exception as e:
        logging.error(f"Error getting embed: {traceback.format_exc()}")
        return None

class EmbedSetupView(discord.ui.View):
    def __init__(self, cog, initial_data=None, message_id=None, channel_id=None):
        super().__init__(timeout=300)
        self.cog = cog
        self.message_id = message_id
        self.channel_id = channel_id
        self.embed_data = initial_data or {
            'title': 'New Embed',
            'description': 'Click the buttons below to customize this embed.',
            'color': 0x000000,
            'fields': [],
            'footer': '',
            'timestamp': False,
            'image_url': '',
            'thumbnail_url': ''
        }
        self.preview_message = None

    async def on_timeout(self):
        try:
            for item in self.children:
                item.disabled = True
            if self.preview_message:
                await self.preview_message.edit(content="Embed setup timed out!", view=self)
        except Exception as e:
            logging.error(f"Error in on_timeout: {traceback.format_exc()}")

    async def on_error(self, interaction: discord.Interaction, error: Exception, item: discord.ui.Item):
        logging.error(f"Error in view {self.__class__.__name__}: {traceback.format_exc()}")
        try:
            await interaction.response.send_message("An error occurred. Please try again.", ephemeral=True)
        except:
            try:
                await interaction.followup.send("An error occurred. Please try again.", ephemeral=True)
            except:
                pass

    def build_embed(self):
        try:
            embed = discord.Embed(
                title=self.embed_data['title'],
                description=self.embed_data['description'],
                color=self.embed_data['color']
            )
            for field in self.embed_data['fields']:
                embed.add_field(name=field['name'], value=field['value'], inline=field['inline'])
            if self.embed_data['footer']:
                embed.set_footer(text=self.embed_data['footer'])
            if self.embed_data['timestamp']:
                embed.timestamp = datetime.utcnow()
            if self.embed_data['image_url']:
                embed.set_image(url=self.embed_data['image_url'])
            if self.embed_data['thumbnail_url']:
                embed.set_thumbnail(url=self.embed_data['thumbnail_url'])
            return embed
        except Exception as e:
            logging.error(f"Error building embed: {traceback.format_exc()}")
            return discord.Embed(title="Error", description="Failed to build embed. Please check your inputs.", color=0xFF0000)

    @discord.ui.button(label="Edit Title", style=discord.ButtonStyle.blurple)
    async def edit_title(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_modal(EmbedEditModal(self, 'title', 'Title', self.embed_data['title']))
        except Exception as e:
            logging.error(f"Error in edit_title: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to open title editor. Please try again.", ephemeral=True)

    @discord.ui.button(label="Edit Description", style=discord.ButtonStyle.blurple)
    async def edit_description(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_modal(EmbedEditModal(self, 'description', 'Description', self.embed_data['description']))
        except Exception as e:
            logging.error(f"Error in edit_description: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to open description editor. Please try again.", ephemeral=True)

    @discord.ui.button(label="Edit Color", style=discord.ButtonStyle.blurple)
    async def edit_color(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_modal(EmbedEditModal(self, 'color', 'Color (hex, e.g. #000000)', f"#{self.embed_data['color']:06x}"))
        except Exception as e:
            logging.error(f"Error in edit_color: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to open color editor. Please try again.", ephemeral=True)

    @discord.ui.button(label="Edit Footer", style=discord.ButtonStyle.blurple)
    async def edit_footer(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_modal(EmbedEditModal(self, 'footer', 'Footer', self.embed_data['footer']))
        except Exception as e:
            logging.error(f"Error in edit_footer: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to open footer editor. Please try again.", ephemeral=True)

    @discord.ui.button(label="Edit Image URL", style=discord.ButtonStyle.blurple)
    async def edit_image(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_modal(EmbedEditModal(self, 'image_url', 'Image URL', self.embed_data['image_url']))
        except Exception as e:
            logging.error(f"Error in edit_image: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to open image URL editor. Please try again.", ephemeral=True)

    @discord.ui.button(label="Edit Thumbnail URL", style=discord.ButtonStyle.blurple)
    async def edit_thumbnail(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_modal(EmbedEditModal(self, 'thumbnail_url', 'Thumbnail URL', self.embed_data['thumbnail_url']))
        except Exception as e:
            logging.error(f"Error in edit_thumbnail: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to open thumbnail URL editor. Please try again.", ephemeral=True)

    @discord.ui.button(label="Toggle Timestamp", style=discord.ButtonStyle.gray)
    async def toggle_timestamp(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            self.embed_data['timestamp'] = not self.embed_data['timestamp']
            await self.update_preview(interaction)
            await interaction.followup.send(f"Timestamp {'enabled' if self.embed_data['timestamp'] else 'disabled'}!", ephemeral=True)
        except Exception as e:
            logging.error(f"Error in toggle_timestamp: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to toggle timestamp. Please try again.", ephemeral=True)

    @discord.ui.button(label="Add Field", style=discord.ButtonStyle.green)
    async def add_field(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_modal(EmbedFieldModal(self))
        except Exception as e:
            logging.error(f"Error in add_field: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to open field editor. Please try again.", ephemeral=True)

    @discord.ui.button(label="Remove Field", style=discord.ButtonStyle.red)
    async def remove_field(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            if not self.embed_data['fields']:
                await interaction.response.send_message("No fields to remove!", ephemeral=True)
                return
            options = [discord.SelectOption(label=f"{i+1}: {f['name']}", value=str(i)) for i, f in enumerate(self.embed_data['fields'])]
            select = discord.ui.Select(placeholder="Select field to remove", options=options)
            async def remove_callback(select_interaction):
                try:
                    idx = int(select.values[0])
                    removed = self.embed_data['fields'].pop(idx)
                    await self.update_preview(select_interaction)
                    await select_interaction.response.send_message(f"Removed field: {removed['name']}", ephemeral=True)
                except Exception as e:
                    logging.error(f"Error in remove_field callback: {traceback.format_exc()}")
                    await select_interaction.response.send_message("Failed to remove field. Please try again.", ephemeral=True)
            select.callback = remove_callback
            remove_view = discord.ui.View()
            remove_view.add_item(select)
            await interaction.response.send_message("Select a field to remove:", view=remove_view, ephemeral=True)
        except Exception as e:
            logging.error(f"Error in remove_field: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to show field removal options. Please try again.", ephemeral=True)

    @discord.ui.button(label="Edit Field", style=discord.ButtonStyle.blurple)
    async def edit_field(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            if not self.embed_data['fields']:
                await interaction.response.send_message("No fields to edit!", ephemeral=True)
                return

            options = [
                discord.SelectOption(
                    label=f"{i+1}: {f['name'][:50]}", # Truncate long names
                    description=f"{f['value'][:50]}...", # Show preview of value
                    value=str(i)
                ) for i, f in enumerate(self.embed_data['fields'])
            ]
            
            select = discord.ui.Select(
                placeholder="Select field to edit",
                options=options,
                min_values=1,
                max_values=1
            )

            async def edit_callback(select_interaction):
                try:
                    idx = int(select.values[0])
                    field_data = self.embed_data['fields'][idx]
                    await select_interaction.response.send_modal(EmbedFieldEditModal(self, idx, field_data))
                except Exception as e:
                    logging.error(f"Error in edit field callback: {traceback.format_exc()}")
                    await select_interaction.response.send_message("Failed to open field editor. Please try again.", ephemeral=True)

            select.callback = edit_callback
            edit_view = discord.ui.View()
            edit_view.add_item(select)
            await interaction.response.send_message("Select a field to edit:", view=edit_view, ephemeral=True)
        except Exception as e:
            logging.error(f"Error in edit_field: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to show field edit options. Please try again.", ephemeral=True)

    @discord.ui.button(label="Send", style=discord.ButtonStyle.blurple)
    async def send_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            await interaction.response.send_message("Please provide the channel ID or mention the channel to send the embed:", ephemeral=True)

            def check(m):
                return m.author.id == interaction.user.id and m.channel.id == interaction.channel.id
            try:
                msg = await self.cog.bot.wait_for('message', check=check, timeout=60.0)
            except asyncio.TimeoutError:
                await interaction.followup.send("Timed out waiting for channel. Please try again.", ephemeral=True)
                return

            channel_id = None
            if msg.content.startswith('<#') and msg.content.endswith('>'):
                channel_id = int(msg.content[2:-1])
            else:
                try:
                    channel_id = int(msg.content)
                except ValueError:
                    await interaction.followup.send("Invalid channel format. Please use a channel mention or ID.", ephemeral=True)
                    return

            channel = self.cog.bot.get_channel(channel_id)
            if not channel:
                await interaction.followup.send("Could not find the channel. Make sure the bot has access to it.", ephemeral=True)
                return

            try:
                embed = self.build_embed()
                sent_message = await channel.send(embed=embed)
                
                # Save the embed with the message ID
                embed_id = save_embed_data(self.embed_data, sent_message.id)
                
                if embed_id:
                    await interaction.followup.send(
                        f"Embed sent to <#{channel_id}>!\n"
                        f"Embed ID: `{embed_id}`\n"
                        f"Message ID: `{sent_message.id}`\n"
                        f"You can edit this embed later using `/embededit {embed_id}` or `/embededit {sent_message.id}`",
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        f"Embed sent to <#{channel_id}>, but there was an error saving it for future editing.",
                        ephemeral=True
                    )
            except discord.Forbidden:
                await interaction.followup.send("I don't have permission to send messages in that channel.", ephemeral=True)
            except Exception as e:
                logging.error(f"Error sending embed: {traceback.format_exc()}")
                await interaction.followup.send("Failed to send the embed. Please check the channel permissions.", ephemeral=True)
        except Exception as e:
            logging.error(f"Error in send_button: {traceback.format_exc()}")
            await interaction.followup.send("An error occurred while processing your request. Please try again.", ephemeral=True)

    @discord.ui.button(label="Save", style=discord.ButtonStyle.green)
    async def save_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            if not self.message_id:
                await interaction.response.send_message("This is a new embed. Please use the Send button instead.", ephemeral=True)
                return

            # Find the message in any channel the bot can see
            message = None
            for guild in self.cog.bot.guilds:
                for channel in guild.text_channels:
                    try:
                        message = await channel.fetch_message(self.message_id)
                        if message:
                            break
                    except (discord.NotFound, discord.Forbidden):
                        continue
                if message:
                    break

            if not message:
                await interaction.response.send_message("Could not find the original message. It might have been deleted or I don't have access to it.", ephemeral=True)
                return

            try:
                embed = self.build_embed()
                await message.edit(embed=embed)
                
                # Update the stored embed data
                data = load_embeds()
                for embed_entry in data["embeds"].values():
                    if str(embed_entry.get("message_id")) == str(self.message_id):
                        embed_entry["data"] = self.embed_data
                        embed_entry["last_modified"] = datetime.utcnow().isoformat()
                        break
                
                with open(EMBEDS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                await interaction.response.send_message("Embed updated successfully!", ephemeral=True)
            except discord.Forbidden:
                await interaction.response.send_message("I don't have permission to edit messages in that channel.", ephemeral=True)
            except Exception as e:
                logging.error(f"Error updating embed: {traceback.format_exc()}")
                await interaction.response.send_message("Failed to update the embed. Please check the channel permissions.", ephemeral=True)
        except Exception as e:
            logging.error(f"Error in save_button: {traceback.format_exc()}")
            await interaction.response.send_message("An error occurred while saving the embed. Please try again.", ephemeral=True)

    async def update_preview(self, interaction, response_sent=False):
        try:
            embed = self.build_embed()
            if self.preview_message:
                await self.preview_message.edit(embed=embed, view=self)
                if not response_sent:
                    await interaction.response.defer()
            else:
                if response_sent:
                    self.preview_message = await interaction.followup.send(embed=embed, view=self, ephemeral=True)
                else:
                    self.preview_message = await interaction.response.send_message(embed=embed, view=self, ephemeral=True)
        except Exception as e:
            logging.error(f"Error updating preview: {traceback.format_exc()}")
            if not response_sent:
                try:
                    await interaction.response.send_message("Failed to update preview. Please try again.", ephemeral=True)
                except:
                    try:
                        await interaction.followup.send("Failed to update preview. Please try again.", ephemeral=True)
                    except:
                        pass

class EmbedEditModal(discord.ui.Modal):
    def __init__(self, parent_view, key, label, default=None):
        super().__init__(title=f"Edit {label}")
        self.key = key
        self.parent_view = parent_view
        self.input = discord.ui.TextInput(label=label, style=discord.TextStyle.paragraph, required=True, default=default or "")
        self.add_item(self.input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            value = self.input.value
            if self.key == "color":
                try:
                    value = int(value.replace('#', ''), 16)
                except ValueError:
                    await interaction.response.send_message("Invalid color. Please provide a hex code like #000000.", ephemeral=True)
                    return
            elif self.key in ["image_url", "thumbnail_url"] and value.lower() == 'none':
                value = ''
            
            self.parent_view.embed_data[self.key] = value
            await interaction.response.defer(ephemeral=True)
            await self.parent_view.update_preview(interaction, response_sent=True)
            await interaction.followup.send(f"Updated {self.key.replace('_', ' ')}!", ephemeral=True)
        except Exception as e:
            logging.error(f"Error in modal submission: {traceback.format_exc()}")
            try:
                await interaction.response.send_message("Failed to update the embed. Please try again.", ephemeral=True)
            except:
                try:
                    await interaction.followup.send("Failed to update the embed. Please try again.", ephemeral=True)
                except:
                    pass

class EmbedFieldModal(discord.ui.Modal):
    def __init__(self, parent_view):
        super().__init__(title="Add Embed Field")
        self.parent_view = parent_view
        self.name = discord.ui.TextInput(label="Field Name", style=discord.TextStyle.short, required=True)
        self.value = discord.ui.TextInput(label="Field Value", style=discord.TextStyle.paragraph, required=True)
        self.inline = discord.ui.TextInput(label="Inline? (yes/no)", style=discord.TextStyle.short, required=True, default="no")
        self.add_item(self.name)
        self.add_item(self.value)
        self.add_item(self.inline)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            inline = self.inline.value.strip().lower() == 'yes'
            self.parent_view.embed_data['fields'].append({
                'name': self.name.value,
                'value': self.value.value,
                'inline': inline
            })
            await interaction.response.defer(ephemeral=True)
            await self.parent_view.update_preview(interaction, response_sent=True)
            await interaction.followup.send("Field added!", ephemeral=True)
        except Exception as e:
            logging.error(f"Error adding field: {traceback.format_exc()}")
            try:
                await interaction.response.send_message("Failed to add field. Please try again.", ephemeral=True)
            except:
                try:
                    await interaction.followup.send("Failed to add field. Please try again.", ephemeral=True)
                except:
                    pass

class EmbedFieldEditModal(discord.ui.Modal):
    def __init__(self, parent_view, field_index, field_data):
        super().__init__(title="Edit Embed Field")
        self.parent_view = parent_view
        self.field_index = field_index
        self.name = discord.ui.TextInput(
            label="Field Name",
            style=discord.TextStyle.short,
            required=True,
            default=field_data['name']
        )
        self.value = discord.ui.TextInput(
            label="Field Value",
            style=discord.TextStyle.paragraph,
            required=True,
            default=field_data['value']
        )
        self.inline = discord.ui.TextInput(
            label="Inline? (yes/no)",
            style=discord.TextStyle.short,
            required=True,
            default="yes" if field_data['inline'] else "no"
        )
        self.add_item(self.name)
        self.add_item(self.value)
        self.add_item(self.inline)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            inline = self.inline.value.strip().lower() == 'yes'
            self.parent_view.embed_data['fields'][self.field_index] = {
                'name': self.name.value,
                'value': self.value.value,
                'inline': inline
            }
            await interaction.response.defer(ephemeral=True)
            await self.parent_view.update_preview(interaction, response_sent=True)
            await interaction.followup.send("Field updated!", ephemeral=True)
        except Exception as e:
            logging.error(f"Error updating field: {traceback.format_exc()}")
            try:
                await interaction.response.send_message("Failed to update field. Please try again.", ephemeral=True)
            except:
                try:
                    await interaction.followup.send("Failed to update field. Please try again.", ephemeral=True)
                except:
                    pass

class EmbedGen(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @app_commands.command(name="embedcreate", description="Interactively create an embed with UI components.")
    async def embedcreate(self, interaction: discord.Interaction):
        try:
            view = EmbedSetupView(self)
            embed = view.build_embed()
            view.preview_message = await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
        except Exception as e:
            logging.error(f"Error in embedcreate command: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to start embed creation. Please try again.", ephemeral=True)

    @app_commands.command(name="embededit", description="Edit an existing embed by ID or message ID.")
    @app_commands.describe(embed_id="The embed ID or message ID of the embed to edit.")
    async def embededit(self, interaction: discord.Interaction, embed_id: str):
        try:
            # Load embeds data
            data = load_embeds()
            
            # Try to find the embed entry
            embed_entry = None
            for entry in data["embeds"].values():
                if str(entry.get("id")) == str(embed_id) or str(entry.get("message_id")) == str(embed_id):
                    embed_entry = entry
                    break
            
            if not embed_entry:
                await interaction.response.send_message("No embed found with that ID.", ephemeral=True)
                return
            
            # Create view with message info
            view = EmbedSetupView(
                self,
                initial_data=embed_entry["data"],
                message_id=embed_entry["message_id"]
            )
            
            embed = view.build_embed()
            view.preview_message = await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
        except Exception as e:
            logging.error(f"Error in embededit command: {traceback.format_exc()}")
            await interaction.response.send_message("Failed to load embed for editing. Please try again.", ephemeral=True)

async def setup(bot):
    await bot.add_cog(EmbedGen(bot)) 