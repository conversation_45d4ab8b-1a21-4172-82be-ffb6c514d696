import discord
from discord.ext import commands
from discord import app_commands
import json
from typing import Dict, List
import os

class FleetData(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.fleet_data: Dict[str, int] = {}  # Store ship name and count
        self.fleet_message_id = None
        self.fleet_channel_id = 1389716636449112184
        self.data_file = 'data/fleet_data.json'
        self.load_data()

    async def cog_load(self):
        """This runs when the cog is loaded"""
        # Add the persistent view
        self.bot.add_view(FleetDataView(self))

    def load_data(self):
        """Load fleet data from JSON file"""
        if os.path.exists(self.data_file):
            with open(self.data_file, 'r') as f:
                data = json.load(f)
                self.fleet_data = data.get('ships', {})
                self.fleet_message_id = data.get('message_id')

    def save_data(self):
        """Save fleet data to JSON file"""
        os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
        with open(self.data_file, 'w') as f:
            json.dump({
                'ships': self.fleet_data,
                'message_id': self.fleet_message_id
            }, f, indent=4)

    def create_fleet_embed(self) -> discord.Embed:
        """Create the fleet data embed"""
        embed = discord.Embed(title="Fleet Data", color=discord.Color.blue())
        
        # Sort ships by name
        sorted_ships = sorted(self.fleet_data.items())
        
        # Create the ship list field
        ship_list = "\n".join([f"{ship} x{count}" for ship, count in sorted_ships]) or "No ships in fleet"
        embed.add_field(name="Ships", value=ship_list, inline=False)
        
        # Add total count
        total_ships = sum(self.fleet_data.values())
        embed.add_field(name="Total Ships", value=str(total_ships), inline=False)
        
        return embed

    @commands.command()
    @commands.has_permissions(administrator=True)
    async def setup_fleetdata(self, ctx):
        """Initial setup command for fleet data tracking"""
        channel = self.bot.get_channel(self.fleet_channel_id)
        if not channel:
            await ctx.send("Could not find the specified channel.")
            return

        # Create the initial embed
        embed = self.create_fleet_embed()
        
        # Create the buttons view
        view = FleetDataView(self)
        
        # Send the embed with buttons
        message = await channel.send(embed=embed, view=view)
        self.fleet_message_id = message.id
        self.save_data()
        
        await ctx.send("Fleet data tracking has been set up!")

    async def update_fleet_message(self):
        """Update the fleet data message"""
        if not self.fleet_message_id:
            return
            
        channel = self.bot.get_channel(self.fleet_channel_id)
        if not channel:
            return
            
        try:
            message = await channel.fetch_message(self.fleet_message_id)
            embed = self.create_fleet_embed()
            view = FleetDataView(self)
            await message.edit(embed=embed, view=view)
        except discord.NotFound:
            self.fleet_message_id = None
            self.save_data()

class FleetDataView(discord.ui.View):
    def __init__(self, cog: FleetData):
        super().__init__(timeout=None)  # Timeout of None makes the view persistent
        self.cog = cog

    @discord.ui.button(label="Add Ship", style=discord.ButtonStyle.green, custom_id="fleet_add_ship")
    async def add_ship(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = AddShipModal(self.cog)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="Remove Ship", style=discord.ButtonStyle.red, custom_id="fleet_remove_ship")
    async def remove_ship(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = RemoveShipModal(self.cog)
        await interaction.response.send_modal(modal)

class AddShipModal(discord.ui.Modal, title="Add Ship"):
    def __init__(self, cog: FleetData):
        super().__init__()
        self.cog = cog

    ship_name = discord.ui.TextInput(
        label="Ship Name",
        placeholder="Enter the ship name...",
        required=True
    )

    quantity = discord.ui.TextInput(
        label="Quantity",
        placeholder="Enter the quantity to add...",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            quantity = int(self.quantity.value)
            if quantity <= 0:
                raise ValueError("Quantity must be positive")
                
            ship_name = self.ship_name.value.strip()
            current_count = self.cog.fleet_data.get(ship_name, 0)
            self.cog.fleet_data[ship_name] = current_count + quantity
            self.cog.save_data()
            
            await self.cog.update_fleet_message()
            await interaction.response.send_message(f"Added {quantity} {ship_name}(s) to the fleet.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Please enter a valid positive number for quantity.", ephemeral=True)

class RemoveShipModal(discord.ui.Modal, title="Remove Ship"):
    def __init__(self, cog: FleetData):
        super().__init__()
        self.cog = cog

    ship_name = discord.ui.TextInput(
        label="Ship Name",
        placeholder="Enter the ship name...",
        required=True
    )

    quantity = discord.ui.TextInput(
        label="Quantity",
        placeholder="Enter the quantity to remove...",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            quantity = int(self.quantity.value)
            if quantity <= 0:
                raise ValueError("Quantity must be positive")
                
            ship_name = self.ship_name.value.strip()
            current_count = self.cog.fleet_data.get(ship_name, 0)
            
            if current_count < quantity:
                await interaction.response.send_message(f"Error: Cannot remove {quantity} {ship_name}(s). Only {current_count} available.", ephemeral=True)
                return
                
            new_count = current_count - quantity
            if new_count == 0:
                del self.cog.fleet_data[ship_name]
            else:
                self.cog.fleet_data[ship_name] = new_count
                
            self.cog.save_data()
            await self.cog.update_fleet_message()
            await interaction.response.send_message(f"Removed {quantity} {ship_name}(s) from the fleet.", ephemeral=True)
        except ValueError:
            await interaction.response.send_message("Please enter a valid positive number for quantity.", ephemeral=True)

async def setup(bot):
    await bot.add_cog(FleetData(bot)) 