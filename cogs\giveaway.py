import discord
from discord.ext import commands
import asyncio
from datetime import datetime, timedelta
import random
import json
import os

class DurationSelect(discord.ui.Select):
    def __init__(self, parent_view):
        self._view = parent_view
        durations = [
            ("10 minutes", timedelta(minutes=10)),
            ("30 minutes", timedelta(minutes=30)),
            ("1 hour", timedelta(hours=1)),
            ("6 hours", timedelta(hours=6)),
            ("12 hours", timedelta(hours=12)),
            ("24 hours", timedelta(hours=24)),
            ("3 days", timedelta(days=3)),
            ("7 days", timedelta(days=7)),
            ("2 weeks", timedelta(days=14)),
            ("1 month", timedelta(days=30))
        ]
        
        options = [
            discord.SelectOption(
                label=name,
                value=str(i),
                description=f"Set giveaway duration to {name}"
            ) for i, (name, _) in enumerate(durations)
        ]
        
        super().__init__(
            placeholder="Select giveaway duration...",
            min_values=1,
            max_values=1,
            options=options
        )
        self.durations = durations

    async def callback(self, interaction: discord.Interaction):
        try:
            self._view.selected_duration = self.durations[int(self.values[0])]
            
            # Create a preview embed to show the selection
            duration_name, _ = self._view.selected_duration
            embed = discord.Embed(
                title="Duration Selected",
                description=f"Giveaway will run for: **{duration_name}**\n\nUse the buttons below to preview or start the giveaway!",
                color=discord.Color.blue()
            )
            
            # Use the interaction response instead of message.edit
            await interaction.response.edit_message(content=None, embed=embed, view=self._view)
            
        except Exception as e:
            print(f"Error in duration select callback: {str(e)}")
            try:
                await interaction.response.send_message("An error occurred while selecting the duration. Please try again.", ephemeral=True)
            except:
                pass

class GiveawaySetupView(discord.ui.View):
    def __init__(self, cog, reward: str, winners: int, channel: discord.TextChannel, required_role: discord.Role = None, min_messages: int = 0, ping: str = None, description: str = None):
        super().__init__(timeout=180)  # 3 minute timeout
        self.cog = cog
        self.reward = reward
        self.winners = winners
        self.channel = channel
        self.ping = ping
        self.description = description if description else f"Win {reward}!"
        self.selected_duration = None
        self.required_role = required_role
        self.min_messages = min_messages
        
        # Add the duration select menu
        self.duration_select = DurationSelect(self)
        self.add_item(self.duration_select)

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True
        try:
            await self.message.edit(content="Giveaway setup timed out!", view=self)
        except:
            pass

    def create_preview_embed(self):
        """Create the giveaway embed for preview"""
        if not self.selected_duration:
            return None
            
        duration_name, duration_delta = self.selected_duration
        
        # Calculate end time in UTC
        end_time = datetime.now() + duration_delta
        unix_timestamp = int(end_time.timestamp())
        
        embed = discord.Embed(
            title="🎉 GIVEAWAY 🎉",
            description=self.description,
            color=discord.Color.green()
        )
        embed.add_field(name="Reward", value=self.reward, inline=False)
        embed.add_field(name="Winners", value=str(self.winners), inline=True)
        
        # Format duration for display
        if duration_delta.days > 0:
            duration_str = f"{duration_delta.days} days"
        elif duration_delta.seconds >= 3600:
            hours = duration_delta.seconds // 3600
            duration_str = f"{hours} hours"
        else:
            minutes = duration_delta.seconds // 60
            duration_str = f"{minutes} minutes"
            
        embed.add_field(name="Duration", value=duration_str, inline=True)
        
        # Use Discord timestamp formatting
        embed.add_field(
            name="Ends", 
            value=f"<t:{unix_timestamp}:R>", 
            inline=True
        )
        
        # Add requirements section if any requirements exist
        requirements = []
        if self.required_role:
            requirements.append(f"Required Role: {self.required_role.mention}")
        if self.min_messages:
            requirements.append(f"Minimum Messages: {self.min_messages}")
            
        if requirements:
            embed.add_field(
                name="Requirements",
                value="\n".join(requirements),
                inline=False
            )
        
        # Set footer with exact end time
        embed.set_footer(text=f"React with 🎉 to enter!")
        
        return embed

    @discord.ui.button(label="Preview", style=discord.ButtonStyle.blurple)
    async def preview_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not self.selected_duration:
            await interaction.response.send_message("Please select a duration first!", ephemeral=True)
            return
            
        embed = self.create_preview_embed()
        preview_text = f"**PREVIEW**\nChannel: {self.channel.mention}\n"
        if self.ping:
            preview_text += f"Ping: {self.ping}\n"
            
        await interaction.response.send_message(f"{preview_text}\nThis is how the giveaway will look:", embed=embed, ephemeral=True)

    @discord.ui.button(label="Start Giveaway", style=discord.ButtonStyle.green)
    async def start_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not self.selected_duration:
            await interaction.response.send_message("Please select a duration first!", ephemeral=True)
            return
            
        duration_name, duration_delta = self.selected_duration
        
        # Calculate end time using current time
        end_time = datetime.now() + duration_delta
        
        # Create embed
        embed = self.create_preview_embed()

        try:
            # First, acknowledge the interaction
            await interaction.response.defer(ephemeral=True)

            # Send giveaway message
            content = self.ping if self.ping else None
            giveaway_msg = await self.channel.send(content=content, embed=embed)
            await giveaway_msg.add_reaction("🎉")

            # Store giveaway info with requirements
            self.cog.active_giveaways[giveaway_msg.id] = {
                'channel_id': self.channel.id,
                'reward': self.reward,
                'winners': self.winners,
                'end_time': end_time,
                'duration': duration_delta.total_seconds(),
                'required_role_id': self.required_role.id if self.required_role else None,
                'min_messages': self.min_messages
            }
            
            # Save to JSON
            self.cog.save_giveaways()

            # Start giveaway timer
            self.cog.bot.loop.create_task(self.cog.end_giveaway(giveaway_msg.id, duration_delta))

            # Try to disable the view, but don't error if it fails
            try:
                for item in self.children:
                    item.disabled = True
                await interaction.message.edit(view=self)
            except discord.NotFound:
                pass
            
            # Send confirmation message
            await interaction.followup.send(f"Giveaway created in {self.channel.mention}!", ephemeral=True)

        except Exception as e:
            print(f"Error creating giveaway: {str(e)}")
            await interaction.followup.send("An error occurred while creating the giveaway.", ephemeral=True)

class Giveaway(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.allowed_roles = [
            1389657163252760636
        ]
        self.active_giveaways = {}
        self.giveaways_file = 'data/giveaways.json'
        self.load_giveaways()

    def load_giveaways(self):
        """Load giveaways from JSON file"""
        try:
            # Create data directory if it doesn't exist
            os.makedirs('data', exist_ok=True)
            
            if os.path.exists(self.giveaways_file):
                with open(self.giveaways_file, 'r') as f:
                    saved_giveaways = json.load(f)
                    
                # Convert stored timestamps back to datetime objects
                for msg_id, giveaway in saved_giveaways.items():
                    giveaway['end_time'] = datetime.fromtimestamp(giveaway['end_time'])
                    # Start timer for giveaway if it hasn't ended yet
                    if giveaway['end_time'] > datetime.utcnow():
                        duration = giveaway['end_time'] - datetime.utcnow()
                        self.bot.loop.create_task(self.end_giveaway(int(msg_id), duration))
                    
                self.active_giveaways = {int(k): v for k, v in saved_giveaways.items()}
        except Exception as e:
            print(f"Error loading giveaways: {str(e)}")
            self.active_giveaways = {}

    def save_giveaways(self):
        """Save giveaways to JSON file"""
        try:
            # Convert datetime objects to timestamps for JSON serialization
            giveaways_to_save = {}
            for msg_id, giveaway in self.active_giveaways.items():
                giveaway_copy = giveaway.copy()
                giveaway_copy['end_time'] = giveaway_copy['end_time'].timestamp()
                giveaways_to_save[str(msg_id)] = giveaway_copy
                
            with open(self.giveaways_file, 'w') as f:
                json.dump(giveaways_to_save, f, indent=4)
        except Exception as e:
            print(f"Error saving giveaways: {str(e)}")

    def has_permission(self, ctx):
        """Check if user has any of the allowed roles"""
        return any(role.id in self.allowed_roles for role in ctx.author.roles)

    @commands.hybrid_command(name="giveaway")
    async def create_giveaway(self, ctx, reward: str, winners: int, channel: discord.TextChannel, required_role: discord.Role = None, min_messages: int = 0, ping: str = None, *, description: str = None):
        """Create a giveaway with optional requirements
        Example: /giveaway "100k E Coins" 1 #giveaways @Required_Role 100 @everyone "Must have role and 100 messages to enter!"
        """
        if not self.has_permission(ctx):
            await ctx.send("You don't have permission to create giveaways!", ephemeral=True)
            return

        view = GiveawaySetupView(self, reward, winners, channel, required_role, min_messages, ping, description)
        await ctx.send("Please select the giveaway duration:", view=view, ephemeral=True)

    async def end_giveaway(self, message_id: int, duration: timedelta):
        """End the giveaway after duration"""
        await asyncio.sleep(duration.total_seconds())

        giveaway_info = self.active_giveaways.get(message_id)
        if not giveaway_info:
            return

        channel = self.bot.get_channel(giveaway_info['channel_id'])
        if not channel:
            return

        try:
            message = await channel.fetch_message(message_id)
            reaction = discord.utils.get(message.reactions, emoji="🎉")
            
            if not reaction:
                await channel.send("No one entered the giveaway 😢")
                return

            # Get all users who reacted
            users = [user async for user in reaction.users()]
            users = [user for user in users if not user.bot]  # Remove bots

            if not users:
                await channel.send("No valid entries found 😢")
                return

            # Select winners
            winners = random.sample(users, min(giveaway_info['winners'], len(users)))
            winners_text = ", ".join(winner.mention for winner in winners)

            # Create winner announcement embed
            winner_embed = discord.Embed(
                title="🎉 GIVEAWAY ENDED 🎉",
                description=f"**Reward:** {giveaway_info['reward']}\n\n**Winners:** {winners_text}",
                color=discord.Color.gold()
            )
            winner_embed.set_footer(text=f"Winners: {len(winners)} | Total Entries: {len(users)}")

            await channel.send(embed=winner_embed)
            
            # Remove giveaway from active giveaways
            del self.active_giveaways[message_id]
            # Save changes to JSON
            self.save_giveaways()

        except Exception as e:
            print(f"Error ending giveaway: {str(e)}")
            await channel.send("An error occurred while ending the giveaway.")

    @commands.Cog.listener()
    async def on_reaction_add(self, reaction, user):
        """Handle reaction adds for giveaways"""
        if user.bot:
            return

        if reaction.emoji == "🎉" and reaction.message.id in self.active_giveaways:
            giveaway = self.active_giveaways[reaction.message.id]
            
            # Check role requirement
            if giveaway.get('required_role_id'):
                member = reaction.message.guild.get_member(user.id)
                if not member or not any(role.id == giveaway['required_role_id'] for role in member.roles):
                    await reaction.message.channel.send(
                        f"{user.mention} You don't have the required role to enter this giveaway!",
                        delete_after=10
                    )
                    await reaction.remove(user)
                    return
            
            # Check message count requirement from stats.json
            if giveaway.get('min_messages', 0) > 0:
                try:
                    with open('data/stats.json', 'r') as f:
                        stats = json.load(f)
                    
                    # Get user's message count from stats
                    user_messages = stats.get('messages', {}).get(str(user.id), 0)
                    
                    if user_messages < giveaway['min_messages']:
                        await reaction.message.channel.send(
                            f"{user.mention} You need at least {giveaway['min_messages']} messages in the server to enter the giveaway! (You have {user_messages})",
                            delete_after=10
                        )
                        await reaction.remove(user)
                        return
                except Exception as e:
                    print(f"Error checking message count: {str(e)}")
                    # If we can't check the message count, let them enter
                    pass

    @commands.Cog.listener()
    async def on_reaction_remove(self, reaction, user):
        """Handle reaction removes for giveaways"""
        if user.bot:
            return

        if reaction.emoji == "🎉" and reaction.message.id in self.active_giveaways:
            # User has left the giveaway
            pass

    @commands.hybrid_command(name="giveawayend")
    async def end_giveaway_manual(self, ctx, message_id: str):
        """Manually end a giveaway by providing its message ID
        Example: /giveawayend 123456789
        """
        if not self.has_permission(ctx):
            await ctx.send("You don't have permission to end giveaways!", ephemeral=True)
            return

        try:
            # Convert message_id to integer
            msg_id = int(message_id)
        except ValueError:
            await ctx.send("Please provide a valid message ID!", ephemeral=True)
            return

        giveaway_info = self.active_giveaways.get(msg_id)
        if not giveaway_info:
            await ctx.send("No active giveaway found with that message ID!", ephemeral=True)
            return

        channel = self.bot.get_channel(giveaway_info['channel_id'])
        if not channel:
            await ctx.send("Could not find the giveaway channel!", ephemeral=True)
            return

        try:
            message = await channel.fetch_message(msg_id)
            reaction = discord.utils.get(message.reactions, emoji="🎉")
            
            if not reaction:
                await channel.send("No one entered the giveaway 😢")
                return

            # Get all users who reacted
            users = [user async for user in reaction.users()]
            users = [user for user in users if not user.bot]  # Remove bots

            if not users:
                await channel.send("No valid entries found 😢")
                return

            # Select winners
            winners = random.sample(users, min(giveaway_info['winners'], len(users)))
            winners_text = ", ".join(winner.mention for winner in winners)

            # Create winner announcement embed
            winner_embed = discord.Embed(
                title="🎉 GIVEAWAY ENDED 🎉",
                description=f"**Reward:** {giveaway_info['reward']}\n\n**Winners:** {winners_text}",
                color=discord.Color.gold()
            )
            winner_embed.set_footer(text=f"Winners: {len(winners)} | Total Entries: {len(users)}")

            await channel.send(embed=winner_embed)
            
            # Remove giveaway from active giveaways
            del self.active_giveaways[msg_id]
            # Save changes to JSON
            self.save_giveaways()

            await ctx.send("Giveaway ended successfully!", ephemeral=True)

        except Exception as e:
            print(f"Error ending giveaway: {str(e)}")
            await ctx.send("An error occurred while ending the giveaway.", ephemeral=True)

    @commands.hybrid_command(name="giveawaylist")
    async def list_giveaways(self, ctx):
        """List all active giveaways"""
        if not self.has_permission(ctx):
            await ctx.send("You don't have permission to list giveaways!", ephemeral=True)
            return

        if not self.active_giveaways:
            await ctx.send("No active giveaways found!", ephemeral=True)
            return

        embed = discord.Embed(
            title="🎉 Active Giveaways",
            color=discord.Color.blue()
        )

        for msg_id, giveaway in self.active_giveaways.items():
            channel = self.bot.get_channel(giveaway['channel_id'])
            if channel:
                time_left = giveaway['end_time'] - datetime.utcnow()
                if time_left.total_seconds() > 0:
                    embed.add_field(
                        name=f"Giveaway in {channel.name}",
                        value=f"Reward: {giveaway['reward']}\nWinners: {giveaway['winners']}\nEnds: <t:{int(giveaway['end_time'].timestamp())}:R>\nMessage ID: {msg_id}",
                        inline=False
                    )

        await ctx.send(embed=embed, ephemeral=True)

async def setup(bot):
    await bot.add_cog(Giveaway(bot)) 