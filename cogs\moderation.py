import discord
from discord.ext import commands
from discord import app_commands
import json
import os
from datetime import datetime, timedelta
import asyncio
from .utils.logging import log_moderation_action, log_role_change

# Create data directory if it doesn't exist
if not os.path.exists('data'):
    os.makedirs('data')

# Staff role IDs
STAFF_ROLES = [1389657163252760636, 1389657332782334112, 1389657392685252659]

def load_warnings():
    try:
        with open('data/warnings.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def save_warnings(warnings):
    with open('data/warnings.json', 'w') as f:
        json.dump(warnings, f, indent=4)

class ConfirmView(discord.ui.View):
    def __init__(self, *, timeout=180):
        super().__init__(timeout=timeout)
        self.value = None

    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.green)
    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = True
        self.stop()
        await interaction.response.defer()

    @discord.ui.button(label='Cancel', style=discord.ButtonStyle.red)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = False
        self.stop()
        await interaction.response.defer()

class Moderation(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def has_staff_role():
        async def predicate(interaction: discord.Interaction):
            return any(role.id in STAFF_ROLES for role in interaction.user.roles)
        return app_commands.check(predicate)

    @app_commands.command(name="ban", description="Ban a member from the server")
    @app_commands.describe(
        member="The member to ban",
        reason="The reason for the ban",
        delete_messages="Number of days of messages to delete (0-7)"
    )
    @has_staff_role()
    async def ban(self, interaction: discord.Interaction, member: discord.Member, reason: str = None, delete_messages: int = 0):
        if delete_messages < 0 or delete_messages > 7:
            await interaction.response.send_message("Delete message days must be between 0 and 7.", ephemeral=True)
            return

        # Check if the bot can ban the member
        if not member.guild.me.guild_permissions.ban_members:
            await interaction.response.send_message("I don't have permission to ban members.", ephemeral=True)
            return

        # Check if the member can be banned
        if member.top_role >= interaction.user.top_role:
            await interaction.response.send_message("You can't ban someone with a higher or equal role.", ephemeral=True)
            return

        view = ConfirmView()
        await interaction.response.send_message(
            f"Are you sure you want to ban {member.mention}?",
            view=view,
            ephemeral=True
        )
        await view.wait()

        if view.value:
            try:
                await member.ban(reason=reason, delete_message_days=delete_messages)
                await log_moderation_action(
                    self.bot,
                    "Ban",
                    interaction.user,
                    member,
                    reason,
                    delete_message_days=delete_messages
                )
                await interaction.edit_original_response(
                    content=f"✅ Banned {member.mention} | Reason: {reason or 'No reason provided'}",
                    view=None
                )
            except discord.Forbidden:
                await interaction.edit_original_response(
                    content="I don't have permission to ban that member.",
                    view=None
                )
        else:
            await interaction.edit_original_response(
                content="Ban cancelled.",
                view=None
            )

    @app_commands.command(name="kick", description="Kick a member from the server")
    @app_commands.describe(
        member="The member to kick",
        reason="The reason for the kick"
    )
    @has_staff_role()
    async def kick(self, interaction: discord.Interaction, member: discord.Member, reason: str = None):
        if not member.guild.me.guild_permissions.kick_members:
            await interaction.response.send_message("I don't have permission to kick members.", ephemeral=True)
            return

        if member.top_role >= interaction.user.top_role:
            await interaction.response.send_message("You can't kick someone with a higher or equal role.", ephemeral=True)
            return

        view = ConfirmView()
        await interaction.response.send_message(
            f"Are you sure you want to kick {member.mention}?",
            view=view,
            ephemeral=True
        )
        await view.wait()

        if view.value:
            try:
                await member.kick(reason=reason)
                await log_moderation_action(
                    self.bot,
                    "Kick",
                    interaction.user,
                    member,
                    reason
                )
                await interaction.edit_original_response(
                    content=f"✅ Kicked {member.mention} | Reason: {reason or 'No reason provided'}",
                    view=None
                )
            except discord.Forbidden:
                await interaction.edit_original_response(
                    content="I don't have permission to kick that member.",
                    view=None
                )
        else:
            await interaction.edit_original_response(
                content="Kick cancelled.",
                view=None
            )

    @app_commands.command(name="timeout", description="Timeout (mute) a member")
    @app_commands.describe(
        member="The member to timeout",
        duration="Duration in minutes",
        reason="The reason for the timeout"
    )
    @has_staff_role()
    async def timeout(self, interaction: discord.Interaction, member: discord.Member, duration: int, reason: str = None):
        if not member.guild.me.guild_permissions.moderate_members:
            await interaction.response.send_message("I don't have permission to timeout members.", ephemeral=True)
            return

        if member.top_role >= interaction.user.top_role:
            await interaction.response.send_message("You can't timeout someone with a higher or equal role.", ephemeral=True)
            return

        try:
            until = discord.utils.utcnow() + timedelta(minutes=duration)
            await member.timeout(until, reason=reason)
            await log_moderation_action(
                self.bot,
                "Timeout",
                interaction.user,
                member,
                reason,
                duration=f"{duration} minutes",
                expires=until.strftime("%Y-%m-%d %H:%M UTC")
            )
            await interaction.response.send_message(
                f"✅ Timed out {member.mention} for {duration} minutes | Reason: {reason or 'No reason provided'}",
                ephemeral=True
            )
        except discord.Forbidden:
            await interaction.response.send_message("I don't have permission to timeout that member.", ephemeral=True)

    @app_commands.command(name="untimeout", description="Remove timeout from a member")
    @app_commands.describe(
        member="The member to remove timeout from",
        reason="The reason for removing the timeout"
    )
    @has_staff_role()
    async def untimeout(self, interaction: discord.Interaction, member: discord.Member, reason: str = None):
        if not member.guild.me.guild_permissions.moderate_members:
            await interaction.response.send_message("I don't have permission to remove timeouts.", ephemeral=True)
            return

        try:
            await member.timeout(None, reason=reason)
            await log_moderation_action(
                self.bot,
                "Timeout Removed",
                interaction.user,
                member,
                reason
            )
            await interaction.response.send_message(
                f"✅ Removed timeout from {member.mention} | Reason: {reason or 'No reason provided'}",
                ephemeral=True
            )
        except discord.Forbidden:
            await interaction.response.send_message("I don't have permission to remove timeout from that member.", ephemeral=True)

    @app_commands.command(name="warn", description="Warn a member")
    @app_commands.describe(
        member="The member to warn",
        reason="The reason for the warning"
    )
    @has_staff_role()
    async def warn(self, interaction: discord.Interaction, member: discord.Member, reason: str):
        warnings = load_warnings()
        user_id = str(member.id)

        if user_id not in warnings:
            warnings[user_id] = []

        warning = {
            "reason": reason,
            "moderator": interaction.user.id,
            "timestamp": datetime.utcnow().isoformat()
        }

        warnings[user_id].append(warning)
        save_warnings(warnings)

        # Log the warning
        await log_moderation_action(
            self.bot,
            "Warning",
            interaction.user,
            member,
            reason,
            warning_count=len(warnings[user_id])
        )

        # DM the user
        try:
            embed = discord.Embed(
                title="Warning Received",
                description=f"You have been warned in {interaction.guild.name}",
                color=discord.Color.yellow()
            )
            embed.add_field(name="Reason", value=reason)
            embed.add_field(name="Moderator", value=interaction.user.mention)
            await member.send(embed=embed)
        except discord.Forbidden:
            pass  # User has DMs disabled

        await interaction.response.send_message(
            f"✅ Warned {member.mention} | Reason: {reason}\nThis is their {len(warnings[user_id])} warning(s).",
            ephemeral=True
        )

    @app_commands.command(name="warnings", description="View warnings for a member")
    @app_commands.describe(member="The member to view warnings for")
    @has_staff_role()
    async def warnings(self, interaction: discord.Interaction, member: discord.Member):
        warnings = load_warnings()
        user_id = str(member.id)

        if user_id not in warnings or not warnings[user_id]:
            await interaction.response.send_message(f"{member.mention} has no warnings.", ephemeral=True)
            return

        embed = discord.Embed(
            title=f"Warnings for {member}",
            color=discord.Color.yellow()
        )

        for i, warning in enumerate(warnings[user_id], 1):
            moderator = interaction.guild.get_member(warning["moderator"])
            moderator_name = moderator.name if moderator else "Unknown Moderator"
            timestamp = datetime.fromisoformat(warning["timestamp"]).strftime("%Y-%m-%d %H:%M UTC")
            
            embed.add_field(
                name=f"Warning #{i}",
                value=f"**Reason:** {warning['reason']}\n**Moderator:** {moderator_name}\n**Date:** {timestamp}",
                inline=False
            )

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="clearwarnings", description="Clear all warnings for a member")
    @app_commands.describe(member="The member to clear warnings for")
    @has_staff_role()
    async def clearwarnings(self, interaction: discord.Interaction, member: discord.Member):
        warnings = load_warnings()
        user_id = str(member.id)

        if user_id not in warnings or not warnings[user_id]:
            await interaction.response.send_message(f"{member.mention} has no warnings to clear.", ephemeral=True)
            return

        view = ConfirmView()
        await interaction.response.send_message(
            f"Are you sure you want to clear all warnings for {member.mention}?",
            view=view,
            ephemeral=True
        )
        await view.wait()

        if view.value:
            warning_count = len(warnings[user_id])
            warnings[user_id] = []
            save_warnings(warnings)
            
            await log_moderation_action(
                self.bot,
                "Warnings Cleared",
                interaction.user,
                member,
                None,
                warnings_cleared=warning_count
            )
            
            await interaction.edit_original_response(
                content=f"✅ Cleared all warnings for {member.mention}",
                view=None
            )
        else:
            await interaction.edit_original_response(
                content="Warning clear cancelled.",
                view=None
            )

    @app_commands.command(name="clear", description="Clear messages in a channel")
    @app_commands.describe(
        amount="Number of messages to clear (1-100)",
        user="Only clear messages from this user"
    )
    @has_staff_role()
    async def clear(self, interaction: discord.Interaction, amount: int, user: discord.Member = None):
        if amount < 1 or amount > 100:
            await interaction.response.send_message("Please specify a number between 1 and 100.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        if user:
            def check(message):
                return message.author == user

            deleted = await interaction.channel.purge(limit=amount, check=check)
            await log_moderation_action(
                self.bot,
                "Messages Cleared",
                interaction.user,
                user,
                None,
                messages_deleted=len(deleted),
                channel=interaction.channel.mention
            )
            await interaction.followup.send(f"Deleted {len(deleted)} messages from {user.mention}.", ephemeral=True)
        else:
            deleted = await interaction.channel.purge(limit=amount)
            await log_moderation_action(
                self.bot,
                "Messages Cleared",
                interaction.user,
                None,
                None,
                messages_deleted=len(deleted),
                channel=interaction.channel.mention
            )
            await interaction.followup.send(f"Deleted {len(deleted)} messages.", ephemeral=True)

    # Add event listener for role changes
    @commands.Cog.listener()
    async def on_member_update(self, before: discord.Member, after: discord.Member):
        # Check for role changes
        removed_roles = set(before.roles) - set(after.roles)
        added_roles = set(after.roles) - set(before.roles)

        # Get audit logs to find who made the change
        if removed_roles or added_roles:
            async for entry in after.guild.audit_logs(limit=1, action=discord.AuditLogAction.member_role_update):
                moderator = entry.user
                break
            else:
                moderator = None

            # Log removed roles
            for role in removed_roles:
                await log_role_change(
                    self.bot,
                    "Role Removed",
                    after,
                    role,
                    moderator
                )

            # Log added roles
            for role in added_roles:
                await log_role_change(
                    self.bot,
                    "Role Added",
                    after,
                    role,
                    moderator
                )

async def setup(bot):
    await bot.add_cog(Moderation(bot)) 