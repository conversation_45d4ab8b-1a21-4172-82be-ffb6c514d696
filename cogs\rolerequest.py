import discord
from discord.ext import commands
from discord import app_commands
import asyncio
import datetime
import json
import os
from typing import Optional
from discord.ui import Button, View, Modal, TextInput, Select
import io

# Create data directory if it doesn't exist
if not os.path.exists('data'):
    os.makedirs('data')

# Load org roles
def load_org_roles():
    try:
        with open('data/org_roles.json', 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {"org_roles": []}

def save_org_roles(data):
    with open('data/org_roles.json', 'w') as f:
        json.dump(data, f, indent=4)

class OrgRoleSelect(discord.ui.Select):
    def __init__(self, org_roles):
        options = [
            discord.SelectOption(label=role["name"], value=str(role["id"]))
            for role in org_roles
        ]
        super().__init__(
            placeholder="Select an organization...",
            min_values=1,
            max_values=1,
            options=options
        )

    async def callback(self, interaction: discord.Interaction):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this.", ephemeral=True)
            return

        role = interaction.guild.get_role(int(self.values[0]))
        if role:
            try:
                await self.view.requester.add_roles(role)
                await interaction.response.send_message(f"Successfully assigned {role.name} role!", ephemeral=True)
            except discord.Forbidden:
                await interaction.response.send_message("Failed to assign role. Please check permissions.", ephemeral=True)

class OrgSelectView(discord.ui.View):
    def __init__(self, requester):
        super().__init__()
        self.requester = requester
        org_roles = load_org_roles()
        if org_roles["org_roles"]:
            self.add_item(OrgRoleSelect(org_roles["org_roles"]))

class StaffActionView(discord.ui.View):
    def __init__(self, requester, request_type="Support Request", org_name=None):
        super().__init__(timeout=None)
        self.claimed_by = None
        self.claimed_at = None
        self.requester = requester
        self.request_type = request_type
        self.org_name = org_name

    @discord.ui.button(label="Claim", style=discord.ButtonStyle.primary, custom_id="claim_request")
    async def claim(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return
        
        self.claimed_by = interaction.user
        self.claimed_at = datetime.datetime.now()
        embed = interaction.message.embeds[0]
        embed.add_field(name="Claimed By", value=f"{interaction.user.mention} at {self.claimed_at.strftime('%Y-%m-%d %H:%M:%S')}", inline=False)
        await interaction.message.edit(embed=embed)
        await interaction.response.send_message("You have claimed this request.", ephemeral=True)

    @discord.ui.button(label="Close", style=discord.ButtonStyle.danger, custom_id="close_request")
    async def close(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return

        class CloseModal(discord.ui.Modal, title="Close Request"):
            def __init__(self, parent_view):
                super().__init__()
                self.parent_view = parent_view

            reason = discord.ui.TextInput(
                label="Reason for closing",
                style=discord.TextStyle.paragraph,
                required=True
            )

            async def on_submit(self, interaction: discord.Interaction):
                close_time = datetime.datetime.now()
                
                # Create transcript embed
                transcript_embed = discord.Embed(
                    title="Request Transcript",
                    color=discord.Color.default()
                )
                transcript_embed.add_field(name="Type", value=self.parent_view.request_type, inline=False)
                transcript_embed.add_field(name="Requester", value=f"{self.parent_view.requester.mention}", inline=False)
                if self.parent_view.claimed_by:
                    transcript_embed.add_field(name="Claimed By", value=f"{self.parent_view.claimed_by.mention}", inline=False)
                    transcript_embed.add_field(name="Claimed At", value=self.parent_view.claimed_at.strftime("%Y-%m-%d %H:%M:%S"), inline=False)
                transcript_embed.add_field(name="Closed By", value=f"{interaction.user.mention}", inline=False)
                transcript_embed.add_field(name="Closed At", value=close_time.strftime("%Y-%m-%d %H:%M:%S"), inline=False)
                transcript_embed.add_field(name="Close Reason", value=self.reason.value, inline=False)

                # Create text transcript
                transcript_text = f"Transcript for {interaction.channel.name}\n"
                transcript_text += f"Created at: {interaction.channel.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                transcript_text += "-" * 50 + "\n\n"

                messages = [message async for message in interaction.channel.history(limit=None, oldest_first=True)]
                
                for msg in messages:
                    timestamp = msg.created_at.strftime("%Y-%m-%d %H:%M:%S")
                    transcript_text += f"[{timestamp}] {msg.author.name}: {msg.content}\n"
                    for embed in msg.embeds:
                        if embed.title:
                            transcript_text += f"Embed Title: {embed.title}\n"
                        if embed.description:
                            transcript_text += f"Embed Description: {embed.description}\n"
                        for field in embed.fields:
                            transcript_text += f"{field.name}: {field.value}\n"
                    transcript_text += "\n"

                transcript_file = discord.File(
                    io.StringIO(transcript_text),
                    filename=f"transcript-{interaction.channel.name}.txt"
                )

                logs_channel = interaction.guild.get_channel(1389705853095120942)
                await logs_channel.send(embed=transcript_embed, file=transcript_file)

                try:
                    await self.parent_view.requester.send(embed=transcript_embed, file=discord.File(
                        io.StringIO(transcript_text),
                        filename=f"transcript-{interaction.channel.name}.txt"
                    ))
                    await interaction.response.send_message("Request closed and transcript sent.", ephemeral=True)
                    await interaction.channel.send("This request has been closed. This channel will be deleted in 5 seconds.")
                    await asyncio.sleep(5)
                    await interaction.channel.delete()
                except discord.Forbidden:
                    await interaction.response.send_message("Failed to DM the user, but the channel will be deleted.", ephemeral=True)
                    await interaction.channel.delete()

        await interaction.response.send_modal(CloseModal(self))

    @discord.ui.button(label="Create Org Role", style=discord.ButtonStyle.success, custom_id="create_org_role")
    async def create_org_role(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return

        if not self.org_name:
            await interaction.response.send_message("No organization name provided for this request.", ephemeral=True)
            return

        try:
            # Create the new role
            new_role = await interaction.guild.create_role(name=self.org_name, mentionable=True)
            
            # Add role to org_roles.json
            org_roles = load_org_roles()
            org_roles["org_roles"].append({
                "id": new_role.id,
                "name": self.org_name
            })
            save_org_roles(org_roles)
            
            # Assign role to requester
            await self.requester.add_roles(new_role)
            
            await interaction.response.send_message(f"Created and assigned the {self.org_name} role!", ephemeral=True)
            button.disabled = True
            await interaction.message.edit(view=self)
        except discord.Forbidden:
            await interaction.response.send_message("Failed to create/assign role. Please check permissions.", ephemeral=True)

    @discord.ui.button(label="Assign Ally", style=discord.ButtonStyle.success, custom_id="assign_ally")
    async def assign_ally(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return
        
        ally_role = interaction.guild.get_role(1389710995974520995)
        if ally_role:
            try:
                await self.requester.add_roles(ally_role)
                await interaction.response.send_message(f"Successfully added the Ally role to {self.requester.mention}!", ephemeral=True)
                button.disabled = True
                await interaction.message.edit(view=self)
            except discord.Forbidden:
                await interaction.response.send_message("Failed to add the Ally role. Please check bot permissions.", ephemeral=True)
        else:
            await interaction.response.send_message("Ally role not found. Please check the role ID.", ephemeral=True)

    @discord.ui.button(label="Assign Org", style=discord.ButtonStyle.success, custom_id="assign_org")
    async def assign_org(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to use this button.", ephemeral=True)
            return
        
        await interaction.response.send_message("Select an organization to assign:", view=OrgSelectView(self.requester), ephemeral=True)

    def __init_subclass__(self):
        # This ensures that buttons are only shown for appropriate request types
        if self.request_type == "Support Request":
            self.create_org_role.disabled = True
            self.assign_ally.disabled = True
            self.assign_org.disabled = True
        elif self.request_type == "Org Request":
            self.assign_ally.disabled = True

class RoleRequestView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Request", style=discord.ButtonStyle.primary, custom_id="role_request_button")
    async def request_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Get the required roles
        founder_role = interaction.guild.get_role(1389657163252760636)
        general_role = interaction.guild.get_role(1389657332782334112)
        commander_role = interaction.guild.get_role(1389657392685252659)
        
        # Get the category
        category = interaction.guild.get_channel(1389660299367284746)
        
        # Create channel overwrites
        overwrites = {
            interaction.guild.default_role: discord.PermissionOverwrite(read_messages=False),
            interaction.user: discord.PermissionOverwrite(
                read_messages=True,
                send_messages=True,
                read_message_history=True,
                attach_files=True,
                embed_links=True
            ),
            founder_role: discord.PermissionOverwrite(read_messages=True, send_messages=True),
            general_role: discord.PermissionOverwrite(read_messages=True, send_messages=True),
            commander_role: discord.PermissionOverwrite(read_messages=True, send_messages=True)
        }

        # Create the channel in the specified category
        channel = await interaction.guild.create_text_channel(
            f'request-{interaction.user.name}',
            category=category,
            overwrites=overwrites
        )

        # Create the request type selection view
        class RequestTypeView(discord.ui.View):
            def __init__(self):
                super().__init__(timeout=None)

            @discord.ui.button(label="Affiliate Request", style=discord.ButtonStyle.primary, custom_id="affiliate_request")
            async def affiliate_request(self, interaction: discord.Interaction, button: discord.ui.Button):
                # Create the modal for affiliate request
                class AffiliateRequestModal(discord.ui.Modal, title="Affiliate Request Form"):
                    age = discord.ui.TextInput(
                        label="Age",
                        placeholder="Enter your age",
                        required=True
                    )
                    username = discord.ui.TextInput(
                        label="In Game Username",
                        placeholder="Enter your in-game username",
                        required=True
                    )
                    inviter = discord.ui.TextInput(
                        label="Who invited you?",
                        placeholder="Enter who invited you",
                        required=True
                    )
                    reason = discord.ui.TextInput(
                        label="Why do you want to be an Affiliate?",
                        style=discord.TextStyle.paragraph,
                        required=True
                    )
                    additional_info = discord.ui.TextInput(
                        label="Additional Info",
                        style=discord.TextStyle.paragraph,
                        required=False
                    )

                    async def on_submit(self, interaction: discord.Interaction):
                        # Create and send the affiliate request embed
                        embed = discord.Embed(
                            title="Affiliate Request",
                            color=discord.Color.default()
                        )
                        embed.add_field(name="Age", value=self.age.value, inline=False)
                        embed.add_field(name="In Game Username", value=self.username.value, inline=False)
                        embed.add_field(name="Invited By", value=self.inviter.value, inline=False)
                        embed.add_field(name="Reason", value=self.reason.value, inline=False)
                        if self.additional_info.value:
                            embed.add_field(name="Additional Info", value=self.additional_info.value, inline=False)

                        # Delete the original message and send the new embed
                        await interaction.message.delete()
                        await interaction.response.send_message(embed=embed, view=StaffActionView(interaction.user, "Affiliate Request"))

                await interaction.response.send_modal(AffiliateRequestModal())

            @discord.ui.button(label="Org Ally Request", style=discord.ButtonStyle.primary, custom_id="org_ally_request")
            async def org_ally_request(self, interaction: discord.Interaction, button: discord.ui.Button):
                class OrgAllyRequestModal(discord.ui.Modal, title="Org Ally Request Form"):
                    org_name = discord.ui.TextInput(
                        label="What is your Organization's Name?",
                        placeholder="Enter your organization's name",
                        required=True
                    )
                    username = discord.ui.TextInput(
                        label="What is your In Game Username",
                        placeholder="Enter your in-game username",
                        required=True
                    )
                    org_link = discord.ui.TextInput(
                        label="Org Link",
                        placeholder="Enter your organization's link",
                        required=True
                    )
                    inviter = discord.ui.TextInput(
                        label="Who invited you?",
                        placeholder="Enter who invited you",
                        required=True
                    )
                    reason = discord.ui.TextInput(
                        label="Why do you want to Ally with Aegis Nox?",
                        style=discord.TextStyle.paragraph,
                        required=True
                    )

                    async def on_submit(self, interaction: discord.Interaction):
                        # Create and send the org ally request embed
                        embed = discord.Embed(
                            title="Org Ally Request",
                            color=discord.Color.default()
                        )
                        embed.add_field(name="Organization Name", value=self.org_name.value, inline=False)
                        embed.add_field(name="In Game Username", value=self.username.value, inline=False)
                        embed.add_field(name="Org Link", value=self.org_link.value, inline=False)
                        embed.add_field(name="Invited By", value=self.inviter.value, inline=False)
                        embed.add_field(name="Reason", value=self.reason.value, inline=False)

                        # Delete the original message and send the new embed
                        await interaction.message.delete()
                        await interaction.response.send_message(embed=embed, view=StaffActionView(interaction.user, "Org Ally Request", self.org_name.value))

                await interaction.response.send_modal(OrgAllyRequestModal())

            @discord.ui.button(label="Org Request", style=discord.ButtonStyle.primary, custom_id="org_request")
            async def org_request(self, interaction: discord.Interaction, button: discord.ui.Button):
                class OrgRequestModal(discord.ui.Modal, title="Org Request Form"):
                    org_name = discord.ui.TextInput(
                        label="What Organization are you in?",
                        placeholder="Enter your organization's name",
                        required=True
                    )
                    username = discord.ui.TextInput(
                        label="In game username?",
                        placeholder="Enter your in-game username",
                        required=True
                    )
                    age = discord.ui.TextInput(
                        label="Age",
                        placeholder="Enter your age",
                        required=True
                    )
                    inviter = discord.ui.TextInput(
                        label="Who invited you",
                        placeholder="Enter who invited you",
                        required=True
                    )
                    additional_info = discord.ui.TextInput(
                        label="Additional Info",
                        style=discord.TextStyle.paragraph,
                        required=False
                    )

                    async def on_submit(self, interaction: discord.Interaction):
                        # Create and send the org request embed
                        embed = discord.Embed(
                            title="Org Request",
                            color=discord.Color.default()
                        )
                        embed.add_field(name="Organization", value=self.org_name.value, inline=False)
                        embed.add_field(name="In Game Username", value=self.username.value, inline=False)
                        embed.add_field(name="Age", value=self.age.value, inline=False)
                        embed.add_field(name="Invited By", value=self.inviter.value, inline=False)
                        if self.additional_info.value:
                            embed.add_field(name="Additional Info", value=self.additional_info.value, inline=False)

                        # Delete the original message and send the new embed
                        await interaction.message.delete()
                        await interaction.response.send_message(embed=embed, view=StaffActionView(interaction.user, "Org Request"))

                await interaction.response.send_modal(OrgRequestModal())

            @discord.ui.button(label="Request Support", style=discord.ButtonStyle.primary, custom_id="request_support")
            async def request_support(self, interaction: discord.Interaction, button: discord.ui.Button):
                class SupportRequestModal(discord.ui.Modal, title="Support Request Form"):
                    org_name = discord.ui.TextInput(
                        label="What Org are you a Part of if any?",
                        placeholder="Enter your organization's name (if any)",
                        required=False
                    )
                    username = discord.ui.TextInput(
                        label="In Game Username?",
                        placeholder="Enter your in-game username",
                        required=True
                    )
                    reason = discord.ui.TextInput(
                        label="Why are you Requesting Support?",
                        style=discord.TextStyle.paragraph,
                        required=True
                    )
                    additional_info = discord.ui.TextInput(
                        label="Additional Info",
                        style=discord.TextStyle.paragraph,
                        required=False
                    )

                    async def on_submit(self, interaction: discord.Interaction):
                        # Create and send the support request embed
                        embed = discord.Embed(
                            title="Support Request",
                            color=discord.Color.default()
                        )
                        if self.org_name.value:
                            embed.add_field(name="Organization", value=self.org_name.value, inline=False)
                        embed.add_field(name="In Game Username", value=self.username.value, inline=False)
                        embed.add_field(name="Support Reason", value=self.reason.value, inline=False)
                        if self.additional_info.value:
                            embed.add_field(name="Additional Info", value=self.additional_info.value, inline=False)

                        # Delete the original message and send the new embed
                        await interaction.message.delete()
                        await interaction.response.send_message(embed=embed, view=StaffActionView(interaction.user, "Support Request"))

                await interaction.response.send_modal(SupportRequestModal())

        # Send the initial message in the new channel with pings
        embed = discord.Embed(
            title="Request Type Selection",
            description=f"Hello {interaction.user.mention} Please select your request type below.\n\n"
                       "- Affiliate Request\n"
                       "> Used to Request the Affiliate Role here in Aegis Nox\n"
                       "- Org Ally Request\n"
                       "> Used for other Org Leaders to Request a Friendly Alliance with Aegis Nox\n"
                       "- Org Request\n"
                       "> Used to Request Org Roles for Org Members joining from other Orgs\n"
                       "- Request Support\n"
                       "> Used to request support from the Aegis Nox Command, Also used to Report other Members, Orgs, or for any Questions/Concerns.",
            color=discord.Color.default()
        )
        
        # Send initial ping message
        ping_message = f"{interaction.user.mention} {founder_role.mention} {general_role.mention} {commander_role.mention}"
        await channel.send(ping_message)
        await channel.send(embed=embed, view=RequestTypeView())
        await interaction.response.send_message(f"Request channel created! Please check {channel.mention}", ephemeral=True)

class RoleRequest(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.config_file = 'data/role_request_config.json'
        self.load_config()

    def load_config(self):
        """Load the configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.config = {'message_id': None, 'channel_id': None}
            self.save_config()

    def save_config(self):
        """Save the configuration to JSON file"""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=4)

    @commands.Cog.listener()
    async def on_ready(self):
        # Add the persistent view
        self.bot.add_view(RoleRequestView())
        
        # Verify the saved message still exists
        if self.config['channel_id'] and self.config['message_id']:
            try:
                channel = self.bot.get_channel(self.config['channel_id'])
                if channel:
                    try:
                        message = await channel.fetch_message(self.config['message_id'])
                        if not message:
                            self.config['message_id'] = None
                            self.config['channel_id'] = None
                            self.save_config()
                    except discord.NotFound:
                        self.config['message_id'] = None
                        self.config['channel_id'] = None
                        self.save_config()
            except Exception as e:
                print(f"Error verifying role request message: {e}")

    @commands.command()
    @commands.has_permissions(administrator=True)
    async def setup_role_request(self, ctx):
        # Delete all messages in the channel
        await ctx.channel.purge()

        # Create and send the role request embed
        embed = discord.Embed(
            title="Aegis Nox Role Request & Support",
            description="Are you part of another Organization inside Star Citizen? Wanting to Affiliate with Aegis Nox? Wanting to Ally your Org with Aegis Nox? Need to request a Organization Role? Need Support?\n\nClick the button below to start the process!",
            color=discord.Color.default()
        )
        message = await ctx.send(embed=embed, view=RoleRequestView())
        
        # Save the message and channel IDs
        self.config['message_id'] = message.id
        self.config['channel_id'] = ctx.channel.id
        self.save_config()
        
        await ctx.send("Role request system has been set up and configuration has been saved!", delete_after=5)

async def setup(bot):
    await bot.add_cog(RoleRequest(bot)) 