import discord
from discord.ext import commands
from discord import app_commands
import json
import os
import asyncio
from .utils.logging import log_promotion, log_demotion, log_role_change

# Create data directory if it doesn't exist
if not os.path.exists('data'):
    os.makedirs('data')

# Role hierarchy constants - Ordered from lowest to highest rank
ROLE_HIERARCHY = [
    # Enlisted Roles
    {"name": "Initiate", "id": 1389657645887000762, "type": "enlisted"},
    {"name": "Operator", "id": 1389657578853761094, "type": "enlisted"},
    {"name": "Team Lead", "id": 1389657509630836827, "type": "enlisted"},
    # Officer Roles
    {"name": "Division Coordinator", "id": 1389657450457858230, "type": "officer"},
    {"name": "Commander", "id": 1389657392685252659, "type": "officer"},
    {"name": "Operations Lead", "id": 1389657332782334112, "type": "officer"},
    {"name": "Founder", "id": 1389657163252760636, "type": "officer"}
]

# Division Role IDs
DIVISION_R<PERSON>ES = {
    "nox_aeris": 1391428580063318017,
    "obsidian_fleet": 1391429192180174848,
    "lysara_core": 1391429291430121553,
    "vanthor_command": 1391428944019984384,
    "vanthor_medica": 1391477809897148426,
    "affiliates": 1389657770248110252
}

OFFICER_ROLE_ID = 1389657903140569200
AEGIS_NOX_ROLE_ID = 1389657832063897721

# Helper function to get next rank
def get_next_rank(current_roles):
    current_role_ids = [role.id for role in current_roles]
    
    # Find the current highest rank index
    current_index = -1
    for i, rank in enumerate(ROLE_HIERARCHY):
        if rank["id"] in current_role_ids:
            current_index = i
            
    # If no current rank or at highest rank, return None
    if current_index == -1 or current_index >= len(ROLE_HIERARCHY) - 1:
        return None
    
    # Return next rank up
    next_rank = ROLE_HIERARCHY[current_index + 1]
    return (next_rank["name"], next_rank["id"], next_rank["type"])

# Helper function to get previous rank
def get_previous_rank(current_roles):
    current_role_ids = [role.id for role in current_roles]
    
    # Find the current highest rank index
    current_index = -1
    for i, rank in enumerate(ROLE_HIERARCHY):
        if rank["id"] in current_role_ids:
            current_index = i
            break
    
    # If no current rank or at lowest rank, return None
    if current_index <= 0:
        return None
    
    # Return previous rank
    prev_rank = ROLE_HIERARCHY[current_index - 1]
    return (prev_rank["name"], prev_rank["id"], prev_rank["type"])

# Helper function to sort roster field by rank
def sort_roster_field(field_content):
    if not field_content or field_content.isspace():
        return ""
        
    entries = field_content.strip().split('\n')
    rank_entries = []
    
    # Convert entries to tuples with rank position for sorting
    for entry in entries:
        entry = entry.strip()
        if not entry:  # Skip empty entries
            continue
            
        if ":" in entry:
            # Handle entries with rank
            role_mention, user = entry.split(":", 1)
            role_id = int(role_mention.strip("<@&>"))
            
            # Find role position in hierarchy
            position = -1
            for i, rank in enumerate(ROLE_HIERARCHY):
                if rank["id"] == role_id:
                    position = i
                    break
            
            # Store as tuple (position, role_mention, user) for sorting
            rank_entries.append((position, role_mention.strip(), user.strip()))
        else:
            # Handle entries without rank (like affiliates)
            rank_entries.append((999, "", entry.strip()))  # Use high position number for no-rank entries
    
    # Sort by position (highest rank first, so we negate the position)
    rank_entries.sort(key=lambda x: (-x[0], x[2].lower()))
    
    # Convert back to formatted strings
    result = []
    for _, role_mention, user in rank_entries:
        if role_mention:
            result.append(f"{role_mention}: {user}")
        else:
            result.append(user)
    
    return '\n'.join(result)

# Helper function to add user to roster field
def add_user_to_field(field_content, role_mention, user_mention):
    # Initialize entries list
    entries = []
    
    # Add existing entries if any
    if field_content and not field_content.isspace():
        entries = [entry.strip() for entry in field_content.split('\n') if entry.strip()]
    
    # Create new entry
    if user_mention:
        new_entry = f"{role_mention}: {user_mention}"
    else:
        new_entry = role_mention
    
    # Remove any existing entry for this user to avoid duplicates
    if user_mention:
        entries = [entry for entry in entries if user_mention not in entry]
    else:
        entries = [entry for entry in entries if role_mention not in entry]
    
    # Add new entry
    entries.append(new_entry)
    
    # Sort and return
    return sort_roster_field('\n'.join(entries))

def load_rosters():
    try:
        with open("data/rosters.json", "r") as f:
            data = json.load(f)
            
            # Ensure all rosters have the required fields
            for roster in data["rosters"]:
                if "high_command" not in roster:
                    roster["high_command"] = ""
                if "ground_forces" not in roster:
                    roster["ground_forces"] = ""
                if "navy" not in roster:
                    roster["navy"] = ""
                if "industry" not in roster:
                    roster["industry"] = ""
                if "affiliates" not in roster:
                    roster["affiliates"] = ""
            
            return data
    except FileNotFoundError:
        return {"rosters": [], "next_id": 1}

def save_rosters(data):
    with open('data/rosters.json', 'w') as f:
        json.dump(data, f, indent=4)

class EditModal(discord.ui.Modal, title="Edit Roster"):
    def __init__(self, roster_data):
        super().__init__()
        self.add_item(discord.ui.TextInput(
            label="Title",
            default=roster_data["title"],
            style=discord.TextStyle.short,
            required=True
        ))
        self.add_item(discord.ui.TextInput(
            label="Description",
            default=roster_data["description"],
            style=discord.TextStyle.paragraph,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        
        for roster in rosters["rosters"]:
            if roster["id"] == roster_id:
                # Create a new roster with only the desired fields
                updated_roster = {
                    "id": roster["id"],
                    "title": self.children[0].value,
                    "description": self.children[1].value,
                    "channel_id": roster["channel_id"],
                    "high_command": roster.get("high_command", "Empty"),
                    "vanthor_command": roster.get("vanthor_command", "Empty"),
                    "nox_aeris": roster.get("nox_aeris", "Empty"),
                    "obsidian_fleet": roster.get("obsidian_fleet", "Empty"),
                    "lysara_core": roster.get("lysara_core", "Empty"),
                    "vanthor_medica": roster.get("vanthor_medica", "Empty"),
                    "affiliates": roster.get("affiliates", "Empty")
                }
                
                # Replace the old roster with the updated one
                rosters["rosters"][rosters["rosters"].index(roster)] = updated_roster
                save_rosters(rosters)
                
                embed = create_roster_embed(updated_roster)
                await interaction.response.edit_message(embed=embed, view=RosterView())
                return

class AddFieldModal(discord.ui.Modal, title="Add Field"):
    def __init__(self):
        super().__init__()
        self.add_item(discord.ui.TextInput(
            label="Field Name",
            placeholder="Enter the name for the new field",
            style=discord.TextStyle.short,
            required=True
        ))
        self.add_item(discord.ui.TextInput(
            label="Field Content",
            placeholder="Enter the initial content for this field",
            style=discord.TextStyle.paragraph,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        
        for roster in rosters["rosters"]:
            if roster["id"] == roster_id:
                field_name = self.children[0].value
                field_content = self.children[1].value
                roster[field_name.lower().replace(" ", "_")] = field_content
                save_rosters(rosters)
                
                embed = create_roster_embed(roster)
                await interaction.response.edit_message(embed=embed, view=RosterView())
                return

class RemoveFieldModal(discord.ui.Modal, title="Remove Field"):
    def __init__(self, fields):
        super().__init__()
        self.add_item(discord.ui.TextInput(
            label="Field Name",
            placeholder="Enter the name of the field to remove",
            style=discord.TextStyle.short,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        
        for roster in rosters["rosters"]:
            if roster["id"] == roster_id:
                field_name = self.children[0].value.lower().replace(" ", "_")
                if field_name in roster:
                    del roster[field_name]
                    save_rosters(rosters)
                    
                    embed = create_roster_embed(roster)
                    await interaction.response.edit_message(embed=embed, view=RosterView())
                    return
                else:
                    await interaction.response.send_message(f"Field '{self.children[0].value}' not found.", ephemeral=True)
                    return

class EditFieldModal(discord.ui.Modal, title="Edit Field"):
    def __init__(self, field_name, current_content, roster_id, message_id, channel_id):
        super().__init__()
        self.field_name = field_name
        self.roster_id = roster_id
        self.message_id = message_id
        self.channel_id = channel_id
        
        # Add field title input
        self.add_item(discord.ui.TextInput(
            label="Field Title",
            default=field_name.replace("_", " ").title(),
            required=True,
            max_length=32
        ))
        
        # Add field content input
        self.add_item(discord.ui.TextInput(
            label="Field Content",
            default=current_content,
            style=discord.TextStyle.paragraph,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        rosters = load_rosters()
        
        for roster in rosters["rosters"]:
            if roster["id"] == self.roster_id:
                # Get the new field title and format it
                new_field_name = self.children[0].value.strip().lower().replace(" ", "_")
                
                # Update the field content
                content = self.children[1].value
                
                # If field name changed, update the key name
                if new_field_name != self.field_name:
                    # Get list of keys to maintain order
                    keys = list(roster.keys())
                    # Find the index of the old field name
                    old_field_index = keys.index(self.field_name)
                    # Create new ordered dictionary
                    new_roster = {}
                    for i, key in enumerate(keys):
                        if i == old_field_index:
                            # Add the field with new name at the same position
                            new_roster[new_field_name] = content
                        elif key != self.field_name:
                            # Copy all other fields as is
                            new_roster[key] = roster[key]
                    # Update the roster with the new ordered dictionary
                    roster.clear()
                    roster.update(new_roster)
                else:
                    # Just update the content if name didn't change
                    roster[self.field_name] = content
                
                save_rosters(rosters)
                
                embed = create_roster_embed(roster)
                channel = interaction.guild.get_channel(self.channel_id)
                if channel:
                    message = await channel.fetch_message(self.message_id)
                    if message:
                        await message.edit(embed=embed, view=RosterView())
                        await interaction.response.send_message("Field updated successfully!", ephemeral=True)
                        return
                
                await interaction.response.send_message("Failed to update the roster message.", ephemeral=True)
                return

class SelectFieldToEdit(discord.ui.Select):
    def __init__(self, fields, field_contents, roster_id, message_id, channel_id):
        options = [
            discord.SelectOption(
                label=field.replace("_", " ").title(),
                value=field
            ) for field in fields
        ]
        super().__init__(
            placeholder="Select a field to edit...",
            min_values=1,
            max_values=1,
            options=options
        )
        self.field_contents = field_contents
        self.roster_id = roster_id
        self.message_id = message_id
        self.channel_id = channel_id

    async def callback(self, interaction: discord.Interaction):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to edit fields.", ephemeral=True)
            return

        selected_field = self.values[0]
        await interaction.response.send_modal(
            EditFieldModal(
                selected_field, 
                self.field_contents[selected_field], 
                self.roster_id,
                self.message_id,
                self.channel_id
            )
        )

class EditFieldView(discord.ui.View):
    def __init__(self, fields, field_contents, roster_id, message_id, channel_id):
        super().__init__()
        self.add_item(SelectFieldToEdit(fields, field_contents, roster_id, message_id, channel_id))

class SortFieldsModal(discord.ui.Modal, title="Sort Fields"):
    def __init__(self, roster_data):
        super().__init__()
        self.roster_data = roster_data
        
        # Get all fields except system fields
        self.fields = [field for field in roster_data.keys() 
                      if field not in ["id", "title", "description", "channel_id"]]
        
        # Create a text input showing current order and instructions
        current_order = "\n".join(f"{i+1}. {field}" for i, field in enumerate(self.fields))
        self.add_item(discord.ui.TextInput(
            label="Enter Field Order (one per line)",
            style=discord.TextStyle.paragraph,
            placeholder="Enter field names in desired order...",
            default=current_order,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        # Get the new order from the text input
        new_order = [line.strip().lower().replace(" ", "_") 
                    for line in self.children[0].value.split("\n")
                    if line.strip()]  # Remove empty lines
        
        # Validate all fields are present
        if set(new_order) != set(self.fields):
            await interaction.response.send_message(
                "Error: All fields must be present. Please make sure you didn't remove or duplicate any fields.",
                ephemeral=True
            )
            return
        
        # Create new ordered dictionary
        ordered_data = {
            "id": self.roster_data["id"],
            "title": self.roster_data["title"],
            "description": self.roster_data["description"],
            "channel_id": self.roster_data["channel_id"]
        }
        
        # Add fields in new order
        for field in new_order:
            ordered_data[field] = self.roster_data[field]
        
        # Save to roster
        rosters = load_rosters()
        for i, roster in enumerate(rosters["rosters"]):
            if roster["id"] == self.roster_data["id"]:
                rosters["rosters"][i] = ordered_data
                break
        
        save_rosters(rosters)
        
        # Update embed
        embed = create_roster_embed(ordered_data)
        await interaction.message.edit(embed=embed, view=RosterView())
        await interaction.response.send_message("Fields have been reordered!", ephemeral=True)

class RosterView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Edit Roster", style=discord.ButtonStyle.primary, custom_id="edit_roster")
    async def edit_roster(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to edit rosters.", ephemeral=True)
            return

        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        roster_data = next((r for r in rosters["rosters"] if r["id"] == roster_id), None)
        
        if roster_data:
            await interaction.response.send_modal(EditModal(roster_data))

    @discord.ui.button(label="Edit Field", style=discord.ButtonStyle.primary, custom_id="edit_field")
    async def edit_field(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to edit fields.", ephemeral=True)
            return

        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        roster = next((r for r in rosters["rosters"] if r["id"] == roster_id), None)
        
        if not roster:
            await interaction.response.send_message("Could not find the roster.", ephemeral=True)
            return

        # Get available fields
        fields = [key for key in roster.keys() if key not in ["id", "title", "description", "channel_id"]]
        if not fields:
            await interaction.response.send_message("No fields available to edit.", ephemeral=True)
            return

        # Create a dictionary of field contents
        field_contents = {field: roster[field] for field in fields}
        
        await interaction.response.send_message(
            "Select a field to edit:",
            view=EditFieldView(
                fields, 
                field_contents, 
                roster_id,
                interaction.message.id,
                interaction.channel_id
            ),
            ephemeral=True
        )

    @discord.ui.button(label="Add Field", style=discord.ButtonStyle.success, custom_id="add_field")
    async def add_field(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to add fields.", ephemeral=True)
            return
            
        await interaction.response.send_modal(AddFieldModal())

    @discord.ui.button(label="Remove Field", style=discord.ButtonStyle.danger, custom_id="remove_field")
    async def remove_field(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to remove fields.", ephemeral=True)
            return
            
        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        roster = next((r for r in rosters["rosters"] if r["id"] == roster_id), None)
        if roster:
            await interaction.response.send_modal(RemoveFieldModal(list(roster.keys())))

    @discord.ui.button(label="Add User", style=discord.ButtonStyle.success, custom_id="add_user")
    async def add_user(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to add users to rosters.", ephemeral=True)
            return

        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        roster = next((r for r in rosters["rosters"] if r["id"] == roster_id), None)
        
        if not roster:
            await interaction.response.send_message("Could not find the roster.", ephemeral=True)
            return

        # Get available fields
        fields = [key for key in roster.keys() if key not in ["id", "title", "description", "channel_id"]]
        if not fields:
            await interaction.response.send_message("No fields available. Please add a field first.", ephemeral=True)
            return

        field_list = "\n".join(f"- {field.replace('_', ' ').title()}" for field in fields)
        await interaction.response.send_message(
            "Please mention the user you want to add, followed by their rank (if applicable), and the field name to add them to.\n"
            f"Available fields:\n{field_list}\n\n"
            "Format: `@user @rank field_name`\n"
            "Example: `@JohnDoe @Commander Ground Forces`\n"
            "For affiliates (no rank): `@JohnDoe Affiliates`",
            ephemeral=True
        )

        def check(m):
            return (m.author == interaction.user and 
                   m.channel == interaction.channel and 
                   len(m.mentions) >= 1)

        try:
            response = await interaction.client.wait_for('message', timeout=60.0, check=check)
            
            # Parse the response
            mentions = response.mentions
            if len(mentions) < 1:
                await interaction.followup.send("Please mention at least the user to add.", ephemeral=True)
                return

            user = mentions[0]
            rank = mentions[1] if len(mentions) > 1 else None
            # Get the field name from the remaining content
            content_parts = response.content.split()
            field_name = ' '.join(part for part in content_parts 
                                if not part.startswith('<@') and 
                                not part.endswith('>')).strip().lower().replace(' ', '_')

            if field_name not in fields:
                await interaction.followup.send(
                    f"Invalid field name. Available fields:\n{field_list}",
                    ephemeral=True
                )
                return

            # Add the user to the field
            if field_name == "affiliates":
                # For affiliates, just add the user mention
                new_entry = user.mention
                current_entries = roster[field_name].split('\n') if roster[field_name] and roster[field_name] != "Empty" else []
                current_entries = [entry for entry in current_entries if entry.strip() and entry != "Empty"]  # Remove empty entries
                if new_entry not in current_entries:
                    current_entries.append(new_entry)
                roster[field_name] = '\n'.join(current_entries) if current_entries else "Empty"
                
                # Add affiliate role
                affiliate_role = interaction.guild.get_role(DIVISION_ROLES["affiliates"])
                if affiliate_role:
                    await user.add_roles(affiliate_role)
                    await log_role_change(
                        interaction.client,
                        "Role Added",
                        user,
                        affiliate_role,
                        interaction.user,
                        reason="Added to Affiliates in roster"
                    )
            else:
                # If no rank is provided, try to get the highest role of the user
                if not rank:
                    member_roles = [role for role in user.roles if role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659, 1389657645887000762, 1389657509630836827, 1389657578853761094]]
                    if member_roles:
                        rank = max(member_roles, key=lambda r: r.position)
                    else:
                        await interaction.followup.send("Please specify a rank for non-affiliate users.", ephemeral=True)
                        return
                
                # Create new entry
                new_entry = f"{rank.mention}: {user.mention}"
                
                # Get current entries, split by newline and remove empty entries
                current_entries = roster[field_name].split('\n') if roster[field_name] and roster[field_name] != "Empty" else []
                current_entries = [entry for entry in current_entries if entry.strip() and entry != "Empty"]
                
                # Remove any existing entry for this user (to avoid duplicates)
                current_entries = [entry for entry in current_entries if user.mention not in entry]
                
                # Add new entry and sort
                current_entries.append(new_entry)
                roster[field_name] = sort_roster_field('\n'.join(current_entries)) if current_entries else "Empty"
                
                # Add division role if applicable
                if field_name in DIVISION_ROLES:
                    division_role = interaction.guild.get_role(DIVISION_ROLES[field_name])
                    if division_role:
                        await user.add_roles(division_role)
                        await log_role_change(
                            interaction.client,
                            "Role Added",
                            user,
                            division_role,
                            interaction.user,
                            reason=f"Added to {field_name.replace('_', ' ').title()} in roster"
                        )
            
            # Save the updated roster data
            for i, r in enumerate(rosters["rosters"]):
                if r["id"] == roster["id"]:
                    rosters["rosters"][i] = roster
                    break
            save_rosters(rosters)
            
            # Update the message with the new embed
            embed = create_roster_embed(roster)
            await interaction.message.edit(embed=embed)
            await interaction.followup.send("User added successfully!", ephemeral=True)
            
            # Clean up the command message
            try:
                await response.delete()
            except:
                pass

        except asyncio.TimeoutError:
            await interaction.followup.send("Timed out. Please try again.", ephemeral=True)

    @discord.ui.button(label="Remove User", style=discord.ButtonStyle.danger, custom_id="remove_user")
    async def remove_user(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to remove users from rosters.", ephemeral=True)
            return

        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        roster = next((r for r in rosters["rosters"] if r["id"] == roster_id), None)
        
        if not roster:
            await interaction.response.send_message("Could not find the roster.", ephemeral=True)
            return

        # Get available fields
        fields = [key for key in roster.keys() if key not in ["id", "title", "description", "channel_id"]]
        if not fields:
            await interaction.response.send_message("No fields available.", ephemeral=True)
            return

        field_list = "\n".join(f"- {field.replace('_', ' ').title()}" for field in fields)
        await interaction.response.send_message(
            "Please mention the user you want to remove and specify the field to remove them from.\n"
            f"Available fields:\n{field_list}\n\n"
            "Format: `@user field_name`\n"
            "Example: `@JohnDoe Ground Forces`",
            ephemeral=True
        )

        def check(m):
            return (m.author == interaction.user and 
                   m.channel == interaction.channel and 
                   len(m.mentions) == 1)

        try:
            response = await interaction.client.wait_for('message', timeout=60.0, check=check)
            
            # Parse the response
            user = response.mentions[0]
            # Get the field name from the remaining content
            content_parts = response.content.split()
            field_name = ' '.join(part for part in content_parts 
                                if not part.startswith('<@') and 
                                not part.endswith('>')).strip().lower().replace(' ', '_')

            if field_name not in fields:
                await interaction.followup.send(
                    f"Invalid field name. Available fields:\n{field_list}",
                    ephemeral=True
                )
                return

            # Remove the user from the field
            lines = roster[field_name].split('\n') if roster[field_name] and roster[field_name] != "Empty" else []
            new_lines = [line for line in lines if user.mention not in line and line.strip() and line != "Empty"]
            roster[field_name] = '\n'.join(new_lines) if new_lines else "Empty"
            
            # Remove division role if applicable
            if field_name in DIVISION_ROLES:
                division_role = interaction.guild.get_role(DIVISION_ROLES[field_name])
                if division_role and division_role in user.roles:
                    await user.remove_roles(division_role)
                    await log_role_change(
                        interaction.client,
                        "Role Removed",
                        user,
                        division_role,
                        interaction.user,
                        reason=f"Removed from {field_name.replace('_', ' ').title()} in roster"
                    )
            
            save_rosters(rosters)
            
            embed = create_roster_embed(roster)
            await interaction.message.edit(embed=embed)
            await interaction.followup.send("User removed successfully!", ephemeral=True)
            
            # Clean up the command message
            try:
                await response.delete()
            except:
                pass

        except asyncio.TimeoutError:
            await interaction.followup.send("Timed out. Please try again.", ephemeral=True)

    @discord.ui.button(label="Sort Fields", style=discord.ButtonStyle.secondary, custom_id="sort_fields")
    async def sort_fields(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to sort fields.", ephemeral=True)
            return

        rosters = load_rosters()
        roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
        roster = next((r for r in rosters["rosters"] if r["id"] == roster_id), None)
        
        if not roster:
            await interaction.response.send_message("Could not find the roster.", ephemeral=True)
            return

        await interaction.response.send_modal(SortFieldsModal(roster))

class CreateRosterModal(discord.ui.Modal, title="Create Roster"):
    def __init__(self):
        super().__init__()
        self.add_item(discord.ui.TextInput(
            label="Roster Title",
            placeholder="Enter the roster title...",
            required=True
        ))
        self.add_item(discord.ui.TextInput(
            label="Description",
            placeholder="Enter a description for the roster...",
            style=discord.TextStyle.paragraph,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        rosters = load_rosters()
        
        # Generate a new unique ID
        new_id = 1
        if rosters["rosters"]:
            new_id = max(roster["id"] for roster in rosters["rosters"]) + 1
        
        # Create new roster with default fields in specific order
        new_roster = {
            "id": new_id,
            "title": self.children[0].value,
            "description": self.children[1].value,
            "channel_id": interaction.channel_id,
            "high_command": "Empty",
            "vanthor_command": "Empty",
            "nox_aeris": "Empty",
            "obsidian_fleet": "Empty",
            "lysara_core": "Empty",
            "affiliates": "Empty"
        }
        
        rosters["rosters"].append(new_roster)
        save_rosters(rosters)
        
        # Create and send the embed
        embed = create_roster_embed(new_roster)
        await interaction.response.send_message(embed=embed, view=RosterView())

def create_roster_embed(roster_data):
    embed = discord.Embed(
        title=roster_data["title"],
        description=roster_data["description"],
        color=0x000000
    )
    
    # Add fields in specific order
    field_order = ["high_command", "vanthor_command", "nox_aeris", "obsidian_fleet", "lysara_core", "vanthor_medica", "affiliates"]
    
    # Process each field in order
    for field_name in field_order:
        if field_name in roster_data:
            value = roster_data[field_name]
            if not value or value.isspace():
                value = "Empty"
            
            # Format the field value
            if "<@" in str(value):
                lines = value.strip().split('\n')
                formatted_lines = []
                for line in lines:
                    if line.strip():  # Only process non-empty lines
                        if ":" in line:
                            role, user = line.split(":", 1)
                            formatted_lines.append(f"{role.strip()} - {user.strip()}")
                        else:
                            formatted_lines.append(line.strip())
                value = '\n'.join(formatted_lines) if formatted_lines else "Empty"
            
            # Set field properties based on field name
            is_inline = field_name not in ["high_command", "affiliates"]
            name = field_name.replace("_", " ").title()
            
            # Add field with appropriate inline setting
            if field_name == "obsidian_fleet":
                # Start a new row for Obsidian Fleet
                if len(embed.fields) % 3 != 0:
                    # Add empty field(s) to force new row
                    remaining = 3 - (len(embed.fields) % 3)
                    for _ in range(remaining):
                        embed.add_field(name="\u200b", value="\u200b", inline=True)
            
            embed.add_field(
                name=name,
                value=value,
                inline=is_inline
            )
    
    embed.set_footer(text=f"Roster #{roster_data['id']}")
    return embed

class Rosters(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Add the persistent view
        self.bot.add_view(RosterView())

    @app_commands.command(name="createroster", description="Create a new roster")
    @app_commands.describe(channel="The channel to send the roster to")
    @app_commands.default_permissions(administrator=True)
    async def createroster(self, interaction: discord.Interaction, channel: discord.TextChannel):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to create rosters.", ephemeral=True)
            return

        rosters = load_rosters()
        new_id = rosters["next_id"]
        rosters["next_id"] += 1
        
        new_roster = {
            "id": new_id,
            "title": f"Roster {new_id}",
            "description": "Aegis Nox Global Roster",
            "channel_id": channel.id,
            "high_command": "Empty",
            "vanthor_command": "Empty",
            "nox_aeris": "Empty",
            "obsidian_fleet": "Empty",
            "lysara_core": "Empty",
            "vanthor_medica": "Empty",
            "affiliates": "Empty"
        }
        
        rosters["rosters"].append(new_roster)
        save_rosters(rosters)
        
        embed = create_roster_embed(new_roster)
        await channel.send(embed=embed, view=RosterView())
        await interaction.response.send_message(f"Roster created in {channel.mention}!", ephemeral=True)

    @app_commands.command(name="editroster", description="Edit a roster by ID")
    @app_commands.describe(roster_id="The ID of the roster to edit")
    @app_commands.default_permissions(administrator=True)
    async def editroster(self, interaction: discord.Interaction, roster_id: int):
        if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to edit rosters.", ephemeral=True)
            return

        rosters = load_rosters()
        roster = next((r for r in rosters["rosters"] if r["id"] == roster_id), None)
        
        if roster:
            channel = interaction.guild.get_channel(roster["channel_id"])
            if channel:
                embed = create_roster_embed(roster)
                await channel.send(embed=embed, view=RosterView())
                await interaction.response.send_message(f"Roster loaded in {channel.mention}!", ephemeral=True)
            else:
                await interaction.response.send_message("The channel for this roster no longer exists.", ephemeral=True)
        else:
            await interaction.response.send_message(f"No roster found with ID #{roster_id}", ephemeral=True)

    @app_commands.command(name="promote", description="Promote a user to the next rank")
    @app_commands.describe(user="The user to promote")
    @app_commands.default_permissions(administrator=True)
    async def promote(self, interaction: discord.Interaction, user: discord.Member):
        try:
            # Defer the response immediately to prevent timeout
            await interaction.response.defer(ephemeral=True)
            
            # Check if the command user has permission to promote
            if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
                await interaction.followup.send("You don't have permission to promote users.", ephemeral=True)
                return

            # Get next rank for the user
            next_rank = get_next_rank(user.roles)
            if not next_rank:
                await interaction.followup.send(f"{user.mention} is already at the highest rank or has no current rank.", ephemeral=True)
                return

            next_rank_name, next_rank_id, rank_type = next_rank
            
            # Get the roles to modify
            roles_to_remove = []
            roles_to_add = []
            
            # Remove all current rank roles
            for role in user.roles:
                if any(role.id == rank["id"] for rank in ROLE_HIERARCHY):
                    roles_to_remove.append(role)
            
            # Add the next rank role
            next_rank_role = interaction.guild.get_role(next_rank_id)
            if next_rank_role:
                roles_to_add.append(next_rank_role)
            
            # Add Officer role if promoting to Division Coordinator or above
            officer_role = interaction.guild.get_role(OFFICER_ROLE_ID)
            if next_rank_name in ["Division Coordinator", "Commander", "Operations Lead", "Founder"]:
                if officer_role and officer_role not in user.roles:
                    roles_to_add.append(officer_role)
            elif officer_role in user.roles:
                roles_to_remove.append(officer_role)
            
            # Add Aegis Nox role if they don't have it
            aegis_nox_role = interaction.guild.get_role(AEGIS_NOX_ROLE_ID)
            if aegis_nox_role and aegis_nox_role not in user.roles:
                roles_to_add.append(aegis_nox_role)
            
            # Find current highest rank for logging
            current_rank = None
            for role in user.roles:
                if any(role.id == rank["id"] for rank in ROLE_HIERARCHY):
                    current_rank = role
                    break
            
            # Apply role changes
            if roles_to_remove:
                await user.remove_roles(*roles_to_remove)
            if roles_to_add:
                await user.add_roles(*roles_to_add)
            
            # Log the promotion
            await log_promotion(
                self.bot,
                user,
                current_rank,
                next_rank_role,
                interaction.user
            )
            
            # Log individual role changes
            for role in roles_to_remove:
                if role != current_rank:  # Don't log the rank role since it's included in the promotion log
                    await log_role_change(
                        self.bot,
                        "Role Removed",
                        user,
                        role,
                        interaction.user,
                        reason=f"Removed as part of promotion to {next_rank_name}"
                    )
            
            for role in roles_to_add:
                if role != next_rank_role:  # Don't log the rank role since it's included in the promotion log
                    await log_role_change(
                        self.bot,
                        "Role Added",
                        user,
                        role,
                        interaction.user,
                        reason=f"Added as part of promotion to {next_rank_name}"
                    )
            
            # Update rosters
            rosters = load_rosters()
            updated = False
            
            for roster in rosters["rosters"]:
                for field_name, field_content in roster.items():
                    if field_name not in ["id", "title", "description", "channel_id"]:
                        if str(user.id) in field_content:
                            # Remove old entry
                            lines = field_content.split('\n')
                            new_lines = [line for line in lines if str(user.id) not in line]
                            
                            # Add new entry with updated rank
                            new_entry = f"<@&{next_rank_id}>: {user.mention}"
                            new_lines.append(new_entry)
                            
                            # Update field content with sorted entries
                            roster[field_name] = sort_roster_field("\n".join(new_lines))
                            updated = True
            
            if updated:
                save_rosters(rosters)
                
                # Update all roster messages
                for roster in rosters["rosters"]:
                    channel = interaction.guild.get_channel(roster["channel_id"])
                    if channel:
                        try:
                            async for message in channel.history(limit=100):
                                if message.embeds and message.embeds[0].footer.text == f"Roster #{roster['id']}":
                                    embed = create_roster_embed(roster)
                                    await message.edit(embed=embed, view=RosterView())
                                    break
                        except:
                            continue

            # Prepare role change messages
            removed_roles = ", ".join(role.name for role in roles_to_remove)
            added_roles = ", ".join(role.name for role in roles_to_add)
            role_changes = []
            if removed_roles:
                role_changes.append(f"Removed roles: {removed_roles}")
            if added_roles:
                role_changes.append(f"Added roles: {added_roles}")

            # Send response message using followup
            await interaction.followup.send(
                f"Successfully promoted {user.mention} to {next_rank_name}!\n" +
                "\n".join(role_changes),
                ephemeral=True
            )
            
        except Exception as e:
            try:
                await interaction.followup.send(f"An error occurred while promoting: {str(e)}", ephemeral=True)
            except:
                # Only as an absolute last resort, send to channel
                await interaction.channel.send(f"An error occurred while promoting: {str(e)}")

    @app_commands.command(name="demote", description="Demote a user to the previous rank")
    @app_commands.describe(user="The user to demote")
    @app_commands.default_permissions(administrator=True)
    async def demote(self, interaction: discord.Interaction, user: discord.Member):
        try:
            # Defer the response immediately to prevent timeout
            await interaction.response.defer(ephemeral=True)
            
            # Check if the command user has permission to demote
            if not any(role.id in [1389657163252760636, 1389657332782334112, 1389657392685252659] for role in interaction.user.roles):
                await interaction.followup.send("You don't have permission to demote users.", ephemeral=True)
                return

            # Get previous rank for the user
            prev_rank = get_previous_rank(user.roles)
            if not prev_rank:
                await interaction.followup.send(f"{user.mention} is already at the lowest rank or has no current rank.", ephemeral=True)
                return

            prev_rank_name, prev_rank_id, rank_type = prev_rank
            
            # Get the roles to modify
            roles_to_remove = []
            roles_to_add = []
            
            # Remove all current rank roles
            for role in user.roles:
                if any(role.id == rank["id"] for rank in ROLE_HIERARCHY):
                    roles_to_remove.append(role)
            
            # Add the previous rank role
            prev_rank_role = interaction.guild.get_role(prev_rank_id)
            if prev_rank_role:
                roles_to_add.append(prev_rank_role)
            
            # Handle Officer role
            officer_role = interaction.guild.get_role(OFFICER_ROLE_ID)
            if officer_role:
                # Remove Officer role if demoting to Team Lead or below
                if prev_rank_name in ["Team Lead", "Operator", "Initiate"]:
                    if officer_role in user.roles:
                        roles_to_remove.append(officer_role)
                else:
                    # Keep Officer role for Division Coordinator and above
                    if officer_role not in user.roles:
                        roles_to_add.append(officer_role)
            
            # Keep Aegis Nox role
            
            # Find current highest rank for logging
            current_rank = None
            for role in user.roles:
                if any(role.id == rank["id"] for rank in ROLE_HIERARCHY):
                    current_rank = role
                    break
            
            # Apply role changes
            if roles_to_remove:
                await user.remove_roles(*roles_to_remove)
            if roles_to_add:
                await user.add_roles(*roles_to_add)
            
            # Log the demotion
            await log_demotion(
                self.bot,
                user,
                current_rank,
                prev_rank_role,
                interaction.user
            )
            
            # Log individual role changes
            for role in roles_to_remove:
                if role != current_rank:  # Don't log the rank role since it's included in the demotion log
                    await log_role_change(
                        self.bot,
                        "Role Removed",
                        user,
                        role,
                        interaction.user,
                        reason=f"Removed as part of demotion to {prev_rank_name}"
                    )
            
            for role in roles_to_add:
                if role != prev_rank_role:  # Don't log the rank role since it's included in the demotion log
                    await log_role_change(
                        self.bot,
                        "Role Added",
                        user,
                        role,
                        interaction.user,
                        reason=f"Added as part of demotion to {prev_rank_name}"
                    )
            
            # Update rosters
            rosters = load_rosters()
            updated = False
            
            for roster in rosters["rosters"]:
                for field_name, field_content in roster.items():
                    if field_name not in ["id", "title", "description", "channel_id"]:
                        if str(user.id) in field_content:
                            # Remove old entry
                            lines = field_content.split('\n')
                            new_lines = [line for line in lines if str(user.id) not in line]
                            
                            # Add new entry with updated rank
                            new_entry = f"<@&{prev_rank_id}>: {user.mention}"
                            new_lines.append(new_entry)
                            
                            # Update field content with sorted entries
                            roster[field_name] = sort_roster_field("\n".join(new_lines))
                            updated = True
            
            if updated:
                save_rosters(rosters)
                
                # Update all roster messages
                for roster in rosters["rosters"]:
                    channel = interaction.guild.get_channel(roster["channel_id"])
                    if channel:
                        try:
                            async for message in channel.history(limit=100):
                                if message.embeds and message.embeds[0].footer.text == f"Roster #{roster['id']}":
                                    embed = create_roster_embed(roster)
                                    await message.edit(embed=embed, view=RosterView())
                                    break
                        except:
                            continue
            
            # Prepare role change messages
            removed_roles = ", ".join(role.name for role in roles_to_remove)
            added_roles = ", ".join(role.name for role in roles_to_add)
            role_changes = []
            if removed_roles:
                role_changes.append(f"Removed roles: {removed_roles}")
            if added_roles:
                role_changes.append(f"Added roles: {added_roles}")
            
            # Send response message using followup
            await interaction.followup.send(
                f"Successfully demoted {user.mention} to {prev_rank_name}!\n" +
                "\n".join(role_changes),
                ephemeral=True
            )
            
        except Exception as e:
            try:
                await interaction.followup.send(f"An error occurred while demoting: {str(e)}", ephemeral=True)
            except:
                await interaction.channel.send(f"An error occurred while demoting: {str(e)}")

async def setup(bot):
    await bot.add_cog(Rosters(bot)) 