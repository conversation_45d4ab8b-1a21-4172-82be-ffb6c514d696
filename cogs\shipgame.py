import discord
from discord.ext import commands
from discord import app_commands
import json
import os
import asyncio
import random
from datetime import datetime
from typing import Dict, List, Optional

# Constants
SHIP_ATTACK_CHANNEL_ID = 1389659396346216489
SHIP_HEALTH = 100
DAMAGE_PER_SHOT = 25
ATTACK_INTERVAL = 600  # 10 minutes in seconds
DESTROYED_EMBED_DURATION = 60  # Duration to show the destroyed embed
SHIP_TIMEOUT = 300  # 5 minutes in seconds
ESCAPE_MESSAGE_DURATION = 30  # Duration to show escape message

class ShipAttackView(discord.ui.View):
    def __init__(self, cog, ship_name: str):
        super().__init__(timeout=None)
        self.cog = cog
        self.ship_name = ship_name
        self.health = SHIP_HEALTH
        self.last_shooter: Optional[discord.Member] = None
        self.lock = asyncio.Lock()
        self.escape_task: Optional[asyncio.Task] = None
        self.has_been_shot = False
        self.original_embed = None

        # Add four shoot buttons
        for i in range(4):
            button = discord.ui.Button(
                style=discord.ButtonStyle.danger,
                label=f"Shoot {i+1}",
                custom_id=f"shoot_{i}"
            )
            button.callback = self.shoot_callback
            self.add_item(button)

    async def handle_escape(self, message: discord.Message):
        """Handle the ship escaping after timeout"""
        await asyncio.sleep(SHIP_TIMEOUT)
        
        async with self.lock:
            if not self.has_been_shot:  # Only escape if no one has shot the ship
                escape_embed = discord.Embed(
                    title="Ship Escaped!",
                    description=f"The {self.ship_name} got away!",
                    color=discord.Color.orange()
                )
                
                # Send escape message
                escape_msg = await message.channel.send(embed=escape_embed)
                
                # Delete both messages after delay
                await asyncio.sleep(ESCAPE_MESSAGE_DURATION)
                await message.delete()
                await escape_msg.delete()

    async def update_embed(self, message: discord.Message):
        """Update only the description of the embed"""
        if self.original_embed:
            self.original_embed.description = f"{self.ship_name} is attacking! Shoot it down!\nHealth: {self.health}HP"
            await message.edit(embed=self.original_embed)

    async def shoot_callback(self, interaction: discord.Interaction):
        async with self.lock:  # Prevent race conditions
            if self.health <= 0:
                await interaction.response.send_message("This ship has already been destroyed!", ephemeral=True)
                return

            # Cancel escape task on first shot
            if not self.has_been_shot:
                self.has_been_shot = True
                if self.escape_task and not self.escape_task.done():
                    self.escape_task.cancel()

            self.health -= DAMAGE_PER_SHOT
            self.last_shooter = interaction.user

            if self.health <= 0:
                # Ship destroyed
                await self.handle_ship_destruction(interaction)
            else:
                # Send response first
                await interaction.response.send_message(
                    f"You shot the {self.ship_name}! Health remaining: {self.health}HP",
                    ephemeral=True
                )
                # Then update the embed description only
                await self.update_embed(interaction.message)

    async def handle_ship_destruction(self, interaction: discord.Interaction):
        # Add ship to user's fleet
        await self.cog.add_ship_to_fleet(self.last_shooter, self.ship_name)
        
        # Create destroyed embed
        destroyed_embed = discord.Embed(
            title="Ship Destroyed!",
            description=f"{self.last_shooter.mention} has destroyed the {self.ship_name}!",
            color=discord.Color.green()
        )
        destroyed_embed.set_image(url="attachment://shipdestroyed.jpeg")

        # Delete the attack message
        await interaction.message.delete()

        # Send destroyed message with image
        file = discord.File("shipdestroyed.jpeg")
        destroyed_msg = await interaction.channel.send(
            embed=destroyed_embed,
            file=file
        )

        # Delete the destroyed message after delay
        await asyncio.sleep(DESTROYED_EMBED_DURATION)
        await destroyed_msg.delete()

class ShipGame(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.attack_task: Optional[asyncio.Task] = None
        self.fleet_data_file = 'data/discordfleets.json'
        self.fleet_data: Dict[str, List[str]] = self.load_fleet_data()

    def load_fleet_data(self) -> Dict[str, List[str]]:
        """Load fleet data from JSON file"""
        if os.path.exists(self.fleet_data_file):
            with open(self.fleet_data_file, 'r') as f:
                return json.load(f)
        return {}

    def save_fleet_data(self):
        """Save fleet data to JSON file"""
        os.makedirs(os.path.dirname(self.fleet_data_file), exist_ok=True)
        with open(self.fleet_data_file, 'w') as f:
            json.dump(self.fleet_data, f, indent=4)

    async def add_ship_to_fleet(self, user: discord.Member, ship_name: str):
        """Add a ship to a user's fleet"""
        user_id = str(user.id)
        if user_id not in self.fleet_data:
            self.fleet_data[user_id] = []
        self.fleet_data[user_id].append(ship_name)
        self.save_fleet_data()

    def get_random_ship(self) -> tuple[str, str]:
        """Get a random ship name and its image path from the shipimages folder"""
        ship_files = [f for f in os.listdir('data/shipimages') if f.endswith('.png')]
        if not ship_files:
            raise ValueError("No ship images found")
        
        ship_file = random.choice(ship_files)
        ship_name = ship_file[:-4]  # Remove .png extension
        ship_path = f'data/shipimages/{ship_file}'
        return ship_name, ship_path

    async def get_user_fleet_embed(self, user: discord.Member, is_self: bool = True) -> discord.Embed:
        """Get the fleet embed for a user"""
        user_id = str(user.id)
        
        title = "Your Discord Ship Fleet" if is_self else f"{user.name}'s Discord Ship Fleet"
        
        if user_id not in self.fleet_data or not self.fleet_data[user_id]:
            return discord.Embed(
                title=title,
                description="No ships in fleet yet!",
                color=discord.Color.darker_grey()
            )

        # Count ships
        ship_counts = {}
        for ship in self.fleet_data[user_id]:
            ship_counts[ship] = ship_counts.get(ship, 0) + 1

        # Create fleet display
        fleet_list = "\n".join(f"{ship} x{count}" for ship, count in sorted(ship_counts.items()))
        total_ships = sum(ship_counts.values())
        
        embed = discord.Embed(
            title=title,
            description=f"{fleet_list}\n\n**Total Ships: {total_ships}**",
            color=discord.Color.darker_grey()
        )
        
        return embed

    @app_commands.command(
        name="showdiscordfleet",
        description="Show your or another user's Discord ship fleet"
    )
    @app_commands.describe(user="The user whose fleet to show (optional)")
    async def show_discord_fleet(self, interaction: discord.Interaction, user: discord.Member = None):
        target_user = user or interaction.user
        embed = await self.get_user_fleet_embed(target_user, is_self=(target_user == interaction.user))
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(
        name="discordfleetleaderboard",
        description="Show the Discord fleet leaderboard"
    )
    async def discord_fleet_leaderboard(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)  # Defer the response to avoid timeout

        # Calculate total ships for each user
        user_totals = []
        for user_id, ships in self.fleet_data.items():
            try:
                member = await interaction.guild.fetch_member(int(user_id))
                if member:
                    user_totals.append((member, len(ships)))
            except discord.NotFound:
                continue

        # Sort by ship count, descending
        user_totals.sort(key=lambda x: x[1], reverse=True)

        if not user_totals:
            embed = discord.Embed(
                title="Discord Fleet Leaderboard",
                description="No ships have been collected yet!",
                color=discord.Color.darker_grey()
            )
        else:
            # Create leaderboard text
            leaderboard_text = ""
            for i, (member, count) in enumerate(user_totals, 1):
                medal = {1: "\U0001F947", 2: "\U0001F948", 3: "\U0001F949"}.get(i, "")
                leaderboard_text += f"{medal} **#{i}** - {member.mention}: {count} ships\n"

            embed = discord.Embed(
                title="Discord Fleet Leaderboard",
                description=leaderboard_text,
                color=discord.Color.darker_grey()
            )

        await interaction.followup.send(embed=embed, ephemeral=True)  # Use followup.send to send the embed

    async def spawn_ship_attack(self):
        """Spawn a new ship attack"""
        channel = self.bot.get_channel(SHIP_ATTACK_CHANNEL_ID)
        if not channel:
            return

        ship_name, ship_image_path = self.get_random_ship()
        
        # Create the embed first
        embed = discord.Embed(
            title="Rogue Ship Attack!",
            description=f"{ship_name} is attacking! Shoot it down!\nHealth: {SHIP_HEALTH}HP",
            color=discord.Color.red()
        )
        
        # Attach the image file and set the embed's image
        file = discord.File(ship_image_path, filename=os.path.basename(ship_image_path))
        embed.set_image(url=f"attachment://{os.path.basename(ship_image_path)}")

        # Create view and send message with both embed and file
        view = ShipAttackView(self, ship_name)
        message = await channel.send(file=file, embed=embed, view=view)
        
        # Store the original embed in the view for updates
        view.original_embed = embed
        
        # Start escape timer
        view.escape_task = asyncio.create_task(view.handle_escape(message))

    async def start_attack_loop(self):
        """Start the periodic ship attack loop"""
        while True:
            try:
                await self.spawn_ship_attack()
                await asyncio.sleep(ATTACK_INTERVAL)
            except Exception as e:
                print(f"Error in ship attack loop: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying if there's an error

    async def cog_load(self):
        """This runs when the cog is loaded"""
        self.attack_task = asyncio.create_task(self.start_attack_loop())

    async def cog_unload(self):
        """This runs when the cog is unloaded"""
        if self.attack_task:
            self.attack_task.cancel()

async def setup(bot):
    await bot.add_cog(ShipGame(bot)) 