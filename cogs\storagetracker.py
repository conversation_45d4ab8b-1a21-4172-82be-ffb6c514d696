import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Button, View, Modal, TextInput
import json
from typing import Optional
from datetime import datetime

class AddItemModal(Modal):
    def __init__(self, original_message: discord.Message):
        super().__init__(title="Add Item to Storage")
        self.original_message = original_message
        
        self.item_name = TextInput(
            label="Item Name",
            placeholder="Enter the item name",
            required=True,
            max_length=100
        )
        self.add_item(self.item_name)
        
        self.quantity = TextInput(
            label="Quantity",
            placeholder="Enter the quantity",
            required=True,
            max_length=10
        )
        self.add_item(self.quantity)
        
        self.scu = TextInput(
            label="SCU (Optional)",
            placeholder="Enter the SCU value",
            required=False,
            max_length=10
        )
        self.add_item(self.scu)
        
        self.location = TextInput(
            label="Location",
            placeholder="Enter storage location (e.g., Port Tressler)",
            required=True,
            max_length=100
        )
        self.add_item(self.location)
        
        self.item_type = TextInput(
            label="Item Type",
            placeholder="Armor/Clothing/Weapons/Ship Parts/Commodities/Vehicles",
            required=True,
            max_length=50
        )
        self.add_item(self.item_type)

    def normalize_item_type(self, item_type: str) -> str:
        # Dictionary of known categories and their normalized forms
        categories = {
            "armor": "Armor",
            "clothing": "Clothing",
            "weapon": "Weapons",
            "weapons": "Weapons",
            "ship part": "Ship Parts",
            "ship parts": "Ship Parts",
            "commodity": "Commodities",
            "commodities": "Commodities",
            "vehicle": "Vehicles",
            "vehicles": "Vehicles"
        }
        
        # Try to match the input type to a known category
        normalized = item_type.lower().strip()
        for key, value in categories.items():
            if key in normalized:
                return value
                
        # If no match found, use the title-cased version of the input
        return item_type.strip().title()

    async def on_submit(self, interaction: discord.Interaction):
        try:
            with open('data/storage_data.json', 'r') as f:
                storage_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            storage_data = {}

        user_id = str(interaction.user.id)
        if user_id not in storage_data:
            storage_data[user_id] = {
                "next_id": 0,
                "items": []
            }

        # Get next available ID
        next_id = storage_data[user_id]["next_id"]

        # Create new item entry with normalized item type
        new_item = {
            "id": next_id,
            "name": self.item_name.value,
            "quantity": self.quantity.value,
            "scu": self.scu.value if self.scu.value else "N/A",
            "location": self.location.value,
            "type": self.normalize_item_type(self.item_type.value),
            "added_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        storage_data[user_id]["items"].append(new_item)
        storage_data[user_id]["next_id"] = next_id + 1

        # Save updated storage data
        with open('data/storage_data.json', 'w') as f:
            json.dump(storage_data, f, indent=4)

        # Create updated embed
        embed = await self.create_storage_embed(interaction.user, storage_data)
        
        await interaction.response.send_message("Item added to your storage!", ephemeral=True)
        await self.original_message.edit(embed=embed)

    async def create_storage_embed(self, user: discord.Member, storage_data: dict) -> discord.Embed:
        user_id = str(user.id)
        
        embed = discord.Embed(
            title=f"📦 Storage Inventory - {user.display_name}",
            description="Track your stored items and their locations across the 'verse!" if storage_data.get(user_id, {}).get("items") else "No items in storage.",
            color=discord.Color.blue()
        )

        if user_id in storage_data and storage_data[user_id].get("items"):
            categories = {
                "Armor": [],
                "Clothing": [],
                "Weapons": [],
                "Ship Parts": [],
                "Commodities": [],
                "Vehicles": [],
                "Other": []
            }

            for item in storage_data[user_id]["items"]:
                item_type = item['type']
                if item_type in categories:
                    categories[item_type].append(item)
                else:
                    categories["Other"].append(item)

            for category, items in categories.items():
                if items:
                    items_text = ""
                    for item in items:
                        items_text += f"**ID: {item['id']}** - {item['name']}\n"
                        items_text += f"└ Qty: {item['quantity']} | SCU: {item['scu']} | Location: {item['location']}\n"
                    embed.add_field(name=f"🔹 {category}", value=items_text, inline=False)

        embed.set_footer(text="Use the buttons below to add or remove items")
        return embed

class RemoveItemModal(Modal):
    def __init__(self, original_message: discord.Message):
        super().__init__(title="Remove Item from Storage")
        self.original_message = original_message
        
        self.item_id = TextInput(
            label="Item ID",
            placeholder="Enter the item ID to remove (e.g., 0, 1, 2)",
            required=True,
            max_length=8
        )
        self.add_item(self.item_id)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            with open('data/storage_data.json', 'r') as f:
                storage_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            await interaction.response.send_message("No storage data found!", ephemeral=True)
            return

        user_id = str(interaction.user.id)
        if user_id not in storage_data:
            await interaction.response.send_message("You don't have any items in storage!", ephemeral=True)
            return

        try:
            item_id = int(self.item_id.value)
        except ValueError:
            await interaction.response.send_message("Please enter a valid number for the Item ID!", ephemeral=True)
            return

        item_found = False
        for item in storage_data[user_id]["items"][:]:
            if item['id'] == item_id:
                storage_data[user_id]["items"].remove(item)
                item_found = True
                break

        if item_found:
            with open('data/storage_data.json', 'w') as f:
                json.dump(storage_data, f, indent=4)

            # Create updated embed
            embed = await self.create_storage_embed(interaction.user, storage_data)
            
            await interaction.response.send_message("Item removed from your storage!", ephemeral=True)
            await self.original_message.edit(embed=embed)
        else:
            await interaction.response.send_message("Item ID not found in your storage!", ephemeral=True)

    async def create_storage_embed(self, user: discord.Member, storage_data: dict) -> discord.Embed:
        user_id = str(user.id)
        
        embed = discord.Embed(
            title=f"📦 Storage Inventory - {user.display_name}",
            description="Track your stored items and their locations across the 'verse!" if storage_data.get(user_id, {}).get("items") else "No items in storage.",
            color=discord.Color.blue()
        )

        if user_id in storage_data and storage_data[user_id].get("items"):
            categories = {
                "Armor": [],
                "Clothing": [],
                "Weapons": [],
                "Ship Parts": [],
                "Commodities": [],
                "Vehicles": [],
                "Other": []
            }

            for item in storage_data[user_id]["items"]:
                item_type = item['type']
                if item_type in categories:
                    categories[item_type].append(item)
                else:
                    categories["Other"].append(item)

            for category, items in categories.items():
                if items:
                    items_text = ""
                    for item in items:
                        items_text += f"**ID: {item['id']}** - {item['name']}\n"
                        items_text += f"└ Qty: {item['quantity']} | SCU: {item['scu']} | Location: {item['location']}\n"
                    embed.add_field(name=f"🔹 {category}", value=items_text, inline=False)

        embed.set_footer(text="Use the buttons below to add or remove items")
        return embed

class StorageView(View):
    def __init__(self, message: Optional[discord.Message] = None):
        super().__init__(timeout=None)  # Make the view persistent
        self.message = message

    @discord.ui.button(label="Add Item", style=discord.ButtonStyle.green, custom_id="add_item")
    async def add_item(self, interaction: discord.Interaction, button: Button):
        await interaction.response.send_modal(AddItemModal(self.message))

    @discord.ui.button(label="Remove Item", style=discord.ButtonStyle.red, custom_id="remove_item")
    async def remove_item(self, interaction: discord.Interaction, button: Button):
        await interaction.response.send_modal(RemoveItemModal(self.message))

class StorageTracker(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.persistent_views_added = False

    @commands.Cog.listener()
    async def on_ready(self):
        if not self.persistent_views_added:
            # Add the view for persistent buttons
            self.bot.add_view(StorageView())
            self.persistent_views_added = True

    @app_commands.command(
        name="storage",
        description="View your or another player's storage inventory"
    )
    async def storage(
        self,
        interaction: discord.Interaction,
        user: Optional[discord.Member] = None
    ):
        target_user = user if user else interaction.user
        
        try:
            with open('data/storage_data.json', 'r') as f:
                storage_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            storage_data = {}

        user_id = str(target_user.id)
        
        # Create embed
        embed = discord.Embed(
            title=f"📦 Storage Inventory - {target_user.display_name}",
            description="Track your stored items and their locations across the 'verse!" if storage_data.get(user_id, {}).get("items") else "No items in storage.",
            color=discord.Color.blue()
        )

        if user_id in storage_data and storage_data[user_id].get("items"):
            categories = {
                "Armor": [],
                "Clothing": [],
                "Weapons": [],
                "Ship Parts": [],
                "Commodities": [],
                "Vehicles": [],
                "Other": []
            }

            for item in storage_data[user_id]["items"]:
                item_type = item['type']
                if item_type in categories:
                    categories[item_type].append(item)
                else:
                    categories["Other"].append(item)

            for category, items in categories.items():
                if items:
                    items_text = ""
                    for item in items:
                        items_text += f"**ID: {item['id']}** - {item['name']}\n"
                        items_text += f"└ Qty: {item['quantity']} | SCU: {item['scu']} | Location: {item['location']}\n"
                    embed.add_field(name=f"🔹 {category}", value=items_text, inline=False)

        embed.set_footer(text="Use the buttons below to add or remove items")

        # Only show buttons if viewing own storage
        if target_user.id == interaction.user.id:
            view = StorageView()
            await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
            # Get the message after sending
            original_message = await interaction.original_response()
            # Update the view with the message reference
            view.message = original_message
        else:
            await interaction.response.send_message(embed=embed, ephemeral=True)

async def setup(bot):
    await bot.add_cog(StorageTracker(bot)) 