import discord
from discord.ext import commands
import json
import os
from typing import Dict, Optional

class VoiceChannelView(discord.ui.View):
    def __init__(self, cog, channel: discord.VoiceChannel):
        super().__init__(timeout=None)
        self.cog = cog
        self.channel = channel

    async def update_control_embed(self, interaction: discord.Interaction):
        """Update the control panel embed with current whitelist/blacklist"""
        channel_id = str(self.channel.id)
        whitelist = self.cog.voice_data[channel_id].get("whitelist", [])
        blacklist = self.cog.voice_data[channel_id].get("blacklist", [])
        
        whitelist_members = []
        blacklist_members = []
        
        for user_id in whitelist:
            member = interaction.guild.get_member(user_id)
            if member:
                whitelist_members.append(f"{member.mention} ({member.name})")
        
        for user_id in blacklist:
            member = interaction.guild.get_member(user_id)
            if member:
                blacklist_members.append(f"{member.mention} ({member.name})")
        
        embed = discord.Embed(
            title="Voice Channel Controls",
            description=(
                f"Channel: {self.channel.mention}\n\n"
                f"**Features:**\n"
                f"• Lock/Unlock Channel (blocks @everyone and all other roles from joining unless whitelisted.)\n"
                f"• Set User Limit\n"
                f"• Rename Channel\n"
                f"• Delete Channel\n"
                f"• Manage Whitelist/Blacklist\n\n"
                f"**Whitelisted Users:**\n"
                f"{'None' if not whitelist_members else chr(10).join(whitelist_members)}\n\n"
                f"**Blacklisted Users:**\n"
                f"{'None' if not blacklist_members else chr(10).join(blacklist_members)}"
            ),
            color=discord.Color.from_rgb(0, 0, 0)
        )
        
        await interaction.message.edit(embed=embed)

    @discord.ui.button(label="Manage Whitelist", style=discord.ButtonStyle.success)
    async def manage_whitelist(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Manage whitelist users"""
        if interaction.user.id != self.cog.voice_data[str(self.channel.id)]["owner"]:
            await interaction.response.send_message("Only the channel owner can use these controls!", ephemeral=True)
            return

        class WhitelistModal(discord.ui.Modal, title="Manage Whitelist"):
            username = discord.ui.TextInput(
                label="Username",
                placeholder="Enter username to add/remove from whitelist",
                required=True
            )

            async def on_submit(self, interaction: discord.Interaction):
                try:
                    # Find member by username
                    member = None
                    for guild_member in interaction.guild.members:
                        if guild_member.name.lower() == self.username.value.lower():
                            member = guild_member
                            break
                    
                    if not member:
                        await interaction.response.send_message("User not found!", ephemeral=True)
                        return

                    channel_id = str(self.channel.id)
                    if "whitelist" not in self.cog.voice_data[channel_id]:
                        self.cog.voice_data[channel_id]["whitelist"] = []
                    if "blacklist" not in self.cog.voice_data[channel_id]:
                        self.cog.voice_data[channel_id]["blacklist"] = []

                    # Remove from blacklist if they're on it
                    if member.id in self.cog.voice_data[channel_id]["blacklist"]:
                        self.cog.voice_data[channel_id]["blacklist"].remove(member.id)

                    if member.id in self.cog.voice_data[channel_id]["whitelist"]:
                        # Remove from whitelist
                        self.cog.voice_data[channel_id]["whitelist"].remove(member.id)
                        overwrites = self.channel.overwrites
                        if member in overwrites:
                            del overwrites[member]
                        await self.channel.edit(overwrites=overwrites)
                        await interaction.response.send_message(f"Removed {member.mention} from whitelist!", ephemeral=True)
                    else:
                        # Add to whitelist
                        self.cog.voice_data[channel_id]["whitelist"].append(member.id)
                        overwrites = self.channel.overwrites
                        overwrites[member] = discord.PermissionOverwrite(connect=True)
                        await self.channel.edit(overwrites=overwrites)
                        await interaction.response.send_message(f"Added {member.mention} to whitelist!", ephemeral=True)

                    self.cog.save_voice_data()
                    await self.view.update_control_embed(interaction)
                except Exception as e:
                    await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

        modal = WhitelistModal()
        modal.channel = self.channel
        modal.cog = self.cog
        modal.view = self
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="Manage Blacklist", style=discord.ButtonStyle.danger)
    async def manage_blacklist(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Manage blacklist users"""
        if interaction.user.id != self.cog.voice_data[str(self.channel.id)]["owner"]:
            await interaction.response.send_message("Only the channel owner can use these controls!", ephemeral=True)
            return

        class BlacklistModal(discord.ui.Modal, title="Manage Blacklist"):
            username = discord.ui.TextInput(
                label="Username",
                placeholder="Enter username to add/remove from blacklist",
                required=True
            )

            async def on_submit(self, interaction: discord.Interaction):
                try:
                    # Find member by username
                    member = None
                    for guild_member in interaction.guild.members:
                        if guild_member.name.lower() == self.username.value.lower():
                            member = guild_member
                            break
                    
                    if not member:
                        await interaction.response.send_message("User not found!", ephemeral=True)
                        return

                    channel_id = str(self.channel.id)
                    if "blacklist" not in self.cog.voice_data[channel_id]:
                        self.cog.voice_data[channel_id]["blacklist"] = []
                    if "whitelist" not in self.cog.voice_data[channel_id]:
                        self.cog.voice_data[channel_id]["whitelist"] = []

                    # Remove from whitelist if they're on it
                    if member.id in self.cog.voice_data[channel_id]["whitelist"]:
                        self.cog.voice_data[channel_id]["whitelist"].remove(member.id)

                    if member.id in self.cog.voice_data[channel_id]["blacklist"]:
                        # Remove from blacklist
                        self.cog.voice_data[channel_id]["blacklist"].remove(member.id)
                        overwrites = self.channel.overwrites
                        if member in overwrites:
                            del overwrites[member]
                        await self.channel.edit(overwrites=overwrites)
                        await interaction.response.send_message(f"Removed {member.mention} from blacklist!", ephemeral=True)
                    else:
                        # Add to blacklist
                        self.cog.voice_data[channel_id]["blacklist"].append(member.id)
                        overwrites = self.channel.overwrites
                        overwrites[member] = discord.PermissionOverwrite(connect=False)
                        await self.channel.edit(overwrites=overwrites)
                        
                        if member in self.channel.members:
                            await member.move_to(None)
                        
                        await interaction.response.send_message(f"Added {member.mention} to blacklist!", ephemeral=True)

                    self.cog.save_voice_data()
                    await self.view.update_control_embed(interaction)
                except Exception as e:
                    await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

        modal = BlacklistModal()
        modal.channel = self.channel
        modal.cog = self.cog
        modal.view = self
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="Lock Channel", style=discord.ButtonStyle.secondary)
    async def toggle_lock(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Toggle lock/unlock state of the voice channel"""
        if interaction.user.id != self.cog.voice_data[str(self.channel.id)]["owner"]:
            await interaction.response.send_message("Only the channel owner can use these controls!", ephemeral=True)
            return

        is_locked = self.cog.voice_data[str(self.channel.id)]["locked"]
        overwrites = self.channel.overwrites
        
        if not is_locked:
            # Lock the channel - block verified role
            verified_role = interaction.guild.get_role(1389657746986500157)
            overwrites[verified_role] = discord.PermissionOverwrite(connect=False)
            button.style = discord.ButtonStyle.primary
            button.label = "Unlock Channel"
            self.cog.voice_data[str(self.channel.id)]["locked"] = True
        else:
            # Unlock the channel - allow verified role
            verified_role = interaction.guild.get_role(1389657746986500157)
            overwrites[verified_role] = discord.PermissionOverwrite(connect=True)
            button.style = discord.ButtonStyle.secondary
            button.label = "Lock Channel"
            self.cog.voice_data[str(self.channel.id)]["locked"] = False

        await self.channel.edit(overwrites=overwrites)
        self.cog.save_voice_data()
        await interaction.response.edit_message(view=self)

    @discord.ui.button(label="Set User Limit", style=discord.ButtonStyle.secondary, custom_id="set_limit")
    async def set_limit(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Set user limit for the voice channel"""
        if interaction.user.id != self.cog.voice_data[str(self.channel.id)]["owner"]:
            await interaction.response.send_message("Only the channel owner can use these controls!", ephemeral=True)
            return

        class LimitModal(discord.ui.Modal, title="Set User Limit"):
            limit = discord.ui.TextInput(
                label="User Limit (0 for unlimited)",
                placeholder="Enter a number between 0 and 99",
                required=True,
                max_length=2
            )

            async def on_submit(self, interaction: discord.Interaction):
                try:
                    limit = int(self.limit.value)
                    if 0 <= limit <= 99:
                        await self.channel.edit(user_limit=limit)
                        await interaction.response.send_message(
                            f"User limit set to {'unlimited' if limit == 0 else limit}",
                            ephemeral=True
                        )
                    else:
                        await interaction.response.send_message(
                            "Please enter a number between 0 and 99",
                            ephemeral=True
                        )
                except ValueError:
                    await interaction.response.send_message(
                        "Please enter a valid number",
                        ephemeral=True
                    )

        modal = LimitModal()
        modal.channel = self.channel
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="Rename Channel", style=discord.ButtonStyle.secondary, custom_id="rename_channel")
    async def rename_channel(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Rename the voice channel"""
        if interaction.user.id != self.cog.voice_data[str(self.channel.id)]["owner"]:
            await interaction.response.send_message("Only the channel owner can use these controls!", ephemeral=True)
            return

        class RenameModal(discord.ui.Modal, title="Rename Channel"):
            name = discord.ui.TextInput(
                label="New Channel Name",
                placeholder="Enter a new name for your channel",
                required=True,
                max_length=100
            )

            async def on_submit(self, interaction: discord.Interaction):
                try:
                    await self.channel.edit(name=self.name.value)
                    await interaction.response.send_message(
                        f"Channel renamed to {self.name.value}",
                        ephemeral=True
                    )
                except discord.Forbidden:
                    await interaction.response.send_message(
                        "I don't have permission to rename the channel",
                        ephemeral=True
                    )

        modal = RenameModal()
        modal.channel = self.channel
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="Delete Channel", style=discord.ButtonStyle.danger, custom_id="delete_channel")
    async def delete_channel(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Delete the voice channel"""
        if interaction.user.id != self.cog.voice_data[str(self.channel.id)]["owner"]:
            await interaction.response.send_message("Only the channel owner can use these controls!", ephemeral=True)
            return

        await interaction.response.send_message("Channel will be deleted...", ephemeral=True)
        
        # Get control channel ID before deleting data
        control_channel_id = self.cog.voice_data[str(self.channel.id)].get("control_channel")
        
        # Delete the control panel if it exists
        if control_channel_id:
            control_channel = interaction.guild.get_channel(control_channel_id)
            if control_channel:
                try:
                    await control_channel.delete()
                except discord.NotFound:
                    pass

        # Delete the voice channel
        try:
            await self.channel.delete()
        except discord.NotFound:
            pass  # Channel already deleted
            
        # Clean up the data
        if str(self.channel.id) in self.cog.voice_data:
            del self.cog.voice_data[str(self.channel.id)]
            self.cog.save_voice_data()

class VoiceChannels(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.voice_data_file = 'data/voice.json'
        self.load_voice_data()

    def load_voice_data(self):
        if not os.path.exists(self.voice_data_file):
            self.voice_data = {
                "hub_channel": None,
                "custom_channels": {}
            }
            self.save_voice_data()
        else:
            with open(self.voice_data_file, 'r') as f:
                self.voice_data = json.load(f)

    def save_voice_data(self):
        with open(self.voice_data_file, 'w') as f:
            json.dump(self.voice_data, f, indent=4)

    @commands.hybrid_command(name="setupvoice")
    @commands.has_any_role(1389657163252760636)
    async def setup_voice(self, ctx: commands.Context, category: discord.CategoryChannel):
        """Set up the custom voice channel system"""
        # Create the hub channel
        hub_channel = await category.create_voice_channel(
            name="➕ Create Voice Channel",
            user_limit=1,
            reason="Setting up custom voice system"
        )
        
        self.voice_data["hub_channel"] = hub_channel.id
        self.save_voice_data()
        
        await ctx.send(
            f"Custom voice channel system has been set up!\n"
            f"Users can join {hub_channel.mention} to create their own voice channel."
        )

    @commands.Cog.listener()
    async def on_voice_state_update(self, member: discord.Member, before: discord.VoiceState, after: discord.VoiceState):
        """Handle voice channel events"""
        # Skip if no change in voice state
        if before.channel == after.channel:
            return

        # Handle joining the hub channel
        if after.channel and after.channel.id == self.voice_data.get("hub_channel"):
            # Create new voice channel
            category = after.channel.category
            new_channel = await category.create_voice_channel(
                name=f"🎤 {member.display_name}'s Channel",
                reason=f"Custom voice channel for {member.display_name}"
            )
            
            # Move member to new channel
            await member.move_to(new_channel)
            
            # Store channel data
            self.voice_data[str(new_channel.id)] = {
                "owner": member.id,
                "created_at": discord.utils.utcnow().isoformat(),
                "locked": False,
                "control_channel": None
            }
            
            # Create control panel
            control_channel = await category.create_text_channel(
                name=f"voice-control-{member.name}",
                overwrites={
                    member.guild.default_role: discord.PermissionOverwrite(read_messages=False),
                    member: discord.PermissionOverwrite(read_messages=True, send_messages=False)
                }
            )
            
            # Store control channel ID
            self.voice_data[str(new_channel.id)]["control_channel"] = control_channel.id
            self.save_voice_data()
            
            embed = discord.Embed(
                title="Voice Channel Controls",
                description=(
                    f"Welcome to your voice channel controls!\n"
                    f"Channel: {new_channel.mention}\n\n"
                    f"**Features:**\n"
                    f"• Lock/Unlock Channel (blocks @everyone and all other roles from joining unless whitelisted.)\n"
                    f"• Set User Limit\n"
                    f"• Rename Channel\n"
                    f"• Delete Channel\n"
                    f"• Manage Whitelist/Blacklist\n\n"
                    f"The channel will be automatically deleted when everyone leaves."
                ),
                color=discord.Color.from_rgb(0, 0, 0)
            )
            
            await control_channel.send(
                embed=embed,
                view=VoiceChannelView(self, new_channel)
            )

        # Handle leaving a custom voice channel
        if before.channel and str(before.channel.id) in self.voice_data:
            if not before.channel.members:  # Channel is empty
                channel_id = str(before.channel.id)
                # Get control channel ID
                control_channel_id = self.voice_data[channel_id].get("control_channel")
                
                # Delete the control panel if it exists
                if control_channel_id:
                    control_channel = before.channel.guild.get_channel(control_channel_id)
                    if control_channel:
                        try:
                            await control_channel.delete()
                        except discord.NotFound:
                            pass
                
                # Delete the voice channel
                try:
                    await before.channel.delete()
                except discord.NotFound:
                    pass  # Channel already deleted
                
                # Clean up the data
                if channel_id in self.voice_data:
                    del self.voice_data[channel_id]
                    self.save_voice_data()

async def setup(bot):
    await bot.add_cog(VoiceChannels(bot)) 