2025-07-01 15:58:13,452 [INFO] Loaded cog: embedgen.py
2025-07-01 15:58:13,454 [INFO] Loaded cog: giveaway.py
2025-07-01 15:58:13,457 [INFO] Loaded cog: welcome.py
2025-07-01 15:58:13,457 [INFO] logging in using static token
2025-07-01 15:58:14,216 [INFO] Shard ID None has connected to Gateway (Session ID: d7f81fcc0790a885f00f340cc4500e65).
2025-07-01 15:58:16,228 [INFO] Logged in as The Spatha Bot
2025-07-01 15:58:16,508 [INFO] Synced 5 slash commands.
2025-07-01 15:58:50,009 [INFO] Loaded cog: embedgen.py
2025-07-01 15:58:50,012 [INFO] Loaded cog: giveaway.py
2025-07-01 15:58:50,013 [INFO] Loaded cog: welcome.py
2025-07-01 15:58:50,016 [INFO] logging in using static token
2025-07-01 15:58:50,683 [INFO] Shard ID None has connected to Gateway (Session ID: 2f645dfddbb9896375ae0fa3dcff878a).
2025-07-01 15:58:52,693 [INFO] Logged in as The Spatha Bot
2025-07-01 15:58:52,932 [INFO] Synced 5 slash commands.
2025-07-01 16:01:55,260 [INFO] Loaded cog: embedgen.py
2025-07-01 16:01:55,263 [INFO] Loaded cog: giveaway.py
2025-07-01 16:01:55,264 [INFO] Loaded cog: welcome.py
2025-07-01 16:01:55,264 [INFO] logging in using static token
2025-07-01 16:01:55,979 [INFO] Shard ID None has connected to Gateway (Session ID: 8337f3ea9b135d17e5b4b85f9c8665de).
2025-07-01 16:01:57,993 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:01:58,254 [INFO] Synced 5 slash commands.
2025-07-01 16:03:35,783 [ERROR] Error in embedcreate command: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 274, in embedcreate
    view.preview_message = await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 221, in request
    raise HTTPException(response, data)
discord.errors.HTTPException: 400 Bad Request (error code: 50035): Invalid Form Body
In data.embeds.0.description: This field is required

2025-07-01 16:03:58,854 [INFO] Loaded cog: embedgen.py
2025-07-01 16:03:58,856 [INFO] Loaded cog: giveaway.py
2025-07-01 16:03:58,858 [INFO] Loaded cog: welcome.py
2025-07-01 16:03:58,858 [INFO] logging in using static token
2025-07-01 16:03:59,685 [INFO] Shard ID None has connected to Gateway (Session ID: 8e9e1ee57827ad4b8dac61ec878619d3).
2025-07-01 16:04:01,686 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:04:02,168 [INFO] Synced 5 slash commands.
2025-07-01 16:04:27,214 [ERROR] Error updating preview: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 209, in update_preview
    self.preview_message = await interaction.followup.send(embed=embed, view=self, ephemeral=True)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 1805, in send
    data = await adapter.execute_webhook(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10015): Unknown Webhook

2025-07-01 16:04:27,310 [ERROR] Error in modal submission: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 209, in update_preview
    self.preview_message = await interaction.followup.send(embed=embed, view=self, ephemeral=True)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 1805, in send
    data = await adapter.execute_webhook(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10015): Unknown Webhook

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 234, in on_submit
    await self.parent_view.update_preview(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 212, in update_preview
    await interaction.followup.send("Failed to update preview. Please try again.", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 1805, in send
    data = await adapter.execute_webhook(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10015): Unknown Webhook

2025-07-01 16:04:27,387 [ERROR] Ignoring exception in modal <EmbedEditModal timeout=None children=1>:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 209, in update_preview
    self.preview_message = await interaction.followup.send(embed=embed, view=self, ephemeral=True)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 1805, in send
    data = await adapter.execute_webhook(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10015): Unknown Webhook

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 234, in on_submit
    await self.parent_view.update_preview(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 212, in update_preview
    await interaction.followup.send("Failed to update preview. Please try again.", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 1805, in send
    data = await adapter.execute_webhook(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10015): Unknown Webhook

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\modal.py", line 188, in _scheduled_task
    await self.on_submit(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 238, in on_submit
    await interaction.response.send_message("Failed to update the embed. Please try again.", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-01 16:05:24,566 [INFO] Loaded cog: embedgen.py
2025-07-01 16:05:24,569 [INFO] Loaded cog: giveaway.py
2025-07-01 16:05:24,570 [INFO] Loaded cog: welcome.py
2025-07-01 16:05:24,570 [INFO] logging in using static token
2025-07-01 16:05:25,260 [INFO] Shard ID None has connected to Gateway (Session ID: 2dc801bee34e3213c2877d672f6d852b).
2025-07-01 16:05:27,266 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:05:27,585 [INFO] Synced 5 slash commands.
2025-07-01 16:09:53,821 [INFO] Loaded cog: embedgen.py
2025-07-01 16:09:53,824 [INFO] Loaded cog: giveaway.py
2025-07-01 16:09:53,824 [INFO] Loaded cog: welcome.py
2025-07-01 16:09:53,824 [INFO] logging in using static token
2025-07-01 16:09:54,694 [INFO] Shard ID None has connected to Gateway (Session ID: 2d54a51cc997412fda9f4115eca10473).
2025-07-01 16:09:56,705 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:09:57,068 [INFO] Synced 5 slash commands.
2025-07-01 16:16:37,844 [INFO] Loaded cog: embedgen.py
2025-07-01 16:16:37,846 [INFO] Loaded cog: giveaway.py
2025-07-01 16:16:37,847 [INFO] Loaded cog: welcome.py
2025-07-01 16:16:37,847 [INFO] logging in using static token
2025-07-01 16:16:38,620 [INFO] Shard ID None has connected to Gateway (Session ID: 75dde3184ee0379baccb9d06aaba51af).
2025-07-01 16:16:40,622 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:16:41,066 [INFO] Synced 5 slash commands.
2025-07-01 16:19:23,977 [ERROR] Error in remove_field callback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 207, in remove_callback
    await select_interaction.response.send_message(f"Removed field: {removed['name']}", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 774, in send_message
    raise InteractionResponded(self._parent)
discord.errors.InteractionResponded: This interaction has already been responded to before

2025-07-01 16:19:23,978 [ERROR] Ignoring exception in view <View timeout=180.0 children=1> for item <Select placeholder='Select field to remove' min_values=1 max_values=1 disabled=False options=[<SelectOption label='1: Field 1' value='0' description=None emoji=None default=False>, <SelectOption label='2: Field 2' value='1' description=None emoji=None default=False>]>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 207, in remove_callback
    await select_interaction.response.send_message(f"Removed field: {removed['name']}", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 774, in send_message
    raise InteractionResponded(self._parent)
discord.errors.InteractionResponded: This interaction has already been responded to before

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 210, in remove_callback
    await select_interaction.response.send_message("Failed to remove field. Please try again.", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 774, in send_message
    raise InteractionResponded(self._parent)
discord.errors.InteractionResponded: This interaction has already been responded to before
2025-07-01 16:21:40,260 [INFO] Loaded cog: embedgen.py
2025-07-01 16:21:40,263 [INFO] Loaded cog: giveaway.py
2025-07-01 16:21:40,263 [INFO] Loaded cog: welcome.py
2025-07-01 16:21:40,263 [INFO] logging in using static token
2025-07-01 16:21:41,037 [INFO] Shard ID None has connected to Gateway (Session ID: 0d51739a819a87ca0627bbbe24f0fd9a).
2025-07-01 16:21:43,048 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:21:43,372 [INFO] Synced 5 slash commands.
2025-07-01 16:22:29,479 [INFO] Loaded cog: embedgen.py
2025-07-01 16:22:29,480 [INFO] Loaded cog: giveaway.py
2025-07-01 16:22:29,482 [INFO] Loaded cog: welcome.py
2025-07-01 16:22:29,482 [INFO] logging in using static token
2025-07-01 16:22:30,189 [INFO] Shard ID None has connected to Gateway (Session ID: 010c5d379fe83e21ac44d68cd9386c26).
2025-07-01 16:22:32,191 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:22:32,525 [INFO] Synced 5 slash commands.
2025-07-01 16:39:30,495 [ERROR] Error in edit_description: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] The specified network name is no longer available

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\windows_events.py", line 846, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\windows_events.py", line 498, in finish_recv
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 141, in edit_description
    await interaction.response.send_modal(EmbedEditModal(self, 'description', 'Description', self.embed_data['description']))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 972, in send_modal
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 177, in request
    async with session.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\client.py", line 1425, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\client.py", line 730, in _request
    await resp.start(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\client_reqrep.py", line 1059, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\aiohttp\streams.py", line 671, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [WinError 64] The specified network name is no longer available

2025-07-01 16:39:30,709 [INFO] Shard ID None has successfully RESUMED session 010c5d379fe83e21ac44d68cd9386c26.
2025-07-01 16:40:12,128 [INFO] Shard ID None has successfully RESUMED session 010c5d379fe83e21ac44d68cd9386c26.
2025-07-01 16:41:41,665 [INFO] Loaded cog: embedgen.py
2025-07-01 16:41:41,666 [INFO] Loaded cog: giveaway.py
2025-07-01 16:41:41,671 [INFO] Loaded cog: rolerequest.py
2025-07-01 16:41:41,673 [INFO] Loaded cog: welcome.py
2025-07-01 16:41:41,673 [INFO] logging in using static token
2025-07-01 16:41:42,358 [INFO] Shard ID None has connected to Gateway (Session ID: ea5935abdbac073d02040847a8f283f8).
2025-07-01 16:41:44,373 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:41:44,649 [INFO] Synced 5 slash commands.
2025-07-01 16:42:27,323 [INFO] Loaded cog: embedgen.py
2025-07-01 16:42:27,325 [INFO] Loaded cog: giveaway.py
2025-07-01 16:42:27,327 [INFO] Loaded cog: rolerequest.py
2025-07-01 16:42:27,328 [INFO] Loaded cog: welcome.py
2025-07-01 16:42:27,328 [INFO] logging in using static token
2025-07-01 16:42:28,212 [INFO] Shard ID None has connected to Gateway (Session ID: 5827fd399a71ea3bc4efb9c4ba2ad3c5).
2025-07-01 16:42:30,220 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:42:30,425 [INFO] Synced 5 slash commands.
2025-07-01 16:45:37,171 [ERROR] Ignoring exception in modal <CloseModal timeout=None children=1>:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\modal.py", line 188, in _scheduled_task
    await self.on_submit(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\rolerequest.py", line 126, in on_submit
    if self.claimed_by:
       ^^^^^^^^^^^^^^^
AttributeError: 'CloseModal' object has no attribute 'claimed_by'
2025-07-01 16:46:58,243 [INFO] Loaded cog: embedgen.py
2025-07-01 16:46:58,246 [INFO] Loaded cog: giveaway.py
2025-07-01 16:46:58,251 [INFO] Loaded cog: rolerequest.py
2025-07-01 16:46:58,253 [INFO] Loaded cog: welcome.py
2025-07-01 16:46:58,254 [INFO] logging in using static token
2025-07-01 16:46:59,133 [INFO] Shard ID None has connected to Gateway (Session ID: cd117b291d603979e18dafffa2d4c287).
2025-07-01 16:47:01,193 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:47:01,443 [INFO] Synced 5 slash commands.
2025-07-01 16:47:56,770 [ERROR] Ignoring exception in modal <CloseModal timeout=None children=1>:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\modal.py", line 188, in _scheduled_task
    await self.on_submit(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\rolerequest.py", line 123, in on_submit
    parent_view = self.view
                  ^^^^^^^^^
AttributeError: 'CloseModal' object has no attribute 'view'
2025-07-01 16:49:28,099 [INFO] Loaded cog: embedgen.py
2025-07-01 16:49:28,102 [INFO] Loaded cog: giveaway.py
2025-07-01 16:49:28,109 [INFO] Loaded cog: rolerequest.py
2025-07-01 16:49:28,110 [INFO] Loaded cog: welcome.py
2025-07-01 16:49:28,111 [INFO] logging in using static token
2025-07-01 16:49:28,989 [INFO] Shard ID None has connected to Gateway (Session ID: a53cd7fb9fd3b5835b5c8a379c6aaaeb).
2025-07-01 16:49:30,996 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:49:31,253 [INFO] Synced 5 slash commands.
2025-07-01 16:51:46,517 [INFO] Loaded cog: embedgen.py
2025-07-01 16:51:46,521 [INFO] Loaded cog: giveaway.py
2025-07-01 16:51:46,527 [INFO] Loaded cog: rolerequest.py
2025-07-01 16:51:46,527 [INFO] Loaded cog: welcome.py
2025-07-01 16:51:46,528 [INFO] logging in using static token
2025-07-01 16:51:47,403 [INFO] Shard ID None has connected to Gateway (Session ID: 9ae4bc448263416517658cf17bd104cd).
2025-07-01 16:51:49,410 [INFO] Logged in as Aegis Nox Bot
2025-07-01 16:51:50,063 [INFO] Synced 5 slash commands.
2025-07-01 17:00:44,738 [INFO] Loaded cog: embedgen.py
2025-07-01 17:00:44,740 [INFO] Loaded cog: giveaway.py
2025-07-01 17:00:44,749 [INFO] Loaded cog: rolerequest.py
2025-07-01 17:00:44,749 [INFO] Loaded cog: welcome.py
2025-07-01 17:00:44,749 [INFO] logging in using static token
2025-07-01 17:00:45,424 [INFO] Shard ID None has connected to Gateway (Session ID: de8b74ed76da1b70f4ea0db885d7f9c4).
2025-07-01 17:00:47,431 [INFO] Logged in as Aegis Nox Bot
2025-07-01 17:00:47,810 [INFO] Synced 5 slash commands.
2025-07-01 17:15:24,593 [INFO] Loaded cog: embedgen.py
2025-07-01 17:15:24,596 [INFO] Loaded cog: giveaway.py
2025-07-01 17:15:24,605 [INFO] Loaded cog: rolerequest.py
2025-07-01 17:15:24,606 [INFO] Loaded cog: welcome.py
2025-07-01 17:15:24,606 [INFO] logging in using static token
2025-07-01 17:15:25,448 [INFO] Shard ID None has connected to Gateway (Session ID: 9f16cc652c57e849fb86bb7eb93931a3).
2025-07-01 17:15:27,455 [INFO] Logged in as Aegis Nox Bot
2025-07-01 17:15:27,662 [INFO] Synced 5 slash commands.
2025-07-01 17:44:57,028 [INFO] Loaded cog: applytoaegis.py
2025-07-01 17:44:57,029 [INFO] Loaded cog: embedgen.py
2025-07-01 17:44:57,032 [INFO] Loaded cog: giveaway.py
2025-07-01 17:44:57,041 [INFO] Loaded cog: rolerequest.py
2025-07-01 17:44:57,041 [INFO] Loaded cog: welcome.py
2025-07-01 17:44:57,041 [INFO] logging in using static token
2025-07-01 17:44:57,778 [INFO] Shard ID None has connected to Gateway (Session ID: a83eca523ffb68d7e06e57afb5b02048).
2025-07-01 17:44:59,794 [INFO] Logged in as Aegis Nox Bot
2025-07-01 17:45:00,160 [INFO] Synced 5 slash commands.
2025-07-01 17:47:26,513 [INFO] Loaded cog: applytoaegis.py
2025-07-01 17:47:26,515 [INFO] Loaded cog: embedgen.py
2025-07-01 17:47:26,518 [INFO] Loaded cog: giveaway.py
2025-07-01 17:47:26,521 [INFO] Loaded cog: rolerequest.py
2025-07-01 17:47:26,522 [INFO] Loaded cog: welcome.py
2025-07-01 17:47:26,522 [INFO] logging in using static token
2025-07-01 17:47:28,792 [INFO] Shard ID None has connected to Gateway (Session ID: f3adfe2f735f19886fb3592d115bba8b).
2025-07-01 17:47:30,795 [INFO] Logged in as Aegis Nox Bot
2025-07-01 17:47:30,997 [INFO] Synced 5 slash commands.
2025-07-01 17:48:54,447 [INFO] Loaded cog: applytoaegis.py
2025-07-01 17:48:54,449 [INFO] Loaded cog: embedgen.py
2025-07-01 17:48:54,452 [INFO] Loaded cog: giveaway.py
2025-07-01 17:48:54,455 [INFO] Loaded cog: rolerequest.py
2025-07-01 17:48:54,455 [INFO] Loaded cog: welcome.py
2025-07-01 17:48:54,455 [INFO] logging in using static token
2025-07-01 17:48:55,384 [INFO] Shard ID None has connected to Gateway (Session ID: d3fe2585009066e0753e340ecbbc2d55).
2025-07-01 17:48:57,418 [INFO] Logged in as Aegis Nox Bot
2025-07-01 17:48:57,762 [INFO] Synced 5 slash commands.
2025-07-01 17:53:00,148 [INFO] Loaded cog: applytoaegis.py
2025-07-01 17:53:00,150 [INFO] Loaded cog: embedgen.py
2025-07-01 17:53:00,152 [INFO] Loaded cog: giveaway.py
2025-07-01 17:53:00,154 [INFO] Loaded cog: rolerequest.py
2025-07-01 17:53:00,154 [INFO] Loaded cog: welcome.py
2025-07-01 17:53:00,154 [INFO] logging in using static token
2025-07-01 17:53:01,049 [INFO] Shard ID None has connected to Gateway (Session ID: 7cf1ee5fc20ec41e0876ded2acb0925f).
2025-07-01 17:53:03,059 [INFO] Logged in as Aegis Nox Bot
2025-07-01 17:53:03,284 [INFO] Synced 5 slash commands.
2025-07-01 18:04:08,452 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:04:08,454 [INFO] Loaded cog: embedgen.py
2025-07-01 18:04:08,456 [INFO] Loaded cog: giveaway.py
2025-07-01 18:04:08,457 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:04:08,462 [INFO] Loaded cog: rosters.py
2025-07-01 18:04:08,462 [INFO] Loaded cog: welcome.py
2025-07-01 18:04:08,462 [INFO] logging in using static token
2025-07-01 18:04:09,399 [INFO] Shard ID None has connected to Gateway (Session ID: db723a976b0403b14f6706bbdc7cafe5).
2025-07-01 18:04:11,411 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:04:12,068 [INFO] Synced 7 slash commands.
2025-07-01 18:06:58,701 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:06:58,702 [INFO] Loaded cog: embedgen.py
2025-07-01 18:06:58,704 [INFO] Loaded cog: giveaway.py
2025-07-01 18:06:58,705 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:06:58,712 [INFO] Loaded cog: rosters.py
2025-07-01 18:06:58,712 [INFO] Loaded cog: welcome.py
2025-07-01 18:06:58,713 [INFO] logging in using static token
2025-07-01 18:06:59,468 [INFO] Shard ID None has connected to Gateway (Session ID: 8a89264879c99c25723b03cb81168014).
2025-07-01 18:07:01,486 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:07:01,888 [INFO] Synced 7 slash commands.
2025-07-01 18:11:18,511 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:11:18,513 [INFO] Loaded cog: embedgen.py
2025-07-01 18:11:18,514 [INFO] Loaded cog: giveaway.py
2025-07-01 18:11:18,516 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:11:18,521 [INFO] Loaded cog: rosters.py
2025-07-01 18:11:18,523 [INFO] Loaded cog: welcome.py
2025-07-01 18:11:18,523 [INFO] logging in using static token
2025-07-01 18:11:19,280 [INFO] Shard ID None has connected to Gateway (Session ID: 62607e0f995612622ca7f40ac992d6b7).
2025-07-01 18:11:21,295 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:11:21,500 [INFO] Synced 7 slash commands.
2025-07-01 18:14:12,925 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:14:12,927 [INFO] Loaded cog: embedgen.py
2025-07-01 18:14:12,929 [INFO] Loaded cog: giveaway.py
2025-07-01 18:14:12,931 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:14:12,938 [INFO] Loaded cog: rosters.py
2025-07-01 18:14:12,939 [INFO] Loaded cog: welcome.py
2025-07-01 18:14:12,939 [INFO] logging in using static token
2025-07-01 18:14:13,663 [INFO] Shard ID None has connected to Gateway (Session ID: 5175c5eab9037056259d23c4f7680d07).
2025-07-01 18:14:15,683 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:14:16,079 [INFO] Synced 7 slash commands.
2025-07-01 18:16:59,267 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:16:59,267 [INFO] Loaded cog: embedgen.py
2025-07-01 18:16:59,270 [INFO] Loaded cog: giveaway.py
2025-07-01 18:16:59,270 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:16:59,277 [INFO] Loaded cog: rosters.py
2025-07-01 18:16:59,279 [INFO] Loaded cog: welcome.py
2025-07-01 18:16:59,279 [INFO] logging in using static token
2025-07-01 18:17:00,263 [INFO] Shard ID None has connected to Gateway (Session ID: 8813d5055402d690f2e0e39e86e6996a).
2025-07-01 18:17:02,290 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:17:02,489 [INFO] Synced 7 slash commands.
2025-07-01 18:17:53,235 [ERROR] Ignoring exception in modal <EditFieldModal timeout=None children=1>:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\modal.py", line 188, in _scheduled_task
    await self.on_submit(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\rosters.py", line 125, in on_submit
    roster_id = int(interaction.message.embeds[0].footer.text.split('#')[1])
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^
IndexError: list index out of range
2025-07-01 18:18:35,173 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:18:35,175 [INFO] Loaded cog: embedgen.py
2025-07-01 18:18:35,177 [INFO] Loaded cog: giveaway.py
2025-07-01 18:18:35,178 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:18:35,185 [INFO] Loaded cog: rosters.py
2025-07-01 18:18:35,185 [INFO] Loaded cog: welcome.py
2025-07-01 18:18:35,185 [INFO] logging in using static token
2025-07-01 18:18:35,990 [INFO] Shard ID None has connected to Gateway (Session ID: cafab12c735d4750f6beba7c5a4718b2).
2025-07-01 18:18:38,018 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:18:38,344 [INFO] Synced 7 slash commands.
2025-07-01 18:21:04,423 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:21:04,425 [INFO] Loaded cog: embedgen.py
2025-07-01 18:21:04,428 [INFO] Loaded cog: giveaway.py
2025-07-01 18:21:04,429 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:21:04,436 [INFO] Loaded cog: rosters.py
2025-07-01 18:21:04,437 [INFO] Loaded cog: welcome.py
2025-07-01 18:21:04,437 [INFO] logging in using static token
2025-07-01 18:21:05,351 [INFO] Shard ID None has connected to Gateway (Session ID: 3db2f428da1d3af69e645e5e2badeb0f).
2025-07-01 18:21:07,363 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:21:07,579 [INFO] Synced 7 slash commands.
2025-07-01 18:23:33,338 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:23:33,341 [INFO] Loaded cog: embedgen.py
2025-07-01 18:23:33,342 [INFO] Loaded cog: giveaway.py
2025-07-01 18:23:33,344 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:23:33,351 [INFO] Loaded cog: rosters.py
2025-07-01 18:23:33,351 [INFO] Loaded cog: welcome.py
2025-07-01 18:23:33,351 [INFO] logging in using static token
2025-07-01 18:23:34,151 [INFO] Shard ID None has connected to Gateway (Session ID: 34735b443e7e4f7fbfdd98344b9873aa).
2025-07-01 18:23:36,153 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:23:36,451 [INFO] Synced 7 slash commands.
2025-07-01 18:26:15,614 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:26:15,616 [INFO] Loaded cog: embedgen.py
2025-07-01 18:26:15,619 [INFO] Loaded cog: giveaway.py
2025-07-01 18:26:15,619 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:26:15,628 [INFO] Loaded cog: rosters.py
2025-07-01 18:26:15,628 [INFO] Loaded cog: welcome.py
2025-07-01 18:26:15,630 [INFO] logging in using static token
2025-07-01 18:26:16,506 [INFO] Shard ID None has connected to Gateway (Session ID: 52de7f702ac3336f5cf02b9eedcc754e).
2025-07-01 18:26:18,546 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:26:19,094 [INFO] Synced 7 slash commands.
2025-07-01 18:27:36,761 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:27:36,764 [INFO] Loaded cog: embedgen.py
2025-07-01 18:27:36,766 [INFO] Loaded cog: giveaway.py
2025-07-01 18:27:36,768 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:27:36,774 [INFO] Loaded cog: rosters.py
2025-07-01 18:27:36,776 [INFO] Loaded cog: welcome.py
2025-07-01 18:27:36,776 [INFO] logging in using static token
2025-07-01 18:27:37,742 [INFO] Shard ID None has connected to Gateway (Session ID: 88ec3e6793d5f581506750337265c5b2).
2025-07-01 18:27:39,763 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:27:40,069 [INFO] Synced 7 slash commands.
2025-07-01 18:29:16,047 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:29:16,049 [INFO] Loaded cog: embedgen.py
2025-07-01 18:29:16,051 [INFO] Loaded cog: giveaway.py
2025-07-01 18:29:16,052 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:29:16,061 [INFO] Loaded cog: rosters.py
2025-07-01 18:29:16,063 [INFO] Loaded cog: welcome.py
2025-07-01 18:29:16,063 [INFO] logging in using static token
2025-07-01 18:29:17,304 [INFO] Shard ID None has connected to Gateway (Session ID: 49f9dbb94656c4d0f2f802885cddef3b).
2025-07-01 18:29:19,320 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:29:21,547 [INFO] Synced 7 slash commands.
2025-07-01 18:32:00,995 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:32:00,998 [INFO] Loaded cog: embedgen.py
2025-07-01 18:32:01,000 [INFO] Loaded cog: giveaway.py
2025-07-01 18:32:01,001 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:32:01,010 [INFO] Loaded cog: rosters.py
2025-07-01 18:32:01,011 [INFO] Loaded cog: welcome.py
2025-07-01 18:32:01,011 [INFO] logging in using static token
2025-07-01 18:32:01,781 [INFO] Shard ID None has connected to Gateway (Session ID: 1c4b06d0b828157725b89d352a23df89).
2025-07-01 18:32:03,784 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:32:04,098 [INFO] Synced 7 slash commands.
2025-07-01 18:33:34,332 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:33:34,334 [INFO] Loaded cog: embedgen.py
2025-07-01 18:33:34,337 [INFO] Loaded cog: giveaway.py
2025-07-01 18:33:34,338 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:33:34,347 [INFO] Loaded cog: rosters.py
2025-07-01 18:33:34,347 [INFO] Loaded cog: welcome.py
2025-07-01 18:33:34,347 [INFO] logging in using static token
2025-07-01 18:33:35,007 [INFO] Shard ID None has connected to Gateway (Session ID: ea43f4168906c9afebb346440ebb6a71).
2025-07-01 18:33:37,016 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:33:37,236 [INFO] Synced 7 slash commands.
2025-07-01 18:37:35,091 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:37:35,093 [INFO] Loaded cog: embedgen.py
2025-07-01 18:37:35,096 [INFO] Loaded cog: giveaway.py
2025-07-01 18:37:35,096 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:37:35,104 [INFO] Loaded cog: rosters.py
2025-07-01 18:37:35,104 [INFO] Loaded cog: welcome.py
2025-07-01 18:37:35,104 [INFO] logging in using static token
2025-07-01 18:37:35,884 [INFO] Shard ID None has connected to Gateway (Session ID: d621cda29979bcbeb88275b3b306e1e9).
2025-07-01 18:37:37,891 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:37:38,153 [INFO] Synced 7 slash commands.
2025-07-01 18:39:07,945 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:39:07,946 [INFO] Loaded cog: embedgen.py
2025-07-01 18:39:07,948 [INFO] Loaded cog: giveaway.py
2025-07-01 18:39:07,950 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:39:07,952 [INFO] Loaded cog: rosters.py
2025-07-01 18:39:07,952 [INFO] Loaded cog: welcome.py
2025-07-01 18:39:07,952 [INFO] logging in using static token
2025-07-01 18:39:08,638 [INFO] Shard ID None has connected to Gateway (Session ID: 2d39f6580d6342727125d2fdc1225def).
2025-07-01 18:39:10,646 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:39:10,866 [INFO] Synced 7 slash commands.
2025-07-01 18:41:04,003 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:41:04,004 [INFO] Loaded cog: embedgen.py
2025-07-01 18:41:04,007 [INFO] Loaded cog: giveaway.py
2025-07-01 18:41:04,009 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:41:04,018 [INFO] Loaded cog: rosters.py
2025-07-01 18:41:04,019 [INFO] Loaded cog: welcome.py
2025-07-01 18:41:04,019 [INFO] logging in using static token
2025-07-01 18:41:04,772 [INFO] Shard ID None has connected to Gateway (Session ID: 864da126057001e75158d02c6dbf30a2).
2025-07-01 18:41:06,777 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:41:07,050 [INFO] Synced 7 slash commands.
2025-07-01 18:45:07,616 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:45:07,619 [INFO] Loaded cog: embedgen.py
2025-07-01 18:45:07,622 [INFO] Loaded cog: fleetdata.py
2025-07-01 18:45:07,625 [INFO] Loaded cog: giveaway.py
2025-07-01 18:45:07,629 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:45:07,630 [INFO] Loaded cog: rosters.py
2025-07-01 18:45:07,631 [INFO] Loaded cog: welcome.py
2025-07-01 18:45:07,631 [INFO] logging in using static token
2025-07-01 18:45:08,384 [INFO] Shard ID None has connected to Gateway (Session ID: ef4f2a8409f0d0c1c44418a3d442e4b5).
2025-07-01 18:45:10,409 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:45:10,728 [INFO] Synced 7 slash commands.
2025-07-01 18:55:28,998 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:55:29,004 [INFO] Loaded cog: embedgen.py
2025-07-01 18:55:29,006 [INFO] Loaded cog: fleetdata.py
2025-07-01 18:55:29,010 [INFO] Loaded cog: giveaway.py
2025-07-01 18:55:29,012 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:55:29,013 [INFO] Loaded cog: rosters.py
2025-07-01 18:55:29,013 [INFO] Loaded cog: welcome.py
2025-07-01 18:55:29,014 [INFO] logging in using static token
2025-07-01 18:55:29,705 [INFO] Shard ID None has connected to Gateway (Session ID: 53816a0dad87498ebbfd130893453d99).
2025-07-01 18:55:31,708 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:55:31,925 [INFO] Synced 7 slash commands.
2025-07-01 18:55:37,641 [INFO] Shard ID None has successfully RESUMED session 53816a0dad87498ebbfd130893453d99.
2025-07-01 18:59:01,998 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:59:02,000 [INFO] Loaded cog: embedgen.py
2025-07-01 18:59:02,005 [INFO] Loaded cog: fleetdata.py
2025-07-01 18:59:02,007 [INFO] Loaded cog: giveaway.py
2025-07-01 18:59:02,009 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:59:02,010 [INFO] Loaded cog: rosters.py
2025-07-01 18:59:02,010 [INFO] Loaded cog: welcome.py
2025-07-01 18:59:02,012 [INFO] logging in using static token
2025-07-01 18:59:02,905 [INFO] Shard ID None has connected to Gateway (Session ID: c9b2d08edc1eab7a509d14e05c40190b).
2025-07-01 18:59:04,906 [INFO] Logged in as Aegis Nox Bot
2025-07-01 18:59:05,301 [INFO] Synced 7 slash commands.
2025-07-01 18:59:21,486 [INFO] Loaded cog: applytoaegis.py
2025-07-01 18:59:21,488 [INFO] Loaded cog: embedgen.py
2025-07-01 18:59:21,489 [INFO] Loaded cog: fleetdata.py
2025-07-01 18:59:21,492 [INFO] Loaded cog: giveaway.py
2025-07-01 18:59:21,494 [INFO] Loaded cog: rolerequest.py
2025-07-01 18:59:21,494 [INFO] Loaded cog: rosters.py
2025-07-01 18:59:21,494 [INFO] Loaded cog: welcome.py
2025-07-01 18:59:21,494 [INFO] logging in using static token
2025-07-01 18:59:22,198 [INFO] Shard ID None has connected to Gateway (Session ID: 7aa6e63a28a2c5e34c4848a0bf0833ef).
2025-07-01 18:59:24,221 [INFO] Logged in as Aegis Nox Bot
2025-07-01 19:00:05,202 [INFO] Synced 7 slash commands.
2025-07-01 19:21:36,663 [ERROR] Error in save_button: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 338, in save_button
    await interaction.response.send_message("Could not find the original message. It might have been deleted or I don't have access to it.", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

2025-07-01 19:21:36,745 [ERROR] Error in view EmbedSetupView: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 338, in save_button
    await interaction.response.send_message("Could not find the original message. It might have been deleted or I don't have access to it.", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\embedgen.py", line 364, in save_button
    await interaction.response.send_message("An error occurred while saving the embed. Please try again.", ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

2025-07-01 21:42:40,809 [INFO] Shard ID None has successfully RESUMED session 7aa6e63a28a2c5e34c4848a0bf0833ef.
2025-07-01 21:59:51,976 [INFO] Loaded cog: applytoaegis.py
2025-07-01 21:59:51,977 [INFO] Loaded cog: embedgen.py
2025-07-01 21:59:51,979 [INFO] Loaded cog: fleetdata.py
2025-07-01 21:59:51,982 [INFO] Loaded cog: giveaway.py
2025-07-01 21:59:51,984 [INFO] Loaded cog: rolerequest.py
2025-07-01 21:59:51,994 [INFO] Loaded cog: rosters.py
2025-07-01 21:59:51,995 [INFO] Loaded cog: welcome.py
2025-07-01 21:59:51,995 [INFO] logging in using static token
2025-07-01 21:59:52,724 [INFO] Shard ID None has connected to Gateway (Session ID: 6961870bb5076a08e62a8558edec13f1).
2025-07-01 21:59:54,740 [INFO] Logged in as Aegis Nox Bot
2025-07-01 21:59:54,913 [INFO] Synced 7 slash commands.
2025-07-01 22:00:48,673 [INFO] Loaded cog: applytoaegis.py
2025-07-01 22:00:48,675 [INFO] Loaded cog: embedgen.py
2025-07-01 22:00:48,676 [INFO] Loaded cog: fleetdata.py
2025-07-01 22:00:48,679 [INFO] Loaded cog: giveaway.py
2025-07-01 22:00:48,681 [INFO] Loaded cog: rolerequest.py
2025-07-01 22:00:48,682 [INFO] Loaded cog: rosters.py
2025-07-01 22:00:48,683 [INFO] Loaded cog: welcome.py
2025-07-01 22:00:48,683 [INFO] logging in using static token
2025-07-01 22:00:49,412 [INFO] Shard ID None has connected to Gateway (Session ID: b826481e50feacd584c8bc3e6cfd7000).
2025-07-01 22:00:51,440 [INFO] Logged in as Aegis Nox Bot
2025-07-01 22:00:51,857 [INFO] Synced 7 slash commands.
2025-07-01 22:10:35,573 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-01 23:59:26,831 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 01:28:59,715 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 02:44:38,371 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 02:45:19,752 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 03:22:47,307 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 07:04:47,085 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 08:36:17,711 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 09:46:52,190 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 10:46:45,942 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 11:35:26,104 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
2025-07-02 12:23:07,047 [INFO] Shard ID None has successfully RESUMED session b826481e50feacd584c8bc3e6cfd7000.
