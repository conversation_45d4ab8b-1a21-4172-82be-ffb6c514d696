2025-07-02 12:50:31,805 [INFO] Loaded cog: applytoaegis.py
2025-07-02 12:50:31,807 [INFO] Loaded cog: embedgen.py
2025-07-02 12:50:31,809 [INFO] Loaded cog: fleetdata.py
2025-07-02 12:50:31,812 [INFO] Loaded cog: giveaway.py
2025-07-02 12:50:31,822 [INFO] Loaded cog: moderation.py
2025-07-02 12:50:31,823 [INFO] Loaded cog: rolerequest.py
2025-07-02 12:50:31,832 [INFO] Loaded cog: rosters.py
2025-07-02 12:50:31,833 [INFO] Loaded cog: welcome.py
2025-07-02 12:50:31,833 [INFO] logging in using static token
2025-07-02 12:50:32,557 [INFO] Shard ID None has connected to Gateway (Session ID: aa2ec9e09b6ad331933f011ce310751a).
2025-07-02 12:50:34,577 [INFO] Logged in as Aegis Nox <PERSON>
2025-07-02 12:50:34,873 [INFO] Synced 17 slash commands.
2025-07-02 12:52:05,869 [ERROR] Ignoring exception in command 'promote'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\rosters.py", line 934, in promote
    await interaction.response.send_message(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'promote' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-02 12:56:00,886 [INFO] Loaded cog: applytoaegis.py
2025-07-02 12:56:00,887 [INFO] Loaded cog: embedgen.py
2025-07-02 12:56:00,890 [INFO] Loaded cog: fleetdata.py
2025-07-02 12:56:00,891 [INFO] Loaded cog: giveaway.py
2025-07-02 12:56:00,896 [INFO] Loaded cog: moderation.py
2025-07-02 12:56:00,897 [INFO] Loaded cog: rolerequest.py
2025-07-02 12:56:00,908 [INFO] Loaded cog: rosters.py
2025-07-02 12:56:00,909 [INFO] Loaded cog: welcome.py
2025-07-02 12:56:00,910 [INFO] logging in using static token
2025-07-02 12:56:01,757 [INFO] Shard ID None has connected to Gateway (Session ID: 1173c5ba9800a02db3cf9549d03d768c).
2025-07-02 12:56:03,777 [INFO] Logged in as Aegis Nox Bot
2025-07-02 12:56:04,283 [INFO] Synced 17 slash commands.
2025-07-02 12:57:15,093 [INFO] Loaded cog: applytoaegis.py
2025-07-02 12:57:15,094 [INFO] Loaded cog: embedgen.py
2025-07-02 12:57:15,096 [INFO] Loaded cog: fleetdata.py
2025-07-02 12:57:15,098 [INFO] Loaded cog: giveaway.py
2025-07-02 12:57:15,102 [INFO] Loaded cog: moderation.py
2025-07-02 12:57:15,104 [INFO] Loaded cog: rolerequest.py
2025-07-02 12:57:15,113 [INFO] Loaded cog: rosters.py
2025-07-02 12:57:15,114 [INFO] Loaded cog: welcome.py
2025-07-02 12:57:15,114 [INFO] logging in using static token
2025-07-02 12:57:15,774 [INFO] Shard ID None has connected to Gateway (Session ID: bfdd013b03cf631d84b95a4dc8bd445e).
2025-07-02 12:57:17,792 [INFO] Logged in as Aegis Nox Bot
2025-07-02 12:57:18,070 [INFO] Synced 17 slash commands.
2025-07-02 12:57:53,767 [ERROR] Ignoring exception in command 'demote'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\rosters.py", line 961, in demote
    if any(role.id == r_id for r_type in ROLE_HIERARCHY.values() for r_id in r_type.values()):
                                         ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'values'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'demote' raised an exception: AttributeError: 'list' object has no attribute 'values'
2025-07-02 13:02:02,620 [INFO] Loaded cog: applytoaegis.py
2025-07-02 13:02:02,623 [INFO] Loaded cog: embedgen.py
2025-07-02 13:02:02,625 [INFO] Loaded cog: fleetdata.py
2025-07-02 13:02:02,627 [INFO] Loaded cog: giveaway.py
2025-07-02 13:02:02,630 [INFO] Loaded cog: moderation.py
2025-07-02 13:02:02,631 [INFO] Loaded cog: rolerequest.py
2025-07-02 13:02:02,641 [INFO] Loaded cog: rosters.py
2025-07-02 13:02:02,641 [INFO] Loaded cog: welcome.py
2025-07-02 13:02:02,641 [INFO] logging in using static token
2025-07-02 13:02:03,512 [INFO] Shard ID None has connected to Gateway (Session ID: d11fa6b5d9d3c9f0bf438b952d44a146).
2025-07-02 13:02:05,516 [INFO] Logged in as Aegis Nox Bot
2025-07-02 13:02:05,732 [INFO] Synced 17 slash commands.
2025-07-02 13:07:09,855 [INFO] Loaded cog: applytoaegis.py
2025-07-02 13:07:09,858 [INFO] Loaded cog: embedgen.py
2025-07-02 13:07:09,867 [INFO] Loaded cog: fleetdata.py
2025-07-02 13:07:09,869 [INFO] Loaded cog: giveaway.py
2025-07-02 13:07:09,874 [INFO] Loaded cog: moderation.py
2025-07-02 13:07:09,875 [INFO] Loaded cog: rolerequest.py
2025-07-02 13:07:09,884 [INFO] Loaded cog: rosters.py
2025-07-02 13:07:09,885 [INFO] Loaded cog: welcome.py
2025-07-02 13:07:09,885 [INFO] logging in using static token
2025-07-02 13:07:10,827 [INFO] Shard ID None has connected to Gateway (Session ID: 8f0c91bca5c80ef9a498af6a711f7660).
2025-07-02 13:07:12,842 [INFO] Logged in as Aegis Nox Bot
2025-07-02 13:07:13,104 [INFO] Synced 17 slash commands.
2025-07-02 13:10:32,637 [INFO] Loaded cog: applytoaegis.py
2025-07-02 13:10:32,639 [INFO] Loaded cog: embedgen.py
2025-07-02 13:10:32,640 [INFO] Loaded cog: fleetdata.py
2025-07-02 13:10:32,643 [INFO] Loaded cog: giveaway.py
2025-07-02 13:10:32,646 [INFO] Loaded cog: moderation.py
2025-07-02 13:10:32,647 [INFO] Loaded cog: rolerequest.py
2025-07-02 13:10:32,658 [INFO] Loaded cog: rosters.py
2025-07-02 13:10:32,659 [INFO] Loaded cog: welcome.py
2025-07-02 13:10:32,659 [INFO] logging in using static token
2025-07-02 13:10:33,677 [INFO] Shard ID None has connected to Gateway (Session ID: 6177b430cd8b9612696e34b8f951d0e8).
2025-07-02 13:10:35,892 [INFO] Logged in as Aegis Nox Bot
2025-07-02 13:10:36,175 [INFO] Synced 17 slash commands.
2025-07-02 13:47:03,371 [INFO] Loaded cog: applytoaegis.py
2025-07-02 13:47:03,372 [INFO] Loaded cog: embedgen.py
2025-07-02 13:47:03,373 [INFO] Loaded cog: fleetdata.py
2025-07-02 13:47:03,375 [INFO] Loaded cog: giveaway.py
2025-07-02 13:47:03,380 [INFO] Loaded cog: moderation.py
2025-07-02 13:47:03,381 [INFO] Loaded cog: rolerequest.py
2025-07-02 13:47:03,382 [INFO] Loaded cog: rosters.py
2025-07-02 13:47:03,386 [INFO] Loaded cog: shipgame.py
2025-07-02 13:47:03,386 [INFO] Loaded cog: welcome.py
2025-07-02 13:47:03,386 [INFO] logging in using static token
2025-07-02 13:47:04,184 [INFO] Shard ID None has connected to Gateway (Session ID: 4baccfed764d88cfa2de3e5e50394126).
2025-07-02 13:47:06,186 [INFO] Logged in as Aegis Nox Bot
2025-07-02 13:47:06,583 [INFO] Synced 18 slash commands.
2025-07-02 13:53:26,948 [INFO] Loaded cog: applytoaegis.py
2025-07-02 13:53:26,949 [INFO] Loaded cog: embedgen.py
2025-07-02 13:53:26,951 [INFO] Loaded cog: fleetdata.py
2025-07-02 13:53:26,953 [INFO] Loaded cog: giveaway.py
2025-07-02 13:53:26,956 [INFO] Loaded cog: moderation.py
2025-07-02 13:53:26,958 [INFO] Loaded cog: rolerequest.py
2025-07-02 13:53:26,959 [INFO] Loaded cog: rosters.py
2025-07-02 13:53:26,963 [INFO] Loaded cog: shipgame.py
2025-07-02 13:53:26,963 [INFO] Loaded cog: welcome.py
2025-07-02 13:53:26,963 [INFO] logging in using static token
2025-07-02 13:53:27,654 [INFO] Shard ID None has connected to Gateway (Session ID: 6415c169aa7a4ef607766961079e20d5).
2025-07-02 13:53:29,672 [INFO] Logged in as Aegis Nox Bot
2025-07-02 13:53:30,024 [INFO] Synced 18 slash commands.
2025-07-02 14:27:04,292 [INFO] Loaded cog: applytoaegis.py
2025-07-02 14:27:04,293 [INFO] Loaded cog: embedgen.py
2025-07-02 14:27:04,295 [INFO] Loaded cog: fleetdata.py
2025-07-02 14:27:04,297 [INFO] Loaded cog: giveaway.py
2025-07-02 14:27:04,300 [INFO] Loaded cog: moderation.py
2025-07-02 14:27:04,301 [INFO] Loaded cog: rolerequest.py
2025-07-02 14:27:04,303 [INFO] Loaded cog: rosters.py
2025-07-02 14:27:04,324 [ERROR] Failed to load cog shipgame.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 935, in _load_from_module_spec
    spec.loader.exec_module(lib)  # type: ignore
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\shipgame.py", line 163
    title=f"{'Your' if is_self else user.name + '\'s'} Discord Ship Fleet",
                                                                          ^
SyntaxError: f-string expression part cannot include a backslash

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TheSpathaBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 938, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.shipgame' raised an error: SyntaxError: f-string expression part cannot include a backslash (shipgame.py, line 163)

2025-07-02 14:27:04,324 [INFO] Loaded cog: welcome.py
2025-07-02 14:27:04,325 [INFO] logging in using static token
2025-07-02 14:27:05,477 [INFO] Shard ID None has connected to Gateway (Session ID: d35e1481300faf3a80fdcf4726275669).
2025-07-02 14:27:07,487 [INFO] Logged in as Aegis Nox Bot
2025-07-02 14:27:07,712 [INFO] Synced 17 slash commands.
2025-07-02 14:28:32,509 [INFO] Loaded cog: applytoaegis.py
2025-07-02 14:28:32,510 [INFO] Loaded cog: embedgen.py
2025-07-02 14:28:32,512 [INFO] Loaded cog: fleetdata.py
2025-07-02 14:28:32,514 [INFO] Loaded cog: giveaway.py
2025-07-02 14:28:32,517 [INFO] Loaded cog: moderation.py
2025-07-02 14:28:32,518 [INFO] Loaded cog: rolerequest.py
2025-07-02 14:28:32,520 [INFO] Loaded cog: rosters.py
2025-07-02 14:28:32,525 [INFO] Loaded cog: shipgame.py
2025-07-02 14:28:32,525 [INFO] Loaded cog: welcome.py
2025-07-02 14:28:32,525 [INFO] logging in using static token
2025-07-02 14:28:33,371 [INFO] Shard ID None has connected to Gateway (Session ID: bd42c93a3d6226d0b9f47511ed8f68cd).
2025-07-02 14:28:35,392 [INFO] Logged in as Aegis Nox Bot
2025-07-02 14:28:35,712 [INFO] Synced 19 slash commands.
2025-07-02 14:39:56,906 [INFO] Loaded cog: applytoaegis.py
2025-07-02 14:39:56,908 [INFO] Loaded cog: embedgen.py
2025-07-02 14:39:56,909 [INFO] Loaded cog: fleetdata.py
2025-07-02 14:39:56,911 [INFO] Loaded cog: giveaway.py
2025-07-02 14:39:56,915 [INFO] Loaded cog: moderation.py
2025-07-02 14:39:56,917 [INFO] Loaded cog: rolerequest.py
2025-07-02 14:39:56,917 [INFO] Loaded cog: rosters.py
2025-07-02 14:39:56,923 [INFO] Loaded cog: shipgame.py
2025-07-02 14:39:56,923 [INFO] Loaded cog: welcome.py
2025-07-02 14:39:56,923 [INFO] logging in using static token
2025-07-02 14:39:57,624 [INFO] Shard ID None has connected to Gateway (Session ID: 420807f679ff638faf9206ac248af3ee).
2025-07-02 14:39:59,646 [INFO] Logged in as Aegis Nox Bot
2025-07-02 14:39:59,992 [INFO] Synced 19 slash commands.
2025-07-02 14:53:38,774 [INFO] Loaded cog: applytoaegis.py
2025-07-02 14:53:38,779 [INFO] Loaded cog: embedgen.py
2025-07-02 14:53:38,801 [INFO] Loaded cog: fleetdata.py
2025-07-02 14:53:38,810 [INFO] Loaded cog: giveaway.py
2025-07-02 14:53:38,818 [INFO] Loaded cog: moderation.py
2025-07-02 14:53:38,831 [INFO] Loaded cog: rolerequest.py
2025-07-02 14:53:38,834 [INFO] Loaded cog: rosters.py
2025-07-02 14:53:38,845 [INFO] Loaded cog: shipgame.py
2025-07-02 14:53:38,847 [INFO] Loaded cog: welcome.py
2025-07-02 14:53:38,848 [INFO] logging in using static token
2025-07-02 14:53:39,621 [INFO] Shard ID None has connected to Gateway (Session ID: ebd70fc27b5248f3c5e38d1739a7148f).
2025-07-02 14:53:41,641 [INFO] Logged in as Aegis Nox Bot
2025-07-02 14:53:41,932 [INFO] Synced 19 slash commands.
2025-07-02 15:14:10,773 [INFO] Loaded cog: applytoaegis.py
2025-07-02 15:14:10,779 [INFO] Loaded cog: embedgen.py
2025-07-02 15:14:10,789 [INFO] Loaded cog: fleetdata.py
2025-07-02 15:14:10,797 [INFO] Loaded cog: giveaway.py
2025-07-02 15:14:10,814 [INFO] Loaded cog: moderation.py
2025-07-02 15:14:10,822 [INFO] Loaded cog: rolerequest.py
2025-07-02 15:14:10,827 [INFO] Loaded cog: rosters.py
2025-07-02 15:14:10,837 [INFO] Loaded cog: shipgame.py
2025-07-02 15:14:10,840 [INFO] Loaded cog: welcome.py
2025-07-02 15:14:10,841 [INFO] logging in using static token
2025-07-02 15:14:11,779 [INFO] Shard ID None has connected to Gateway (Session ID: 6ff7d2b5a88414de0c691628b0c8ceca).
2025-07-02 15:14:13,787 [INFO] Logged in as Aegis Nox Bot
2025-07-02 15:14:14,211 [INFO] Synced 19 slash commands.
2025-07-02 18:20:21,004 [INFO] Shard ID None has successfully RESUMED session 6ff7d2b5a88414de0c691628b0c8ceca.
2025-07-02 18:59:14,907 [INFO] Loaded cog: applytoaegis.py
2025-07-02 18:59:14,909 [INFO] Loaded cog: embedgen.py
2025-07-02 18:59:14,911 [INFO] Loaded cog: fleetdata.py
2025-07-02 18:59:14,913 [INFO] Loaded cog: giveaway.py
2025-07-02 18:59:14,916 [INFO] Loaded cog: moderation.py
2025-07-02 18:59:14,918 [INFO] Loaded cog: rolerequest.py
2025-07-02 18:59:14,919 [INFO] Loaded cog: rosters.py
2025-07-02 18:59:14,921 [INFO] Loaded cog: shipgame.py
2025-07-02 18:59:14,924 [INFO] Loaded cog: storagetracker.py
2025-07-02 18:59:14,925 [INFO] Loaded cog: welcome.py
2025-07-02 18:59:14,925 [INFO] logging in using static token
2025-07-02 18:59:15,805 [INFO] Shard ID None has connected to Gateway (Session ID: 2a2abd1c9198247fee6dbe64bbcb69f7).
2025-07-02 18:59:17,813 [INFO] Logged in as Aegis Nox Bot
2025-07-02 18:59:18,069 [INFO] Synced 20 slash commands.
2025-07-02 19:00:49,098 [INFO] Loaded cog: applytoaegis.py
2025-07-02 19:00:49,101 [INFO] Loaded cog: embedgen.py
2025-07-02 19:00:49,103 [INFO] Loaded cog: fleetdata.py
2025-07-02 19:00:49,105 [INFO] Loaded cog: giveaway.py
2025-07-02 19:00:49,109 [INFO] Loaded cog: moderation.py
2025-07-02 19:00:49,110 [INFO] Loaded cog: rolerequest.py
2025-07-02 19:00:49,112 [INFO] Loaded cog: rosters.py
2025-07-02 19:00:49,113 [INFO] Loaded cog: shipgame.py
2025-07-02 19:00:49,117 [INFO] Loaded cog: storagetracker.py
2025-07-02 19:00:49,118 [INFO] Loaded cog: welcome.py
2025-07-02 19:00:49,118 [INFO] logging in using static token
2025-07-02 19:00:49,906 [INFO] Shard ID None has connected to Gateway (Session ID: 143050efc4596fe6604b71ad38759b13).
2025-07-02 19:00:51,917 [INFO] Logged in as Aegis Nox Bot
2025-07-02 19:00:52,305 [INFO] Synced 20 slash commands.
2025-07-02 19:03:00,639 [INFO] Loaded cog: applytoaegis.py
2025-07-02 19:03:00,640 [INFO] Loaded cog: embedgen.py
2025-07-02 19:03:00,642 [INFO] Loaded cog: fleetdata.py
2025-07-02 19:03:00,644 [INFO] Loaded cog: giveaway.py
2025-07-02 19:03:00,648 [INFO] Loaded cog: moderation.py
2025-07-02 19:03:00,649 [INFO] Loaded cog: rolerequest.py
2025-07-02 19:03:00,651 [INFO] Loaded cog: rosters.py
2025-07-02 19:03:00,652 [INFO] Loaded cog: shipgame.py
2025-07-02 19:03:00,656 [INFO] Loaded cog: storagetracker.py
2025-07-02 19:03:00,656 [INFO] Loaded cog: welcome.py
2025-07-02 19:03:00,656 [INFO] logging in using static token
2025-07-02 19:03:01,439 [INFO] Shard ID None has connected to Gateway (Session ID: e2941e12088f54d4c1f97a8f54274111).
2025-07-02 19:03:03,448 [INFO] Logged in as Aegis Nox Bot
2025-07-02 19:03:03,847 [INFO] Synced 20 slash commands.
2025-07-02 19:03:10,026 [ERROR] Ignoring exception in command 'storage'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\storagetracker.py", line 181, in storage
    description="Track your stored items and their locations across the 'verse!" if storage_data.get(user_id, {}).get("items") else "No items in storage.",
                                                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'get'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'storage' raised an exception: AttributeError: 'list' object has no attribute 'get'
2025-07-02 19:03:18,257 [ERROR] Ignoring exception in command 'storage'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\storagetracker.py", line 181, in storage
    description="Track your stored items and their locations across the 'verse!" if storage_data.get(user_id, {}).get("items") else "No items in storage.",
                                                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'get'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'storage' raised an exception: AttributeError: 'list' object has no attribute 'get'
2025-07-02 19:04:41,125 [INFO] Loaded cog: applytoaegis.py
2025-07-02 19:04:41,127 [INFO] Loaded cog: embedgen.py
2025-07-02 19:04:41,128 [INFO] Loaded cog: fleetdata.py
2025-07-02 19:04:41,130 [INFO] Loaded cog: giveaway.py
2025-07-02 19:04:41,133 [INFO] Loaded cog: moderation.py
2025-07-02 19:04:41,134 [INFO] Loaded cog: rolerequest.py
2025-07-02 19:04:41,136 [INFO] Loaded cog: rosters.py
2025-07-02 19:04:41,137 [INFO] Loaded cog: shipgame.py
2025-07-02 19:04:41,138 [INFO] Loaded cog: storagetracker.py
2025-07-02 19:04:41,138 [INFO] Loaded cog: welcome.py
2025-07-02 19:04:41,139 [INFO] logging in using static token
2025-07-02 19:04:41,989 [INFO] Shard ID None has connected to Gateway (Session ID: c2f77ec078c1ee13860705b26d1431b0).
2025-07-02 19:04:43,989 [INFO] Logged in as Aegis Nox Bot
2025-07-02 19:04:44,277 [INFO] Synced 20 slash commands.
2025-07-02 19:09:14,705 [INFO] Loaded cog: applytoaegis.py
2025-07-02 19:09:14,706 [INFO] Loaded cog: embedgen.py
2025-07-02 19:09:14,707 [INFO] Loaded cog: fleetdata.py
2025-07-02 19:09:14,710 [INFO] Loaded cog: giveaway.py
2025-07-02 19:09:14,714 [INFO] Loaded cog: moderation.py
2025-07-02 19:09:14,715 [INFO] Loaded cog: rolerequest.py
2025-07-02 19:09:14,716 [INFO] Loaded cog: rosters.py
2025-07-02 19:09:14,717 [INFO] Loaded cog: shipgame.py
2025-07-02 19:09:14,722 [INFO] Loaded cog: storagetracker.py
2025-07-02 19:09:14,722 [INFO] Loaded cog: welcome.py
2025-07-02 19:09:14,722 [INFO] logging in using static token
2025-07-02 19:09:17,327 [INFO] Shard ID None has connected to Gateway (Session ID: 0b2b855551ff12c77e785e6953125c88).
2025-07-02 19:09:19,346 [INFO] Logged in as Aegis Nox Bot
2025-07-02 19:09:19,575 [INFO] Synced 20 slash commands.
2025-07-02 19:09:26,935 [ERROR] Ignoring exception in command 'storage'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\storagetracker.py", line 333, in storage
    await message.edit(view=view)
          ^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'edit'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'storage' raised an exception: AttributeError: 'NoneType' object has no attribute 'edit'
2025-07-02 19:10:26,751 [INFO] Loaded cog: applytoaegis.py
2025-07-02 19:10:26,752 [INFO] Loaded cog: embedgen.py
2025-07-02 19:10:26,754 [INFO] Loaded cog: fleetdata.py
2025-07-02 19:10:26,755 [INFO] Loaded cog: giveaway.py
2025-07-02 19:10:26,758 [INFO] Loaded cog: moderation.py
2025-07-02 19:10:26,760 [INFO] Loaded cog: rolerequest.py
2025-07-02 19:10:26,761 [INFO] Loaded cog: rosters.py
2025-07-02 19:10:26,762 [INFO] Loaded cog: shipgame.py
2025-07-02 19:10:26,767 [INFO] Loaded cog: storagetracker.py
2025-07-02 19:10:26,767 [INFO] Loaded cog: welcome.py
2025-07-02 19:10:26,768 [INFO] logging in using static token
2025-07-02 19:10:27,501 [INFO] Shard ID None has connected to Gateway (Session ID: e0d3b390b2adba06df36dbf591b24fe7).
2025-07-02 19:10:29,545 [INFO] Logged in as Aegis Nox Bot
2025-07-02 19:10:29,957 [INFO] Synced 20 slash commands.
2025-07-02 21:05:52,934 [INFO] Shard ID None has successfully RESUMED session e0d3b390b2adba06df36dbf591b24fe7.
2025-07-02 21:58:47,111 [INFO] Shard ID None has successfully RESUMED session e0d3b390b2adba06df36dbf591b24fe7.
2025-07-02 22:26:13,138 [ERROR] Ignoring exception in view <ShipAttackView timeout=None children=4> for item <Button style=<ButtonStyle.danger: 4> url=None disabled=False label='Shoot 1' emoji=None row=None>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\TheSpathaBot\cogs\shipgame.py", line 88, in shoot_callback
    await interaction.response.send_message(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-02 22:31:25,978 [INFO] Shard ID None has successfully RESUMED session e0d3b390b2adba06df36dbf591b24fe7.
2025-07-02 22:34:31,142 [INFO] Loaded cog: applytoaegis.py
2025-07-02 22:34:31,143 [INFO] Loaded cog: embedgen.py
2025-07-02 22:34:31,145 [INFO] Loaded cog: fleetdata.py
2025-07-02 22:34:31,147 [INFO] Loaded cog: giveaway.py
2025-07-02 22:34:31,150 [INFO] Loaded cog: moderation.py
2025-07-02 22:34:31,151 [INFO] Loaded cog: rolerequest.py
2025-07-02 22:34:31,161 [INFO] Loaded cog: rosters.py
2025-07-02 22:34:31,162 [INFO] Loaded cog: shipgame.py
2025-07-02 22:34:31,163 [INFO] Loaded cog: storagetracker.py
2025-07-02 22:34:31,164 [INFO] Loaded cog: welcome.py
2025-07-02 22:34:31,164 [INFO] logging in using static token
2025-07-02 22:34:31,885 [INFO] Shard ID None has connected to Gateway (Session ID: 00bd9324d8c09214fd48b66b9a05d0cb).
2025-07-02 22:34:33,911 [INFO] Logged in as Aegis Nox Bot
2025-07-02 22:34:34,113 [INFO] Synced 20 slash commands.
2025-07-02 22:36:04,947 [INFO] Loaded cog: applytoaegis.py
2025-07-02 22:36:04,949 [INFO] Loaded cog: embedgen.py
2025-07-02 22:36:04,951 [INFO] Loaded cog: fleetdata.py
2025-07-02 22:36:04,953 [INFO] Loaded cog: giveaway.py
2025-07-02 22:36:04,957 [INFO] Loaded cog: moderation.py
2025-07-02 22:36:04,958 [INFO] Loaded cog: rolerequest.py
2025-07-02 22:36:04,967 [INFO] Loaded cog: rosters.py
2025-07-02 22:36:04,968 [INFO] Loaded cog: shipgame.py
2025-07-02 22:36:04,969 [INFO] Loaded cog: storagetracker.py
2025-07-02 22:36:04,970 [INFO] Loaded cog: welcome.py
2025-07-02 22:36:04,970 [INFO] logging in using static token
2025-07-02 22:36:05,718 [INFO] Shard ID None has connected to Gateway (Session ID: 3f3ea614568726e22120e2ed03aeaafa).
2025-07-02 22:36:07,731 [INFO] Logged in as Aegis Nox Bot
2025-07-02 22:36:08,020 [INFO] Synced 20 slash commands.
2025-07-02 22:38:33,108 [INFO] Loaded cog: applytoaegis.py
2025-07-02 22:38:33,110 [INFO] Loaded cog: embedgen.py
2025-07-02 22:38:33,111 [INFO] Loaded cog: fleetdata.py
2025-07-02 22:38:33,114 [INFO] Loaded cog: giveaway.py
2025-07-02 22:38:33,117 [INFO] Loaded cog: moderation.py
2025-07-02 22:38:33,118 [INFO] Loaded cog: rolerequest.py
2025-07-02 22:38:33,127 [INFO] Loaded cog: rosters.py
2025-07-02 22:38:33,128 [INFO] Loaded cog: shipgame.py
2025-07-02 22:38:33,129 [INFO] Loaded cog: storagetracker.py
2025-07-02 22:38:33,130 [INFO] Loaded cog: welcome.py
2025-07-02 22:38:33,130 [INFO] logging in using static token
2025-07-02 22:38:34,456 [INFO] Shard ID None has connected to Gateway (Session ID: b66d42bd4ede4b15f1575da1f8d0d944).
2025-07-02 22:38:36,467 [INFO] Logged in as Aegis Nox Bot
2025-07-02 22:38:36,705 [INFO] Synced 20 slash commands.
2025-07-02 22:40:36,626 [INFO] Loaded cog: applytoaegis.py
2025-07-02 22:40:36,627 [INFO] Loaded cog: embedgen.py
2025-07-02 22:40:36,629 [INFO] Loaded cog: fleetdata.py
2025-07-02 22:40:36,631 [INFO] Loaded cog: giveaway.py
2025-07-02 22:40:36,635 [INFO] Loaded cog: moderation.py
2025-07-02 22:40:36,636 [INFO] Loaded cog: rolerequest.py
2025-07-02 22:40:36,647 [INFO] Loaded cog: rosters.py
2025-07-02 22:40:36,648 [INFO] Loaded cog: shipgame.py
2025-07-02 22:40:36,649 [INFO] Loaded cog: storagetracker.py
2025-07-02 22:40:36,650 [INFO] Loaded cog: welcome.py
2025-07-02 22:40:36,650 [INFO] logging in using static token
2025-07-02 22:40:37,718 [INFO] Shard ID None has connected to Gateway (Session ID: ffe4c6b22826b4858aba629b4a513b1b).
2025-07-02 22:40:39,747 [INFO] Logged in as Aegis Nox Bot
2025-07-02 22:40:40,208 [INFO] Synced 20 slash commands.
2025-07-03 00:05:24,769 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 00:12:58,806 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 00:14:21,486 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 04:06:49,963 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 04:58:23,440 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 06:26:46,874 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 06:56:09,468 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 06:56:39,674 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 07:29:54,442 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 08:34:14,512 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
2025-07-03 10:58:43,433 [INFO] Shard ID None has successfully RESUMED session ffe4c6b22826b4858aba629b4a513b1b.
