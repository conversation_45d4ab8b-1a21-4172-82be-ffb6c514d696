2025-07-11 15:09:15,436 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:09:15,438 [INFO] Loaded cog: embedgen.py
2025-07-11 15:09:15,445 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:09:15,451 [INFO] Loaded cog: giveaway.py
2025-07-11 15:09:15,456 [INFO] Loaded cog: moderation.py
2025-07-11 15:09:15,463 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:09:15,465 [INFO] Loaded cog: rosters.py
2025-07-11 15:09:15,472 [INFO] Loaded cog: shipgame.py
2025-07-11 15:09:15,473 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:09:15,479 [INFO] Loaded cog: voice.py
2025-07-11 15:09:15,481 [INFO] Loaded cog: welcome.py
2025-07-11 15:09:15,481 [INFO] logging in using static token
2025-07-11 15:09:16,255 [INFO] Shard ID None has connected to Gateway (Session ID: 8788c57c3fd17e19c0736a083bb12cb4).
2025-07-11 15:09:18,279 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:09:18,499 [INFO] Synced 21 slash commands.
2025-07-11 15:18:10,392 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:18:10,394 [INFO] Loaded cog: embedgen.py
2025-07-11 15:18:10,399 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:18:10,406 [INFO] Loaded cog: giveaway.py
2025-07-11 15:18:10,410 [INFO] Loaded cog: moderation.py
2025-07-11 15:18:10,416 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:18:10,418 [INFO] Loaded cog: rosters.py
2025-07-11 15:18:10,425 [INFO] Loaded cog: shipgame.py
2025-07-11 15:18:10,426 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:18:10,430 [INFO] Loaded cog: voice.py
2025-07-11 15:18:10,436 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:18:10,436 [INFO] Loaded cog: welcome.py
2025-07-11 15:18:10,436 [INFO] logging in using static token
2025-07-11 15:18:11,193 [INFO] Shard ID None has connected to Gateway (Session ID: 2f67913087589f49ab6155ce8f967a50).
2025-07-11 15:18:13,208 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:18:14,749 [INFO] Synced 25 slash commands.
2025-07-11 15:19:48,279 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:19:48,280 [INFO] Loaded cog: embedgen.py
2025-07-11 15:19:48,282 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:19:48,284 [INFO] Loaded cog: giveaway.py
2025-07-11 15:19:48,287 [INFO] Loaded cog: moderation.py
2025-07-11 15:19:48,289 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:19:48,290 [INFO] Loaded cog: rosters.py
2025-07-11 15:19:48,292 [INFO] Loaded cog: shipgame.py
2025-07-11 15:19:48,293 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:19:48,294 [INFO] Loaded cog: voice.py
2025-07-11 15:19:48,299 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:19:48,300 [INFO] Loaded cog: welcome.py
2025-07-11 15:19:48,300 [INFO] logging in using static token
2025-07-11 15:19:49,119 [INFO] Shard ID None has connected to Gateway (Session ID: 7bb4579afc38888420357a9f0ffc506c).
2025-07-11 15:19:51,126 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:19:51,394 [INFO] Synced 25 slash commands.
2025-07-11 15:28:27,624 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:28:27,626 [INFO] Loaded cog: embedgen.py
2025-07-11 15:28:27,628 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:28:27,631 [INFO] Loaded cog: giveaway.py
2025-07-11 15:28:27,636 [INFO] Loaded cog: moderation.py
2025-07-11 15:28:27,637 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:28:27,639 [INFO] Loaded cog: rosters.py
2025-07-11 15:28:27,641 [INFO] Loaded cog: shipgame.py
2025-07-11 15:28:27,642 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:28:27,644 [INFO] Loaded cog: voice.py
2025-07-11 15:28:27,649 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:28:27,651 [INFO] Loaded cog: welcome.py
2025-07-11 15:28:27,651 [INFO] logging in using static token
2025-07-11 15:28:28,455 [INFO] Shard ID None has connected to Gateway (Session ID: 3e0519024cf10776b72df41f2d95f2e0).
2025-07-11 15:28:30,450 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:28:30,705 [INFO] Synced 26 slash commands.
2025-07-11 15:30:00,751 [ERROR] Unhandled exception in internal background task 'check_inactivity'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 232, in check_inactivity
    f"{founder_role.mention} User {member.mention} has not joined a voice channel in 1 minute (testing - normally 7 days)."
       ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Object' object has no attribute 'mention'
2025-07-11 15:30:24,172 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:30:24,174 [INFO] Loaded cog: embedgen.py
2025-07-11 15:30:24,175 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:30:24,177 [INFO] Loaded cog: giveaway.py
2025-07-11 15:30:24,180 [INFO] Loaded cog: moderation.py
2025-07-11 15:30:24,182 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:30:24,184 [INFO] Loaded cog: rosters.py
2025-07-11 15:30:24,185 [INFO] Loaded cog: shipgame.py
2025-07-11 15:30:24,186 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:30:24,187 [INFO] Loaded cog: voice.py
2025-07-11 15:30:24,193 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:30:24,194 [INFO] Loaded cog: welcome.py
2025-07-11 15:30:24,194 [INFO] logging in using static token
2025-07-11 15:30:32,432 [INFO] Shard ID None has connected to Gateway (Session ID: 92781a5bb8e22b7c279c33971f39a5ff).
2025-07-11 15:30:34,459 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:30:34,730 [INFO] Synced 26 slash commands.
2025-07-11 15:30:34,743 [ERROR] Unhandled exception in internal background task 'check_inactivity'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 278, in check_inactivity
    f"{founder_role.mention} User {member.mention} has been inactive for 1.5 minutes (testing - normally 14 days). Assigning Reserves role."
       ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Object' object has no attribute 'mention'
2025-07-11 15:31:49,547 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:31:49,548 [INFO] Loaded cog: embedgen.py
2025-07-11 15:31:49,550 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:31:49,553 [INFO] Loaded cog: giveaway.py
2025-07-11 15:31:49,556 [INFO] Loaded cog: moderation.py
2025-07-11 15:31:49,557 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:31:49,559 [INFO] Loaded cog: rosters.py
2025-07-11 15:31:49,560 [INFO] Loaded cog: shipgame.py
2025-07-11 15:31:49,561 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:31:49,563 [INFO] Loaded cog: voice.py
2025-07-11 15:31:49,569 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:31:49,570 [INFO] Loaded cog: welcome.py
2025-07-11 15:31:49,570 [INFO] logging in using static token
2025-07-11 15:31:50,410 [INFO] Shard ID None has connected to Gateway (Session ID: eb5d2caa279a295137bfb75845cc62ac).
2025-07-11 15:31:52,445 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:31:52,701 [INFO] Synced 26 slash commands.
2025-07-11 15:34:12,865 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:34:12,867 [INFO] Loaded cog: embedgen.py
2025-07-11 15:34:12,868 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:34:12,871 [INFO] Loaded cog: giveaway.py
2025-07-11 15:34:12,875 [INFO] Loaded cog: moderation.py
2025-07-11 15:34:12,876 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:34:12,878 [INFO] Loaded cog: rosters.py
2025-07-11 15:34:12,879 [INFO] Loaded cog: shipgame.py
2025-07-11 15:34:12,880 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:34:12,881 [INFO] Loaded cog: voice.py
2025-07-11 15:34:12,886 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:34:12,887 [INFO] Loaded cog: welcome.py
2025-07-11 15:34:12,887 [INFO] logging in using static token
2025-07-11 15:34:13,934 [INFO] Shard ID None has connected to Gateway (Session ID: 6672a0836782be4395f7d4196efac893).
2025-07-11 15:34:15,945 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:34:16,139 [INFO] Synced 26 slash commands.
2025-07-11 15:35:58,765 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:35:58,766 [INFO] Loaded cog: embedgen.py
2025-07-11 15:35:58,768 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:35:58,771 [INFO] Loaded cog: giveaway.py
2025-07-11 15:35:58,774 [INFO] Loaded cog: moderation.py
2025-07-11 15:35:58,776 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:35:58,778 [INFO] Loaded cog: rosters.py
2025-07-11 15:35:58,779 [INFO] Loaded cog: shipgame.py
2025-07-11 15:35:58,780 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:35:58,781 [INFO] Loaded cog: voice.py
2025-07-11 15:35:58,786 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:35:58,787 [INFO] Loaded cog: welcome.py
2025-07-11 15:35:58,787 [INFO] logging in using static token
2025-07-11 15:35:59,523 [INFO] Shard ID None has connected to Gateway (Session ID: dbaea9100ca5e19f3f2930c8edd715dd).
2025-07-11 15:36:01,542 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:36:01,819 [INFO] Synced 26 slash commands.
2025-07-11 15:36:31,370 [ERROR] Error in on_voice_state_update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 92, in on_voice_state_update
    await self.remove_inactive_role(member)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 74, in remove_inactive_role
    self.save_data()
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 36, in save_data
    json.dump(self.voice_times, f, indent=4)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type set is not JSON serializable

2025-07-11 15:36:34,696 [ERROR] Error in on_voice_state_update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 92, in on_voice_state_update
    await self.remove_inactive_role(member)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 74, in remove_inactive_role
    self.save_data()
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 36, in save_data
    json.dump(self.voice_times, f, indent=4)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 432, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 406, in _iterencode_dict
    yield from chunks
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 439, in _iterencode
    o = _default(o)
        ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type set is not JSON serializable

2025-07-11 15:38:41,871 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:38:41,873 [INFO] Loaded cog: embedgen.py
2025-07-11 15:38:41,874 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:38:41,876 [INFO] Loaded cog: giveaway.py
2025-07-11 15:38:41,880 [INFO] Loaded cog: moderation.py
2025-07-11 15:38:41,882 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:38:41,883 [INFO] Loaded cog: rosters.py
2025-07-11 15:38:41,885 [INFO] Loaded cog: shipgame.py
2025-07-11 15:38:41,886 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:38:41,888 [INFO] Loaded cog: voice.py
2025-07-11 15:38:41,893 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:38:41,894 [INFO] Loaded cog: welcome.py
2025-07-11 15:38:41,894 [INFO] logging in using static token
2025-07-11 15:38:42,850 [INFO] Shard ID None has connected to Gateway (Session ID: c7e3c63674b694a7fdcf84e809083f5b).
2025-07-11 15:38:44,856 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:38:45,046 [INFO] Synced 26 slash commands.
2025-07-11 15:43:01,836 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:43:01,838 [INFO] Loaded cog: embedgen.py
2025-07-11 15:43:01,839 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:43:01,842 [INFO] Loaded cog: giveaway.py
2025-07-11 15:43:01,844 [INFO] Loaded cog: moderation.py
2025-07-11 15:43:01,845 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:43:01,848 [INFO] Loaded cog: rosters.py
2025-07-11 15:43:01,849 [INFO] Loaded cog: shipgame.py
2025-07-11 15:43:01,850 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:43:01,851 [INFO] Loaded cog: voice.py
2025-07-11 15:43:01,856 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:43:01,857 [INFO] Loaded cog: welcome.py
2025-07-11 15:43:01,857 [INFO] logging in using static token
2025-07-11 15:43:02,658 [INFO] Shard ID None has connected to Gateway (Session ID: 9ca13ed199d95e52ee4bfcccd198a05d).
2025-07-11 15:43:04,658 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:43:04,865 [INFO] Synced 26 slash commands.
2025-07-11 15:43:12,430 [ERROR] Ignoring exception in command 'clear'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\moderation.py", line 372, in clear
    await log_moderation_action(
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\utils\logging.py", line 20, in log_moderation_action
    embed.add_field(name="Target", value=f"{target.mention} ({target.name}#{target.discriminator})\nID: {target.id}", inline=False)
                                            ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'mention'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'clear' raised an exception: AttributeError: 'NoneType' object has no attribute 'mention'
2025-07-11 15:43:48,162 [ERROR] Ignoring exception in command 'clear'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\moderation.py", line 372, in clear
    await log_moderation_action(
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\utils\logging.py", line 20, in log_moderation_action
    embed.add_field(name="Target", value=f"{target.mention} ({target.name}#{target.discriminator})\nID: {target.id}", inline=False)
                                            ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'mention'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'clear' raised an exception: AttributeError: 'NoneType' object has no attribute 'mention'
2025-07-11 15:44:20,445 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:44:20,447 [INFO] Loaded cog: embedgen.py
2025-07-11 15:44:20,448 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:44:20,451 [INFO] Loaded cog: giveaway.py
2025-07-11 15:44:20,454 [INFO] Loaded cog: moderation.py
2025-07-11 15:44:20,455 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:44:20,456 [INFO] Loaded cog: rosters.py
2025-07-11 15:44:20,458 [INFO] Loaded cog: shipgame.py
2025-07-11 15:44:20,459 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:44:20,460 [INFO] Loaded cog: voice.py
2025-07-11 15:44:20,470 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:44:20,471 [INFO] Loaded cog: welcome.py
2025-07-11 15:44:20,471 [INFO] logging in using static token
2025-07-11 15:44:21,222 [INFO] Shard ID None has connected to Gateway (Session ID: 9800580e454483ce7eddef0f2bfdb28f).
2025-07-11 15:44:23,249 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:44:24,442 [INFO] Synced 26 slash commands.
2025-07-11 15:48:06,906 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:48:06,908 [INFO] Loaded cog: embedgen.py
2025-07-11 15:48:06,909 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:48:06,911 [INFO] Loaded cog: giveaway.py
2025-07-11 15:48:06,915 [INFO] Loaded cog: moderation.py
2025-07-11 15:48:06,916 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:48:06,918 [INFO] Loaded cog: rosters.py
2025-07-11 15:48:06,919 [INFO] Loaded cog: shipgame.py
2025-07-11 15:48:06,920 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:48:06,921 [INFO] Loaded cog: voice.py
2025-07-11 15:48:06,928 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:48:06,929 [INFO] Loaded cog: welcome.py
2025-07-11 15:48:06,929 [INFO] logging in using static token
2025-07-11 15:48:07,801 [INFO] Shard ID None has connected to Gateway (Session ID: 4037668911378c91de60673b8507ace5).
2025-07-11 15:48:09,823 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:48:10,130 [INFO] Synced 26 slash commands.
2025-07-11 15:48:19,008 [ERROR] Ignoring exception in command 'clear'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\moderation.py", line 372, in clear
    await log_moderation_action(
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\utils\logging.py", line 20, in log_moderation_action
    embed.add_field(name="Target", value=f"{target.mention} ({target.name}#{target.discriminator})\nID: {target.id}", inline=False)
                                            ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'mention'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'clear' raised an exception: AttributeError: 'NoneType' object has no attribute 'mention'
2025-07-11 15:48:35,361 [ERROR] Ignoring exception in command 'clear'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\moderation.py", line 372, in clear
    await log_moderation_action(
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\utils\logging.py", line 20, in log_moderation_action
    embed.add_field(name="Target", value=f"{target.mention} ({target.name}#{target.discriminator})\nID: {target.id}", inline=False)
                                            ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'mention'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'clear' raised an exception: AttributeError: 'NoneType' object has no attribute 'mention'
2025-07-11 15:52:34,414 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:52:34,416 [INFO] Loaded cog: embedgen.py
2025-07-11 15:52:34,417 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:52:34,420 [INFO] Loaded cog: giveaway.py
2025-07-11 15:52:34,423 [INFO] Loaded cog: moderation.py
2025-07-11 15:52:34,424 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:52:34,426 [INFO] Loaded cog: rosters.py
2025-07-11 15:52:34,427 [INFO] Loaded cog: shipgame.py
2025-07-11 15:52:34,428 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:52:34,429 [INFO] Loaded cog: voice.py
2025-07-11 15:52:34,436 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:52:34,436 [INFO] Loaded cog: welcome.py
2025-07-11 15:52:34,437 [INFO] logging in using static token
2025-07-11 15:52:35,116 [INFO] Shard ID None has connected to Gateway (Session ID: d19293e7245370a53972b7f004fdc3ce).
2025-07-11 15:52:37,132 [INFO] Logged in as Aegis Nox Bot
2025-07-11 15:52:37,367 [INFO] Synced 26 slash commands.
2025-07-11 15:54:53,203 [INFO] Loaded cog: applytoaegis.py
2025-07-11 15:54:53,205 [INFO] Loaded cog: embedgen.py
2025-07-11 15:54:53,206 [INFO] Loaded cog: fleetdata.py
2025-07-11 15:54:53,209 [INFO] Loaded cog: giveaway.py
2025-07-11 15:54:53,213 [INFO] Loaded cog: moderation.py
2025-07-11 15:54:53,214 [INFO] Loaded cog: rolerequest.py
2025-07-11 15:54:53,215 [INFO] Loaded cog: rosters.py
2025-07-11 15:54:53,217 [INFO] Loaded cog: shipgame.py
2025-07-11 15:54:53,218 [INFO] Loaded cog: storagetracker.py
2025-07-11 15:54:53,219 [INFO] Loaded cog: voice.py
2025-07-11 15:54:53,229 [INFO] Loaded cog: voicetracker.py
2025-07-11 15:54:53,230 [INFO] Loaded cog: welcome.py
2025-07-11 15:54:53,230 [INFO] logging in using static token
2025-07-11 15:54:53,796 [ERROR] Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001DABC3441D0>
2025-07-11 16:00:23,224 [INFO] Loaded cog: applytoaegis.py
2025-07-11 16:00:23,225 [INFO] Loaded cog: embedgen.py
2025-07-11 16:00:23,226 [INFO] Loaded cog: fleetdata.py
2025-07-11 16:00:23,229 [INFO] Loaded cog: giveaway.py
2025-07-11 16:00:23,232 [INFO] Loaded cog: moderation.py
2025-07-11 16:00:23,233 [INFO] Loaded cog: rolerequest.py
2025-07-11 16:00:23,235 [INFO] Loaded cog: rosters.py
2025-07-11 16:00:23,236 [INFO] Loaded cog: shipgame.py
2025-07-11 16:00:23,237 [INFO] Loaded cog: storagetracker.py
2025-07-11 16:00:23,238 [INFO] Loaded cog: voice.py
2025-07-11 16:00:23,244 [INFO] Loaded cog: voicetracker.py
2025-07-11 16:00:23,245 [INFO] Loaded cog: welcome.py
2025-07-11 16:00:23,245 [INFO] logging in using static token
2025-07-11 16:00:24,151 [INFO] Shard ID None has connected to Gateway (Session ID: 7956489b459ef68f5256935ef03dc27b).
2025-07-11 16:00:26,167 [INFO] Logged in as Aegis Nox Bot
2025-07-11 16:00:26,427 [INFO] Synced 26 slash commands.
2025-07-11 16:04:33,555 [INFO] Loaded cog: applytoaegis.py
2025-07-11 16:04:33,556 [INFO] Loaded cog: embedgen.py
2025-07-11 16:04:33,558 [INFO] Loaded cog: fleetdata.py
2025-07-11 16:04:33,560 [INFO] Loaded cog: giveaway.py
2025-07-11 16:04:33,563 [INFO] Loaded cog: moderation.py
2025-07-11 16:04:33,564 [INFO] Loaded cog: rolerequest.py
2025-07-11 16:04:33,566 [INFO] Loaded cog: rosters.py
2025-07-11 16:04:33,567 [INFO] Loaded cog: shipgame.py
2025-07-11 16:04:33,568 [INFO] Loaded cog: storagetracker.py
2025-07-11 16:04:33,570 [INFO] Loaded cog: voice.py
2025-07-11 16:04:33,575 [INFO] Loaded cog: voicetracker.py
2025-07-11 16:04:33,576 [INFO] Loaded cog: welcome.py
2025-07-11 16:04:33,576 [INFO] logging in using static token
2025-07-11 16:04:36,032 [INFO] Shard ID None has connected to Gateway (Session ID: 6ebcea010f0f182b4925179516263577).
2025-07-11 16:04:38,037 [INFO] Logged in as Aegis Nox Bot
2025-07-11 16:04:38,260 [INFO] Synced 26 slash commands.
2025-07-11 16:05:32,174 [INFO] Loaded cog: applytoaegis.py
2025-07-11 16:05:32,176 [INFO] Loaded cog: embedgen.py
2025-07-11 16:05:32,177 [INFO] Loaded cog: fleetdata.py
2025-07-11 16:05:32,179 [INFO] Loaded cog: giveaway.py
2025-07-11 16:05:32,182 [INFO] Loaded cog: moderation.py
2025-07-11 16:05:32,182 [INFO] Loaded cog: rolerequest.py
2025-07-11 16:05:32,185 [INFO] Loaded cog: rosters.py
2025-07-11 16:05:32,186 [INFO] Loaded cog: shipgame.py
2025-07-11 16:05:32,187 [INFO] Loaded cog: storagetracker.py
2025-07-11 16:05:32,188 [INFO] Loaded cog: voice.py
2025-07-11 16:05:32,194 [INFO] Loaded cog: voicetracker.py
2025-07-11 16:05:32,195 [INFO] Loaded cog: welcome.py
2025-07-11 16:05:32,195 [INFO] logging in using static token
2025-07-11 16:05:32,939 [INFO] Shard ID None has connected to Gateway (Session ID: a51103941084236a080e5a038fb91dbf).
2025-07-11 16:05:34,975 [INFO] Logged in as Aegis Nox Bot
2025-07-11 16:05:35,291 [INFO] Synced 26 slash commands.
2025-07-11 16:07:43,776 [INFO] Loaded cog: applytoaegis.py
2025-07-11 16:07:43,777 [INFO] Loaded cog: embedgen.py
2025-07-11 16:07:43,779 [INFO] Loaded cog: fleetdata.py
2025-07-11 16:07:43,781 [INFO] Loaded cog: giveaway.py
2025-07-11 16:07:43,784 [INFO] Loaded cog: moderation.py
2025-07-11 16:07:43,786 [INFO] Loaded cog: rolerequest.py
2025-07-11 16:07:43,787 [INFO] Loaded cog: rosters.py
2025-07-11 16:07:43,788 [INFO] Loaded cog: shipgame.py
2025-07-11 16:07:43,789 [INFO] Loaded cog: storagetracker.py
2025-07-11 16:07:43,790 [INFO] Loaded cog: voice.py
2025-07-11 16:07:43,798 [INFO] Loaded cog: voicetracker.py
2025-07-11 16:07:43,798 [INFO] Loaded cog: welcome.py
2025-07-11 16:07:43,798 [INFO] logging in using static token
2025-07-11 16:07:44,512 [INFO] Shard ID None has connected to Gateway (Session ID: 407c6a678eabc29492d63e0ea48f8c01).
2025-07-11 16:07:46,517 [INFO] Logged in as Aegis Nox Bot
2025-07-11 16:07:46,827 [INFO] Synced 26 slash commands.
2025-07-11 16:11:25,751 [ERROR] Error in on_message:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 189, in on_message
    await self.remove_inactive_role(message.author)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 124, in remove_inactive_role
    inactive_role = member.guild.get_role(1393309592166858793)
                    ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'guild'

2025-07-11 16:11:28,966 [ERROR] Error in on_message:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 189, in on_message
    await self.remove_inactive_role(message.author)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 124, in remove_inactive_role
    inactive_role = member.guild.get_role(1393309592166858793)
                    ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'guild'

2025-07-11 16:11:32,853 [ERROR] Error in on_message:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 189, in on_message
    await self.remove_inactive_role(message.author)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 124, in remove_inactive_role
    inactive_role = member.guild.get_role(1393309592166858793)
                    ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'guild'

2025-07-11 16:12:19,222 [ERROR] Error in on_message:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 189, in on_message
    await self.remove_inactive_role(message.author)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 124, in remove_inactive_role
    inactive_role = member.guild.get_role(1393309592166858793)
                    ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'guild'

2025-07-11 16:13:01,896 [ERROR] Error in on_message:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 189, in on_message
    await self.remove_inactive_role(message.author)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 124, in remove_inactive_role
    inactive_role = member.guild.get_role(1393309592166858793)
                    ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'guild'

2025-07-11 16:13:12,499 [ERROR] Error in on_message:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 189, in on_message
    await self.remove_inactive_role(message.author)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 124, in remove_inactive_role
    inactive_role = member.guild.get_role(1393309592166858793)
                    ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'guild'

2025-07-11 16:13:22,228 [ERROR] Error in on_message:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 189, in on_message
    await self.remove_inactive_role(message.author)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\voicetracker.py", line 124, in remove_inactive_role
    inactive_role = member.guild.get_role(1393309592166858793)
                    ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'guild'

2025-07-11 16:13:45,845 [INFO] Loaded cog: applytoaegis.py
2025-07-11 16:13:45,847 [INFO] Loaded cog: embedgen.py
2025-07-11 16:13:45,848 [INFO] Loaded cog: fleetdata.py
2025-07-11 16:13:45,851 [INFO] Loaded cog: giveaway.py
2025-07-11 16:13:45,854 [INFO] Loaded cog: moderation.py
2025-07-11 16:13:45,855 [INFO] Loaded cog: rolerequest.py
2025-07-11 16:13:45,856 [INFO] Loaded cog: rosters.py
2025-07-11 16:13:45,858 [INFO] Loaded cog: shipgame.py
2025-07-11 16:13:45,859 [INFO] Loaded cog: storagetracker.py
2025-07-11 16:13:45,861 [INFO] Loaded cog: voice.py
2025-07-11 16:13:45,867 [INFO] Loaded cog: voicetracker.py
2025-07-11 16:13:45,868 [INFO] Loaded cog: welcome.py
2025-07-11 16:13:45,868 [INFO] logging in using static token
2025-07-11 16:13:46,686 [INFO] Shard ID None has connected to Gateway (Session ID: cd5bfe6a8e610ce44f0e8d9f0d985de6).
2025-07-11 16:13:48,705 [INFO] Logged in as Aegis Nox Bot
2025-07-11 16:13:48,916 [INFO] Synced 26 slash commands.
2025-07-11 18:28:15,162 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-11 21:35:37,122 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-11 22:50:35,548 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 00:40:35,481 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 01:17:00,351 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 05:09:33,426 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 08:21:22,572 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 09:55:16,783 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 12:44:06,519 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 14:07:33,364 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 17:14:55,918 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 18:02:11,055 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 19:51:32,838 [ERROR] Ignoring exception in view <ShipAttackView timeout=None children=4> for item <Button style=<ButtonStyle.danger: 4> url=None disabled=False label='Shoot 4' emoji=None row=None>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\view.py", line 427, in _scheduled_task
    await item.callback(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\shipgame.py", line 88, in shoot_callback
    await interaction.response.send_message(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-12 20:29:15,474 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 21:35:49,254 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-12 22:37:24,812 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-13 01:27:18,165 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-13 03:23:18,543 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-13 05:05:06,461 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-13 08:10:06,653 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-13 10:18:18,265 [ERROR] Ignoring exception in command 'discordfleetleaderboard'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\shipgame.py", line 237, in discord_fleet_leaderboard
    await interaction.response.send_message(embed=embed, ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'discordfleetleaderboard' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 10:18:45,145 [ERROR] Ignoring exception in command 'discordfleetleaderboard'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\shipgame.py", line 237, in discord_fleet_leaderboard
    await interaction.response.send_message(embed=embed, ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'discordfleetleaderboard' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 10:31:22,088 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-13 13:13:05,875 [INFO] Shard ID None has successfully RESUMED session cd5bfe6a8e610ce44f0e8d9f0d985de6.
2025-07-13 13:59:50,995 [ERROR] Ignoring exception in command 'discordfleetleaderboard'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\shipgame.py", line 237, in discord_fleet_leaderboard
    await interaction.response.send_message(embed=embed, ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'discordfleetleaderboard' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-13 14:08:50,092 [ERROR] Ignoring exception in command 'discordfleetleaderboard'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\shipgame.py", line 237, in discord_fleet_leaderboard
    await interaction.response.send_message(embed=embed, ephemeral=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 801, in send_message
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'discordfleetleaderboard' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
