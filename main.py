import os
import discord
from discord.ext import commands
from dotenv import load_dotenv # type: ignore
import logging
import traceback
from datetime import datetime

# Configure logging
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(f'{log_dir}/bot_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)

load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')

if not TOKEN:
    logging.critical("No Discord token found in .env file!")
    raise ValueError("Discord token not found")

intents = discord.Intents.default()
intents.members = True
intents.message_content = True 

bot = commands.Bot(command_prefix='!', intents=intents)

@bot.event
async def on_ready():
    logging.info(f'Logged in as {bot.user.name}')
    try:
        # Try guild sync first for faster updates (replace with your guild ID)
        # guild = discord.Object(id=YOUR_GUILD_ID)  # Uncomment and add your guild ID for faster sync
        # synced = await bot.tree.sync(guild=guild)
        # logging.info(f'Synced {len(synced)} slash commands to guild.')

        # Global sync
        synced = await bot.tree.sync()
        logging.info(f'Synced {len(synced)} slash commands globally.')

        # Print all registered commands for debugging
        all_commands = bot.tree.get_commands()
        logging.info(f'Registered commands: {[cmd.name for cmd in all_commands]}')

    except Exception as e:
        logging.error(f'Error syncing slash commands: {traceback.format_exc()}')

@bot.event
async def on_error(event, *args, **kwargs):
    logging.error(f'Error in {event}:\n{traceback.format_exc()}')

@bot.event
async def on_command_error(ctx, error):
    logging.error(f'Command error: {error}\n{traceback.format_exc()}')

async def load_cogs():
    for filename in os.listdir('./cogs'):
        if filename.endswith('.py'):
            try:
                await bot.load_extension(f'cogs.{filename[:-3]}')
                logging.info(f'Loaded cog: {filename}')
            except Exception as e:
                logging.error(f'Failed to load cog {filename}: {traceback.format_exc()}')

if __name__ == '__main__':
    import asyncio
    async def main():
        try:
            await load_cogs()
            await bot.start(TOKEN)
        except Exception as e:
            logging.critical(f'Fatal error: {traceback.format_exc()}')
            raise
    asyncio.run(main()) 