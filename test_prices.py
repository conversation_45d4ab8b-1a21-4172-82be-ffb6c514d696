#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append('.')

from cogs.uex_trade import UEXTrade

async def test_prices():
    """Test commodity prices to understand the investment calculation issue"""
    
    # Create UEX trade cog instance
    cog = UEXTrade(None)
    
    try:
        # Refresh cache
        await cog._ensure_cache_fresh()
        
        print("=== COMMODITY PRICES TEST ===")
        print(f"Commodities cache: {len(cog.commodities_cache)} entries")
        print(f"Commodity prices cache: {len(cog.commodity_prices_cache)} entries")
        
        # Check BIOP and ETAM prices
        biop_found = False
        etam_found = False
        
        for commodity_id, commodity in cog.commodities_cache.items():
            name = commodity.get('name', '').lower()
            if 'bioplastic' in name or name == 'biop':
                biop_found = True
                print(f"\n=== BIOPLASTIC ===")
                print(f"ID: {commodity_id}")
                print(f"Name: {commodity.get('name')}")
                print(f"Average Buy Price: {commodity.get('price_buy', 0)}")
                print(f"Average Sell Price: {commodity.get('price_sell', 0)}")
                
                # Check specific location prices
                if commodity_id in cog.commodity_prices_cache:
                    prices = cog.commodity_prices_cache[commodity_id]
                    print(f"Location-specific prices: {len(prices)} locations")
                    
                    # Show first few buy/sell prices
                    buy_prices = [p.get('price_buy', 0) for p in prices if p.get('price_buy', 0) > 0]
                    sell_prices = [p.get('price_sell', 0) for p in prices if p.get('price_sell', 0) > 0]
                    
                    if buy_prices:
                        print(f"Buy prices range: {min(buy_prices)} - {max(buy_prices)}")
                        print(f"Sample buy prices: {buy_prices[:5]}")
                    if sell_prices:
                        print(f"Sell prices range: {min(sell_prices)} - {max(sell_prices)}")
                        print(f"Sample sell prices: {sell_prices[:5]}")
                        
            elif 'etam' in name.lower() or name == 'etam':
                etam_found = True
                print(f"\n=== ETAM ===")
                print(f"ID: {commodity_id}")
                print(f"Name: {commodity.get('name')}")
                print(f"Average Buy Price: {commodity.get('price_buy', 0)}")
                print(f"Average Sell Price: {commodity.get('price_sell', 0)}")
                
                # Check specific location prices
                if commodity_id in cog.commodity_prices_cache:
                    prices = cog.commodity_prices_cache[commodity_id]
                    print(f"Location-specific prices: {len(prices)} locations")
                    
                    # Show first few buy/sell prices
                    buy_prices = [p.get('price_buy', 0) for p in prices if p.get('price_buy', 0) > 0]
                    sell_prices = [p.get('price_sell', 0) for p in prices if p.get('price_sell', 0) > 0]
                    
                    if buy_prices:
                        print(f"Buy prices range: {min(buy_prices)} - {max(buy_prices)}")
                        print(f"Sample buy prices: {buy_prices[:5]}")
                    if sell_prices:
                        print(f"Sell prices range: {min(sell_prices)} - {max(sell_prices)}")
                        print(f"Sample sell prices: {sell_prices[:5]}")
        
        if not biop_found:
            print("\n❌ BIOPLASTIC not found in commodities cache")
        if not etam_found:
            print("\n❌ ETAM not found in commodities cache")
            
        # Test investment calculation
        print(f"\n=== INVESTMENT CALCULATION TEST ===")
        investment = 2000000  # 2M aUEC
        vehicle_scu = 381     # 890 Jump capacity
        
        print(f"Investment: {investment:,} aUEC")
        print(f"Vehicle capacity: {vehicle_scu} SCU")
        
        # Test with different price points
        test_prices = [290, 5242, 7143]  # BIOP website, BIOP logs, ETAM logs
        
        for price in test_prices:
            max_scu_by_investment = int(investment / price)
            final_scu = min(vehicle_scu, max_scu_by_investment)
            actual_investment = price * final_scu
            
            print(f"\nPrice {price} aUEC/SCU:")
            print(f"  Max SCU by investment: {max_scu_by_investment}")
            print(f"  Final SCU (limited by vehicle): {final_scu}")
            print(f"  Actual investment: {actual_investment:,} aUEC")
            print(f"  Within limit: {actual_investment <= investment}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        if hasattr(cog, 'uex_client'):
            await cog.uex_client.close()

if __name__ == "__main__":
    asyncio.run(test_prices())
