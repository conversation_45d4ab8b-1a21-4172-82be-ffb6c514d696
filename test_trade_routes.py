#!/usr/bin/env python3
"""
Test script to verify trade routes calculation logic
"""
import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path so we can import cogs
sys.path.append('.')

from cogs.uex_trade import UEXTrade

class MockBot:
    """Mock bot for testing"""
    pass

async def test_trade_routes():
    """Test the trade routes calculation"""
    print("Testing UEX Trade Routes Calculation...")
    
    # Create mock bot and cog
    bot = MockBot()
    cog = UEXTrade(bot)
    
    # Initialize the cache
    print("Refreshing cache...")
    await cog._refresh_cache()
    
    # Test parameters matching the user's example
    vehicle_scu = 388  # 890 Jump SCU capacity
    investment_limit = 1000000  # 1M aUEC to match the expected ETAM calculation

    print(f"\nTesting with vehicle SCU: {vehicle_scu}, investment limit: {investment_limit}")
    
    # Calculate trade routes
    routes = await cog._calculate_trade_routes(
        vehicle_scu=vehicle_scu,
        investment_limit=investment_limit
    )
    
    print(f"\nGenerated {len(routes)} routes")
    
    if routes:
        print("\nTop 10 routes:")
        for i, route in enumerate(routes[:10], 1):
            commodity = route['commodity_name']
            origin = route['origin_terminal_name']
            destination = route['destination_terminal_name']
            profit = route['profit']
            investment = route['investment']
            scu = route['scu']
            buy_price = route['price_buy']
            sell_price = route['price_sell']
            roi = route['price_roi']
            
            print(f"{i:2d}. {commodity}")
            print(f"    {origin} -> {destination}")
            print(f"    Buy: {buy_price:,.0f} aUEC/SCU, Sell: {sell_price:,.0f} aUEC/SCU")
            print(f"    SCU: {scu}, Investment: {investment:,.0f} aUEC, Profit: {profit:,.0f} aUEC")
            print(f"    ROI: {roi:.1f}%")
            print()
        
        # Check specifically for E'tam (ETAM)
        etam_routes = [r for r in routes if "tam" in r['commodity_name'].lower()]
        if etam_routes:
            print(f"\nE'tam routes found: {len(etam_routes)}")

            # Look for the specific route: The Golden Riviera -> Samson Son
            samson_routes = [r for r in etam_routes if "samson" in r['destination_terminal_name'].lower()]
            if samson_routes:
                print("\nE'tam route to Samson Son found:")
                etam = samson_routes[0]
                print(f"  {etam['origin_terminal_name']} -> {etam['destination_terminal_name']}")
                print(f"  Buy: {etam['price_buy']:,.0f} aUEC/SCU, Sell: {etam['price_sell']:,.0f} aUEC/SCU")
                print(f"  SCU: {etam['scu']}, Investment: {etam['investment']:,.0f} aUEC, Profit: {etam['profit']:,.0f} aUEC")
                print(f"  ROI: {etam['price_roi']:.1f}%")

                # Check if it matches expected values
                expected_scu = 180
                expected_investment = 1000000
                expected_profit = 953000

                if (abs(etam['scu'] - expected_scu) <= 5 and
                    abs(etam['investment'] - expected_investment) <= 50000 and
                    abs(etam['profit'] - expected_profit) <= 50000):
                    print("  ✅ E'tam to Samson Son matches expected UEX website values!")
                else:
                    print("  ❌ E'tam to Samson Son doesn't match expected values:")
                    print(f"     Expected: {expected_scu} SCU, {expected_investment:,} investment, {expected_profit:,} profit")
                    print(f"     Actual:   {etam['scu']} SCU, {etam['investment']:,} investment, {etam['profit']:,} profit")
            else:
                print("\n❌ No E'tam route to Samson Son found!")
                print("Available E'tam destinations:")
                for route in etam_routes[:5]:
                    print(f"  {route['origin_terminal_name']} -> {route['destination_terminal_name']}")

            print("\nTop 5 E'tam routes:")
            for i, route in enumerate(etam_routes[:5], 1):
                print(f"  {i}. {route['origin_terminal_name']} -> {route['destination_terminal_name']}")
                print(f"     Buy: {route['price_buy']:,.0f}, Sell: {route['price_sell']:,.0f}")
                print(f"     SCU: {route['scu']}, Investment: {route['investment']:,.0f}, Profit: {route['profit']:,.0f}")

            # Check all unique E'tam buy locations and prices
            print("\nAll E'tam buy locations and prices:")
            buy_locations = {}
            for route in etam_routes:
                origin = route['origin_terminal_name']
                buy_price = route['price_buy']
                if origin not in buy_locations:
                    buy_locations[origin] = buy_price

            for location, price in sorted(buy_locations.items(), key=lambda x: x[1]):
                scu_for_1m = int(1000000 / price)
                print(f"  {location}: {price:,.0f} aUEC/SCU (1M = {scu_for_1m} SCU)")
        else:
            print("\n❌ No E'tam routes found!")
    
    # Test what the /price command would show for E'tam
    print("\n" + "="*60)
    print("TESTING /price COMMAND DATA FOR E'TAM")
    print("="*60)

    # Find E'tam commodity
    etam_commodity = None
    for commodity in cog.commodities_cache.values():
        if "tam" in commodity['name'].lower():
            etam_commodity = commodity
            break

    if etam_commodity:
        commodity_id = etam_commodity['id']
        commodity_name = etam_commodity['name']
        print(f"Found commodity: {commodity_name} (ID: {commodity_id})")

        # Get price data (same as /price command uses)
        prices = cog.commodity_prices_cache.get(commodity_id, [])
        if prices:
            print(f"\nPrice data entries: {len(prices)}")

            # Show buy locations (sorted by price, lowest first)
            buy_locations = [p for p in prices if p.get('price_buy', 0) > 0 and p.get('status_buy', 0) > 0]
            buy_locations = sorted(buy_locations, key=lambda x: x.get('price_buy', 0))

            print(f"\nBuy locations ({len(buy_locations)} found):")
            for i, loc in enumerate(buy_locations[:10], 1):
                terminal_name = cog._get_terminal_name(loc.get('id_terminal', 0))
                price = loc.get('price_buy', 0)
                inventory = loc.get('inventory', 'N/A')
                print(f"  {i:2d}. {terminal_name}: {price:,.0f} aUEC/SCU (inventory: {inventory})")

            # Show sell locations (sorted by price, highest first)
            sell_locations = [p for p in prices if p.get('price_sell', 0) > 0 and p.get('status_sell', 0) > 0]
            sell_locations = sorted(sell_locations, key=lambda x: x.get('price_sell', 0), reverse=True)

            print(f"\nSell locations ({len(sell_locations)} found):")
            for i, loc in enumerate(sell_locations[:10], 1):
                terminal_name = cog._get_terminal_name(loc.get('id_terminal', 0))
                price = loc.get('price_sell', 0)
                inventory = loc.get('inventory', 'N/A')
                print(f"  {i:2d}. {terminal_name}: {price:,.0f} aUEC/SCU (inventory: {inventory})")

            # Check specifically for The Golden Riviera -> Samson Son route
            golden_riviera_buy = None
            samson_son_sell = None

            for loc in buy_locations:
                terminal_name = cog._get_terminal_name(loc.get('id_terminal', 0))
                if "golden riviera" in terminal_name.lower():
                    golden_riviera_buy = loc
                    break

            for loc in sell_locations:
                terminal_name = cog._get_terminal_name(loc.get('id_terminal', 0))
                if "samson" in terminal_name.lower():
                    samson_son_sell = loc
                    break

            if golden_riviera_buy and samson_son_sell:
                buy_price = golden_riviera_buy.get('price_buy', 0)
                sell_price = samson_son_sell.get('price_sell', 0)
                profit_per_scu = sell_price - buy_price

                print(f"\n🎯 GOLDEN RIVIERA -> SAMSON SON ROUTE:")
                print(f"   Buy Price: {buy_price:,.0f} aUEC/SCU")
                print(f"   Sell Price: {sell_price:,.0f} aUEC/SCU")
                print(f"   Profit per SCU: {profit_per_scu:,.0f} aUEC")

                # Calculate for different investment amounts
                for investment in [1000000, 2000000]:
                    max_scu = int(investment / buy_price)
                    total_investment = buy_price * max_scu
                    total_profit = profit_per_scu * max_scu
                    print(f"   With {investment/1000000:.0f}M investment: {max_scu} SCU, {total_investment:,.0f} invested, {total_profit:,.0f} profit")
        else:
            print("No price data found for E'tam")
    else:
        print("E'tam commodity not found")

    # Clean up
    await cog.uex_client.close()

if __name__ == "__main__":
    asyncio.run(test_trade_routes())
